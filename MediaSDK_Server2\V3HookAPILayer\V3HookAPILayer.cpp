#include "V3HookAPILayer.h"

#include "graphics_helper.h"

namespace v3
{
HookAPILayer::HookAPILayer()
    : video_frame_processor_(thread_cv_)
{
}

HookAPILayer::~HookAPILayer()
{
}

mediasdk::hook_api::VideoHookPoint* HookAPILayer::GetVideoHookPoint()
{
    return &video_hook_layer_;
}

mediasdk::hook_api::AudioHookPoint* HookAPILayer::GetAudioHookPoint()
{
    return &audio_hook_layer_;
}

void HookAPILayer::SetVideoFrameProcessorCallback(
    VideoFrameProcessorOnReadyCallback             on_ready,
    VideoFrameProcessorOnTickCallback              on_tick,
    VideoFrameProcessorOnCaptureVideoFrameCallback on_capture)
{
    std::unique_lock<std::mutex> lock(video_frame_process_callback_mutex_);
    video_frame_process_callback_.on_ready = on_ready;
    video_frame_process_callback_.on_tick = on_tick;
    video_frame_process_callback_.on_capture = on_capture;
}

AudioFrameProcessor* HookAPILayer::GetAudioFrameProcessor()
{
    return &audio_frame_processor_;
}

VideoFrameProcessor* HookAPILayer::GetVideoFrameProcessor()
{
    return &video_frame_processor_;
}

bool HookAPILayer::Init(const ReadyCallback& ready_cb)
{
    std::unique_lock<std::mutex> lock(api_mutex_);
    UninitNolock();

    init_ = true;
    stop_ = false;

    {
        std::unique_lock<std::mutex> cb_lock(ready_cb_mutex_);
        ready_cb_ = ready_cb;
    }

    thread_ = std::thread(std::bind(&HookAPILayer::WorkerThreadLoop, this));
    video_hook_layer_.Init(
        std::bind(&HookAPILayer::OnHookVideoAPILayerReady, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
        std::bind(&HookAPILayer::OnHookVideoAPILayerRenderLoopBegin, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3),
        std::bind(&HookAPILayer::OnHookVideoAPILayerRenderLoopEnd, this),
        std::bind(&HookAPILayer::OnHookVideoAPILayerCanvasItemOriginalTextureCallback, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&HookAPILayer::OnHookVideoAPILayerCanvasItemFilteredTextureCallback, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&HookAPILayer::OnHookVideoAPILayerPreviewTextureCallback, this, std::placeholders::_1, std::placeholders::_2));

    audio_hook_layer_.Init(
        std::bind(&HookAPILayer::OnHookAudioLayerAudioInputRawFrameCallback, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3),
        std::bind(&HookAPILayer::OnHookAudioLayerAudioInputFilteredFrameCallback, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3),
        std::bind(&HookAPILayer::OnHookAudioLayerMixedAudioFrameCallback, this, std::placeholders::_1, std::placeholders::_2));

    audio_frame_processor_.Init();

    return true;
}

void HookAPILayer::UninitNolock()
{
    if (!init_)
        return;

    stop_ = true;
    init_ = false;

    audio_hook_layer_.Uninit();
    audio_frame_processor_.Uninit();

    thread_cv_.notify_all();

    if (thread_.joinable())
    {
        thread_.join();
        thread_ = {};
    }

    video_hook_layer_.Uninit();
    video_frame_processor_.Uninit();

    dev_ = nullptr;
    ctx_ = nullptr;

    hook_video_api_layer_ready_ = false;

    {
        std::unique_lock<std::mutex> cb_lock(ready_cb_mutex_);
        ready_cb_ = nullptr;
    }
}

void HookAPILayer::Uninit()
{
    std::unique_lock<std::mutex> lock(api_mutex_);
    UninitNolock();
}

void HookAPILayer::OnHookVideoAPILayerReady(ID3D11Device* sdk_dev, ID3D11DeviceContext* sdk_ctx, ID3D11Device* dev, ID3D11DeviceContext* ctx)
{
    dev_ = dev;
    ctx_ = ctx;

    video_frame_processor_.Init(sdk_dev, sdk_ctx, dev, ctx);

    hook_video_api_layer_ready_ = true;
}

void HookAPILayer::OnHookVideoAPILayerCanvasItemOriginalTextureCallback(const std::string& canvas_item_id, ID3D11Texture2D* texture)
{
    video_frame_processor_.OnCanvasItemOriginalTexture(canvas_item_id, texture);
}

void HookAPILayer::OnHookVideoAPILayerCanvasItemFilteredTextureCallback(const std::string& canvas_item_id, ID3D11Texture2D* texture)
{
    video_frame_processor_.OnCanvasItemFilteredTexture(canvas_item_id, texture);
}

void HookAPILayer::OnHookVideoAPILayerPreviewTextureCallback(const int64_t sink_id, ID3D11Texture2D* texture)
{
    video_frame_processor_.OnPreviewTextureCallback(sink_id, texture);
}

void HookAPILayer::OnHookAudioLayerAudioInputRawFrameCallback(const std::string& audio_input_id, bool is_mute, const mediasdk::AudioSourceFrame& frame)
{
    audio_frame_processor_.OnAudioInputRawFrame(audio_input_id, is_mute, frame);
}

void HookAPILayer::OnHookAudioLayerAudioInputFilteredFrameCallback(const std::string& audio_input_id, bool is_mute, const mediasdk::AudioSourceFrame& frame)
{
    audio_frame_processor_.OnAudioInputFilteredFrame(audio_input_id, is_mute, frame);
}

void HookAPILayer::OnHookAudioLayerMixedAudioFrameCallback(uint32_t track_id, const mediasdk::AudioSourceFrame& frame)
{
    audio_frame_processor_.OnMixedAudioFrame(track_id, frame);
}

void HookAPILayer::OnHookVideoAPILayerRenderLoopBegin(const std::map<uint32_t, std::vector<std::string>>&    canvas_map,
                                                      const std::map<std::string, std::vector<std::string>>& canvas_item_map,
                                                      const std::map<std::string, std::string>&              canvas_item_map2)
{
    ++render_count_;
    video_frame_processor_.OnHookVideoAPILayerRenderLoopBegin(render_count_, canvas_map, canvas_item_map, canvas_item_map2);
}

void HookAPILayer::OnHookVideoAPILayerRenderLoopEnd()
{
    video_frame_processor_.OnHookVideoAPILayerRenderLoopEnd();
}

void HookAPILayer::WorkerThreadLoop()
{
    bool video_frame_process_notified = false;

    uint64_t tick_id = 0;

    while (!stop_)
    {
        do
        {
            if (!hook_video_api_layer_ready_)
                break;

            ++tick_id;

            video_frame_processor_.OnWorkerThreadLoopTick(tick_id);

            if (!video_frame_process_notified)
            {
                video_frame_process_notified = true;

                std::unique_lock<std::mutex> cb_lock(ready_cb_mutex_);
                if (ready_cb_)
                    ready_cb_();
            }

        } while (0);

        auto wakeup_tp = std::chrono::high_resolution_clock::now() + std::chrono::milliseconds(16);

        while (!stop_)
        {
            if (hook_video_api_layer_ready_)
                video_frame_processor_.OnRenderTaskTick();

            std::unique_lock<std::mutex> lock(thread_cv_mutex_);
            auto                         status = thread_cv_.wait_until(lock, wakeup_tp);
            if (status == std::cv_status::timeout)
                break;
            else if (status == std::cv_status::no_timeout)
                continue;
        }
    }
}

} // namespace v3