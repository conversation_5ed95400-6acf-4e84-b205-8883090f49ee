#include "VideoFrameProcessor.h"

namespace v3
{

static const char* kSendBufferGUID = "21bf6224-1947-45d4-a606-447c82e45e7e";
static const char* kAckBufferGUID = "defda1ca-bf34-45e4-bae4-f2b4c8d1ef26";

#pragma pack(push)
#pragma pack(4)

struct GrabFrameMsgSend
{
    int64_t task_id;
    char    frame_id[128];
    int32_t frame_id_len;
    int64_t shared_handle;
    int64_t frame_width;
    int64_t frame_height;

    DXGI_ADAPTER_DESC adapter_desc;
};

struct GrabFrameMsgAck
{
    int64_t task_id;
};

#pragma pack(pop)

VideoFrameProcessor::VideoFrameProcessor(std::condition_variable& tick_cv)
    : tick_cv_(tick_cv)
{
}

VideoFrameProcessor::~VideoFrameProcessor()
{
}

bool VideoFrameProcessor::Init(ID3D11Device* sdk_dev, ID3D11DeviceContext* sdk_ctx, ID3D11Device* dev, ID3D11DeviceContext* ctx)
{

    bool success = false;

    do
    {
        if (!sdk_dev || !sdk_ctx || !dev || !ctx)
            break;

        sdk_dev_ = sdk_dev;
        sdk_ctx_ = sdk_ctx;
        dev_ = dev;
        ctx_ = ctx;

        {
            ComPtr<IDXGIDevice> dxgi_dev;
            HRESULT             hr = dev->QueryInterface(dxgi_dev.GetAddressOf());
            if (FAILED(hr))
                break;

            ComPtr<IDXGIAdapter> adapter;
            hr = dxgi_dev->GetAdapter(adapter.GetAddressOf());
            if (FAILED(hr))
                break;

            adapter->GetDesc(&adapter_desc_);
        }

        frame_grab_.SetSendFrameCB(
            std::bind(&VideoFrameProcessor::OnGrabFrameSentCB, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4, std::placeholders::_5));

        bool ok = frame_grab_.Init(sdk_dev, sdk_ctx, dev, ctx);
        if (!ok)
            break;

        StartGrabFrameProcessThread();

        color_correction_filter_.Init(dev_, ctx_);

        ready_ = true;
        success = true;

    } while (0);

    return success;
}

void VideoFrameProcessor::Uninit()
{
    StopGrabFrameProcessThread();
    frame_grab_.SetSendFrameCB(nullptr);
    frame_grab_.Uninit();

    color_correction_filter_.Uninit();

    ready_ = false;
    sdk_dev_ = nullptr;
    sdk_ctx_ = nullptr;
    dev_ = nullptr;
    ctx_ = nullptr;
}

void VideoFrameProcessor::OnHookVideoAPILayerRenderLoopBegin(int64_t                                                render_count,
                                                             const std::map<uint32_t, std::vector<std::string>>&    canvas_map,
                                                             const std::map<std::string, std::vector<std::string>>& canvas_item_map,
                                                             const std::map<std::string, std::string>&              canvas_item_map2)
{
    frame_grab_.OnHookVideoAPILayerRenderLoopBegin(render_count, canvas_map, canvas_item_map, canvas_item_map2);
}

void VideoFrameProcessor::OnHookVideoAPILayerRenderLoopEnd()
{
    frame_grab_.OnHookVideoAPILayerRenderLoopEnd();
}

void VideoFrameProcessor::OnCanvasItemOriginalTexture(const std::string& canvas_item_id, ID3D11Texture2D* texture)
{
    frame_grab_.OnCanvasItemOriginalTexture(canvas_item_id, texture);
}

void VideoFrameProcessor::OnCanvasItemFilteredTexture(const std::string& canvas_item_id, ID3D11Texture2D* texture)
{
    frame_grab_.OnCanvasItemFilteredTexture(canvas_item_id, texture);
}

void VideoFrameProcessor::OnPreviewTextureCallback(const int64_t sink_id, ID3D11Texture2D* texture)
{
    frame_grab_.OnPreviewTextureCallback(sink_id, texture);
}

void VideoFrameProcessor::OnWorkerThreadLoopTick(uint64_t tick_count)
{
    {
        std::unique_lock<std::mutex> lock(loop_render_tasks_mutex_);
        tmp_loop_render_tasks_ = std::move(loop_render_tasks_);
    }

    for (auto task : tmp_loop_render_tasks_)
    {
        if (task && *task)
        {
            bool need_continue = true;
            (*task)(dev_, ctx_, need_continue);

            if (need_continue)
            {
                std::unique_lock<std::mutex> lock(loop_render_tasks_mutex_);
                loop_render_tasks_.push_back(task);
            }
        }
    }
    tmp_loop_render_tasks_.clear();

    frame_grab_.OnWorkerThreadLoopTick(tick_count);
}

void VideoFrameProcessor::OnRenderTaskTick()
{
    if (!ready_)
        return;

    {
        std::unique_lock<std::mutex> lock(render_tasks_mutex_);

        if (render_tasks_.empty())
            return;

        render_tasks_.swap(tmp_render_tasks_);
    }

    for (const auto& task : tmp_render_tasks_)
    {
        if (task)
            task(dev_, ctx_);
    }
    tmp_render_tasks_.clear();
}

void VideoFrameProcessor::SubmitRenderTask(const RenderTask& task)
{

    if (!ready_)
        return;

    {
        std::unique_lock<std::mutex> lock(render_tasks_mutex_);
        render_tasks_.push_back(task);
    }

    tick_cv_.notify_one();
}

void VideoFrameProcessor::SubmitLoopRenderTask(const LoopRenderTask& task)
{
    {
        std::unique_lock<std::mutex> lock(loop_render_tasks_mutex_);
        loop_render_tasks_.push_back(std::make_shared<LoopRenderTask>(task));
    }
}

bool VideoFrameProcessor::GrabFrame(
    const std::string&  media_object_id,
    const std::string&  frame_id,
    ClipResizeOrderEnum clip_resize_order,
    FitModeEnum         fit_mode,
    int32_t             target_width,
    int32_t             target_height,
    int32_t             clip_x,
    int32_t             clip_y,
    int32_t             clip_z,
    int32_t             clip_w)
{
    return frame_grab_.GrabFrame(media_object_id, frame_id, clip_resize_order, fit_mode, target_width, target_height, clip_x, clip_y, clip_z, clip_w);
}

void VideoFrameProcessor::SetGrabFrameResultCallback(const GrabFrameResultCallback& cb)
{
    frame_grab_.SetGrabFrameResultCallback(cb);
}

ColorCorrectionFilter* VideoFrameProcessor::GetColorCorrectionFilter()
{
    return &color_correction_filter_;
}

bool VideoFrameProcessor::OnGrabFrameSentCB(int64_t task_id, const std::string& frame_id, int64_t shared_handle, int64_t frame_width, int64_t frame_height)
{
    GrabFrameMsgSend msg;

    msg.task_id = task_id;

    msg.frame_id_len = (std::min)(frame_id.size(), sizeof(msg.frame_id));
    strncpy(msg.frame_id, frame_id.c_str(), msg.frame_id_len);

    msg.shared_handle = shared_handle;

    msg.frame_width = frame_width;
    msg.frame_height = frame_height;

    memcpy(&msg.adapter_desc, &adapter_desc_, sizeof(DXGI_ADAPTER_DESC));

    bool ok = send_buffer_.Push(&msg, sizeof(msg), true);

    return ok;
}

void VideoFrameProcessor::StartGrabFrameProcessThread()
{
    StopGrabFrameProcessThread();

    grab_frame_process_thread_stop_ = false;
    grab_frame_process_thread_ = std::thread(&VideoFrameProcessor::GrabFrameProcessThread, this);
}

void VideoFrameProcessor::StopGrabFrameProcessThread()
{
    grab_frame_process_thread_stop_ = true;
    grab_frame_process_thread_cv_.notify_all();

    ack_buffer_.Close();

    if (grab_frame_process_thread_.joinable())
        grab_frame_process_thread_.join();

    grab_frame_process_thread_ = {};
}

void VideoFrameProcessor::GrabFrameProcessThread()
{
    bool send_buffer_ok = false;
    bool ack_buffer_ok = false;

    size_t buffer_elem_size = 0;
    size_t buffer_buffer_cap = 0;

    size_t ack_elem_size = 0;
    size_t ack_buffer_cap = 0;

    while (!grab_frame_process_thread_stop_)
    {
        buffer_elem_size = sizeof(GrabFrameMsgSend);
        buffer_buffer_cap = 128;

        send_buffer_ok = send_buffer_.Create(kSendBufferGUID, buffer_elem_size, buffer_buffer_cap);
        if (!send_buffer_ok)
            send_buffer_ok = send_buffer_.Open(kSendBufferGUID, &buffer_elem_size, &buffer_buffer_cap);

        if (send_buffer_ok)
            break;

        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }

    while (!grab_frame_process_thread_stop_)
    {
        ack_elem_size = sizeof(GrabFrameMsgAck);
        ack_buffer_cap = 128;

        ack_buffer_ok = ack_buffer_.Create(kAckBufferGUID, ack_elem_size, ack_buffer_cap);
        if (!ack_buffer_ok)
            ack_buffer_ok = ack_buffer_.Open(kAckBufferGUID, &ack_elem_size, &ack_buffer_cap);

        if (ack_buffer_ok)
            break;

        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }

    send_buffer_.Clear();
    ack_buffer_.Clear();

    if (ack_buffer_ok)
    {
        GrabFrameMsgAck ack;

        while (!grab_frame_process_thread_stop_)
        {
            size_t tmp_size = sizeof(ack);
            bool   ok = ack_buffer_.Pop(&ack, &tmp_size, 200);

            if (ok)
            {
                frame_grab_.SendFrameAck(ack.task_id);
            }
        }
    }
}
} // namespace v3