﻿#pragma once

#include <string>
#include <stdint.h>
#include <functional>
#include <mutex>
#include <map>
#include <memory>
#include <thread>
#include <deque>
#include <queue>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <set>

#include "mediasdk_v2_header/hook_api/custom_audio_input_delegate.h"
#include "AudioResampler.h"
#include "CustomAudioInputImpl.h"

namespace v3
{
class AudioFrameProcessor
{
public:
    friend class HookAPILayer;

public:
    // AudioInput-创建
    bool CreateCaptureAudioInput(
        uint32_t audio_track,
        const std::string& audio_input_id,
        uint32_t buffer_time_ms);

    // AudioInput-销毁
    bool DestroyCaptureAudioInput(
        const std::string& audio_input_id,
        bool               delay_delete = true);

    // AudioInput-消音开关
    bool CaptureAudioInputEnableAutoMute(
        const std::string& audio_input_id,
        bool               enable);

    // AudioInput-设置替代音频
    bool CaptureAudioInputSetReplaceAudio(
        const std::string& audio_input_id,
        const std::string& replace_audio_filename);

    // AudioInput-设置音源
    bool CaptureAudioInputSetSource(
        const std::string& audio_input_id,
        const std::string& source_audio_input_id);

    // AudioInput-获取音源信息
    bool CaptureAudioInputGetSource(
        const std::string& audio_input_id,
        std::string*       source_audio_input_id);

    // AudioInput-消除指定范围声音
    bool CaptureAudioInputRange(
        const std::string& audio_input_id,
        int64_t begin_pts_ms,
        int64_t end_pts_ms,
        bool* hit = nullptr,
        int64_t* buffer_begin_pts_ms = nullptr,
        int64_t* buffer_end_pts_ms = nullptr,
        bool enable_dump = false,
        const std::string& dump_tag = "",
        const std::string& dump_filename = "");

    // AudioInput-获取全部id列表
    bool CaptureAudioInputGetIDList(std::vector<std::string>* audio_input_id_list);

    using CaptureAudioInputRangeDumpFileCallback = std::function<void(const std::string& dump_tag, const std::string& dump_filename, bool hit, bool dump_file_success)>;
    void SetCaptureAudioInputRangeDumpFileCallback(CaptureAudioInputRangeDumpFileCallback cb);

public:
    enum class AudioInputFrameTypeEnum
    {
        kAudioInputFrameTypeEnum_Raw,
        kAudioInputFrameTypeEnum_Filtered
    };

    // 单声道PCM-S16数据回调
    using MonoAudioPCMS16DataCallback = std::function<void(
        const std::string&      audio_input_id,
        const std::string&      audio_capture_id,
        AudioInputFrameTypeEnum frame_type,
        int64_t                 timestamp_ns,
        int16_t*                pcm_s16_data,
        int32_t                 nb_sample_count)>;

    // AudioInput-设置监听音源回调
    void SetMonoAudioPCMS16DataCallback(MonoAudioPCMS16DataCallback cb);

    // AudioInput-开始监听
    bool AudioInputStartListen(const std::string& audio_input_id, AudioInputFrameTypeEnum frame_type, const std::string& audio_capture_id = "");
    // AudioInput-停止监听
    bool AudioInputStopListen(const std::string& audio_input_id, AudioInputFrameTypeEnum frame_type);
    bool AudioInputStopListen(const std::string& audio_input_id);
    // AudioInput-获取监听列表
    bool AudioInputGetListeningList(std::vector<std::string>* audio_id_list);

public:
    using AddCustomAudioInputHandler = std::function<bool(const std::string& audio_input_id, uint32_t track_id, mediasdk::hook_api::CustomAudioInputDelegate* delegate)>;
    using RemoveCustomAudioInputHandler = std::function<void(const std::string& audio_input_id)>;
    using SetMainAudioTrackDelayHandler = std::function<bool(int64_t delay_ms)>;

    void SetAddCustomAudioInputHandler(AddCustomAudioInputHandler h);
    void SetRemoveCustomAudioInputHandler(RemoveCustomAudioInputHandler h);
    void SetSetMainAudioTrackDelayHandler(SetMainAudioTrackDelayHandler h);

protected:
    struct ListenAudioInputContext
    {
        std::string audio_input_id;

        int ref_count = 0;

        bool listen_raw_frame = false;
        bool listen_filtered_frame = false;
    };

    using ListenAudioInputContextPtr = std::shared_ptr<ListenAudioInputContext>;

    AudioFrameProcessor();
    ~AudioFrameProcessor();

    bool Init();
    void Uninit();

    void OnAudioInputRawFrame(const std::string& audio_input_id, const bool is_mute, const mediasdk::AudioSourceFrame& frame);
    void OnAudioInputFilteredFrame(const std::string& audio_input_id, const bool is_mute, const mediasdk::AudioSourceFrame& frame);
    void OnMixedAudioFrame(uint32_t track_id, const mediasdk::AudioSourceFrame& frame);

private:
    struct AudioFrameContextBase
    {
        uint8_t* data[mediasdk::AudioFramePlane::kMaxAudioPlanes];
        int32_t  sample_rate;
        int32_t  block_size;
        int32_t  count;
        int32_t  channel_count;
        int64_t  timestamp_ns;
        int64_t  begin_timestamp_ns;
    };

    struct AudioFrameContext : public AudioFrameContextBase
    {
        std::chrono::high_resolution_clock::time_point recv_tp;
        std::chrono::high_resolution_clock::time_point target_flush_tp;
        std::chrono::high_resolution_clock::time_point target_keep_tp;

        std::string buffer;

        bool is_mute = false;
        bool capture_mark = false;
    };

    using AudioFrameContextPtr = std::shared_ptr<AudioFrameContext>;

    struct CustomAudioInputContext
    {
        std::string custom_audio_input_id;
        uint32_t    buffer_time_ms = 2000;
        uint32_t    raw_buffer_time_ms = 3000;

        std::atomic_bool                               need_remove = false;
        std::chrono::high_resolution_clock::time_point remove_tp;

        std::mutex                       audio_frame_list_mutex;
        std::deque<AudioFrameContextPtr> audio_frame_list;

        std::mutex                       raw_audio_frame_list_mutex;
        std::deque<AudioFrameContextPtr> raw_audio_frame_list;

        std::deque<AudioFrameContextPtr> flush_audio_frame_list;
        bool                             first_flush_audio_frame = false;

        using CustomAudioInputImplPtr = std::unique_ptr<CustomAudioInputImpl>;

        std::mutex              impl_mutex;
        CustomAudioInputImplPtr impl;

        std::mutex capture_audio_buffer_mutex;

        std::string capture_audio_buffer_l;
        std::string capture_audio_buffer_r;
        int64_t     capture_audio_frame_count;

        int64_t capture_audio_idx = 0;

        std::chrono::high_resolution_clock::time_point audio_frame_list_overflow_tp;
        std::chrono::high_resolution_clock::time_point raw_audio_frame_list_overflow_tp;
    };

    using CustomAudioInputContextPtr = std::shared_ptr<CustomAudioInputContext>;

    void AddAudioFrame(CustomAudioInputContextPtr ctx, AudioFrameContextPtr audio_frame, AudioInputFrameTypeEnum frame_type);
    void AsyncConvertToMonoAudio(const std::string& audio_input_id, AudioFrameContextPtr audio_frame, AudioInputFrameTypeEnum frame_type);
    void NotifyMonoAudioPCMS16Data(const std::string&      audio_input_id,
                                   const std::string&      audio_capture_id,
                                   AudioInputFrameTypeEnum frame_type,
                                   int64_t                 timestamp_ns,
                                   int16_t*                pcm_s16_data,
                                   int32_t                 nb_sample_count);

private:
    bool AddCustomAudioInput(const std::string& audio_input_id, uint32_t track_id, mediasdk::hook_api::CustomAudioInputDelegate* delegate);
    bool RemoveCustomAudioInput(const std::string& audio_input_id);
    bool SetMainAudioTrackDelay(const int64_t delay_ms);

    ListenAudioInputContextPtr GetListenAudioInputContextNoLock(const std::string& audio_input_id);
    void                       CheckIfNeedRemoveListenAudioInputContextNoLock(ListenAudioInputContextPtr ctx);

    AudioFrameContextPtr CreateAudioFrameContext(
        const mediasdk::AudioSourceFrame&                     frame,
        const std::chrono::high_resolution_clock::time_point& recv_tp,
        const bool                                            is_mute);

    void                  ToAudioSourceFrame(const AudioFrameContextBase* in, mediasdk::AudioSourceFrame* out);
    AudioFrameContextBase MakeAudioFrameRef(const AudioFrameContext* in, void* buffer, size_t len);

    bool StartAudioProcessThread();
    void StopAudioProcessThread();
    void AudioProcessThread();

private:
    std::mutex                    custom_audio_input_add_remove_handler_mutex_;
    AddCustomAudioInputHandler    custom_audio_input_add_handler_;
    RemoveCustomAudioInputHandler custom_audio_input_remove_handler_;
    SetMainAudioTrackDelayHandler custom_audio_input_set_track_delay_handler_;

    std::mutex                                        listen_audio_input_list_mutex_;
    std::map<std::string, ListenAudioInputContextPtr> listen_audio_input_list_;

    struct ResamplerContext
    {
        std::atomic_bool need_delete = false;

        std::string             audio_input_id;
        std::string             audio_capture_id;
        AudioInputFrameTypeEnum frame_type = AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw;

        AudioResampler resampler;
        bool           resampler_init = false;

        std::mutex                       frame_list_mutex;
        std::deque<AudioFrameContextPtr> frame_list;

        int64_t nb_input_frame = 0;
        int64_t nb_output_frame = 0;
        bool    log_first_input = false;
        bool    log_first_output = false;
    };

    using ResamplerContextPtr = std::shared_ptr<ResamplerContext>;

    std::mutex                                 resampler_ctxs_mutex_;
    std::map<std::string, ResamplerContextPtr> raw_resampler_ctxs_;
    std::map<std::string, ResamplerContextPtr> filtered_resampler_ctxs_;

    std::atomic_bool        audio_process_stop_ = true;
    std::thread             audio_process_thread_;
    std::condition_variable audio_process_cv_;
    std::mutex              audio_process_cv_mutex_;
    std::atomic_uint64_t    audio_process_task_count_ = 0;

    std::mutex                  notify_listener_cb_mutex_;
    MonoAudioPCMS16DataCallback notify_listener_cb_;

private:
    std::mutex                                        custom_audio_input_map_mutex_;
    std::map<std::string, CustomAudioInputContextPtr> custom_audio_input_map_;

    struct AudioHandleContext
    {
        std::chrono::high_resolution_clock::time_point handle_tp;
        std::string                                    custom_audio_input_id;

        bool operator>(const AudioHandleContext& o) const
        {
            return handle_tp > o.handle_tp;
        }
    };

    std::mutex              audio_handle_queue_mutex_;
    std::condition_variable audio_handle_queue_cv_;

    std::priority_queue<
        AudioHandleContext,
        std::vector<AudioHandleContext>,
        std::greater<AudioHandleContext>>
        audio_handle_queue_;

    std::mutex                         custom_audio_input_listen_map_mutex_;
    std::map<std::string, std::string> custom_audio_input_listen_map_; // listen id -> custom audio iput id

    CustomAudioInputContextPtr GetCustomAudioInputContext(const std::string& audio_input_id);
    CustomAudioInputContextPtr GetCustomAudioInputContextBySource(const std::string& source_audio_input_id);
    CustomAudioInputContextPtr RemoveCustomAudioInputContext(const std::string& audio_input_id);
    void                       RemoveCustomAudioInputListenSource(const std::string& audio_input_id);

    bool StartCustomAudioInputProcessThread();
    void StopCustomAudioInputProcessThread();
    void CustomAudioInputProcessThread();
    void FlushCustomAudioInput(
        CustomAudioInputContextPtr                            ctx,
        const std::chrono::high_resolution_clock::time_point& handle_tp,
        std::chrono::high_resolution_clock::time_point&       next_handle_tp,
        int64_t&                                              api_usetime_ns);
    void HighPrecisionSleep(const std::chrono::high_resolution_clock::time_point& end_tp);

    std::thread      custom_audio_input_output_thread_;
    std::atomic_bool custom_audio_input_output_thread_stop_ = true;

    int64_t main_audio_track_delay_ms_ = 0;
    void    AdjustMainAudioTrackDelay();

private:
    struct DeleteHandleContext
    {
        std::chrono::high_resolution_clock::time_point target_delete_tp;
        std::string                                    custom_audio_input_id;

        bool operator>(const DeleteHandleContext& o) const
        {
            return target_delete_tp > o.target_delete_tp;
        }
    };

    std::mutex delete_handle_queue_mutex_;

    std::priority_queue<
        DeleteHandleContext,
        std::vector<DeleteHandleContext>,
        std::greater<DeleteHandleContext>>
        delete_handle_queue_;

    std::thread             delete_handle_thread_;
    std::atomic_bool        delete_handle_thread_stop_ = true;
    std::condition_variable delete_handle_thread_cv_;

    bool StartDeleteHandleThread();
    void StopDeleteHandleThread();
    void DeleteHandleThread();

private:
    struct DumpFileTask
    {
        std::string                      tag;
        std::string                      filename;
        std::deque<AudioFrameContextPtr> audio_frame_list;
        bool                             hit = false;
    };

    using DumpFileTaskPtr = std::shared_ptr<DumpFileTask>;

    std::thread                 dump_file_thread_;
    std::atomic_bool            dump_file_thread_stop_ = true;
    std::mutex                  dump_file_thread_mutex_;
    std::condition_variable     dump_file_thread_cv_;
    std::deque<DumpFileTaskPtr> dump_file_tasks_;
    AudioResampler              dump_file_resampler_;

    std::mutex                             dump_file_cb_mutex;
    CaptureAudioInputRangeDumpFileCallback dump_file_cb_;

    bool StartDumpFileThread();
    void StopDumpFileThread();
    void DumpFileThread();
    bool ProcessDumpFileTask(DumpFileTask* task);
    void PostDumpFileTask(const std::string& tag, const std::string& filename, const std::deque<AudioFrameContextPtr>& frames, bool hit);
};
} // namespace v3