#pragma once

#include "mediasdk_array.hpp"
#include "mediasdk_defines_visual.h"
#include "mediasdk_defines_visual_filter.h"
#include "mediasdk_export.h"
#include "mediasdk_visual_event_observer.h"

#ifdef __cplusplus
extern "C" {
#endif

namespace mediasdk {

// Enum SDK current visual's plug ins can support source/device
// Asynchronous callback result need cast to mediasdk::PluginInfoArray
MEDIASDK_EXPORT void MS_API EnumVisualSource(Closure closure);

// Create visual identifier with visual_id
// More information refs to CreateVisualParams
// Asynchronous callback result need cast to bool
// If creating an RTC visual, this interface needs to be called after the RTC
// engine is created.
MEDIASDK_EXPORT void MS_API CreateVisual(const char* visual_id,
                                         const CreateVisualParams& params,
                                         Closure closure);

// Destroy a visual by id with CreateVisual first param
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API DestroyVisual(const char* visual_id,
                                          Closure closure);

// Rereate visual identifier with visual_id
// More information refs to CreateVisualParams
// Asynchronous callback result need cast to bool
// Can not used for rtc visual
MEDIASDK_EXPORT void MS_API RecreateVisual(const char* visual_id,
                                           const CreateVisualParams& params,
                                           Closure closure);

// Register an object named MediaSDKVisualEventObserver to SDK, When event
// happened it will notify Ensure observer is valid before you call the
// UnregisterVisualEventObserver return Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
RegisterVisualEventObserver(MediaSDKVisualEventObserver* observer,
                            Closure closure);

// Unregister observer to the SDK
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
UnregisterVisualEventObserver(MediaSDKVisualEventObserver* observer,
                              Closure closure);

// Reopen a visual by id with json_params for plugin when used by CreateVisual
// Asynchronous callback result need cast to bool
MEDIASDK_EXPORT void MS_API ReopenVisual(const char* visual_id,
                                         const char* json_params,
                                         Closure closure);

MEDIASDK_EXPORT void MS_API
ReopenVisualAndUpdateTransforms(const char* visual_id,
                                const char* json_params,
                                const MediaSDKArray<ReopenParam>& reopen_params,
                                Closure closure);

// Get visual property by id and key
// Asynchronous callback result, MediaSDKString value used utf8
MEDIASDK_EXPORT void MS_API GetVisualInputProperty(const char* id,
                                                   const char* key,
                                                   Closure closure);

// Set visual property by id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVisualInputProperty(const char* id,
                                                   const char* key,
                                                   const char* json_params,
                                                   Closure closure);

MEDIASDK_EXPORT void MS_API DoVisualInputAction(const char* id,
                                                const char* json_params,
                                                Closure closure);

// Set video range mediasdk::VideoRange to visual by id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVisualVideoRange(const char* id,
                                                int video_range,
                                                Closure closure);

// Set color space mediasdk::ColorSpace to visual by id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVisualColorSpace(const char* id,
                                                int color_space,
                                                Closure closure);

// Enum visual format by plugin_name and device_id which is EnumVisualInput
// Asynchronous callback result, MediaSDKString value used utf8
MEDIASDK_EXPORT void MS_API EnumVisualFormat(const char* plugin_name,
                                             const char* device_id,
                                             Closure closure);

// Enum audio format by plugin_name and device_id which is EnumVisualInput
// Asynchronous callback result, MediaSDKString value used utf8
MEDIASDK_EXPORT void MS_API EnumAudioFormat(const char* plugin_name,
                                            const char* device_id,
                                            Closure closure);

// Enum support visual input by plugin_name and json_config
// Asynchronous callback result, MediaSDKString value used utf8
MEDIASDK_EXPORT void MS_API EnumVisualInput(const char* plugin_name,
                                            const char* json_config,
                                            Closure closure);

// Pause visual capture by id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API PauseVisualCapture(const char* id, Closure closure);

// Pause visual capture by id
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API IsVisualCapturePause(const char* id,
                                                 Closure closure);

// Resume visual capture by id
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API ContinueVisualCapture(const char* id,
                                                  Closure closure);

// Get the visual fps by id
// This interface should not be called after calling unintialize, as there is a
// risk of crash. Result is ResultBoolFloat
MEDIASDK_EXPORT bool MS_API GetVisualFPS(const char* id, float& fps);

// Test if visual has audio
// Asynchronous callback result, ResultBoolBool
MEDIASDK_EXPORT void MS_API IsVisualHasAudio(const char* id, Closure closure);

// Get visual plugin source property
// Asynchronous callback result, MediaSDKString
MEDIASDK_EXPORT void MS_API GetVisualSourceProperty(const char* plugin_name,
                                                    const char* json,
                                                    Closure closure);

// Set visual plugin source property
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API SetVisualSourceProperty(const char* plugin_name,
                                                    const char* key,
                                                    const char* json,
                                                    Closure closure);

// Forward window message to preview windows
MEDIASDK_EXPORT void MS_API ForwardWindowMessage(uint32_t preview_id,
                                                 int32_t msg,
                                                 uint64_t wparam,
                                                 uint64_t lparam);

MEDIASDK_EXPORT void MS_API GetCanvasItemIdsFromVisual(const char* id,
                                                       Closure closure);

// Get Visual BGRA Format Frame
// Asynchronous callback result, ResultBoolMSVisualFrame
MEDIASDK_EXPORT void MS_API GetVisualFrame(const char* visual_id,
                                           int target_width,
                                           int target_height,
                                           mediasdk::MSClip clip,
                                           Closure closure);

// Change visual's destroy behavior when all reference removed
// Asynchronous callback result, bool
MEDIASDK_EXPORT void MS_API
SetVisualDestroyedWhenAllReferenceRemoved(const char* visual_id,
                                          bool destroyed,
                                          Closure closure);

}  // namespace mediasdk
#ifdef __cplusplus
}
#endif  // __cplusplus
