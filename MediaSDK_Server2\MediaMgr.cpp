﻿#include <fstream>
#include <string>
#include <deque>
#include <filesystem>
#include "MediaMgr.h"
#include "stringutil.h"
#include "ciefhelper.h"
#include "EnumConverter.h"
#include "LogDumpMgr.h"
#include "MonitorMgr.h"
#include "NvidiaDriverHangRecover.h"
#include "SourceMgr.h"
#include "ModeSceneMgr.h"
#include "MediaMgr.h"
#include "PhoneCameraMgr.h"
extern media_mgr::MediaMgr* g_mediaMgr;

extern "C"
{
    __declspec(dllexport) DWORD NvOptimusEnablement = 1;
    __declspec(dllexport) int AmdPowerXpressRequestHighPerformance = 1;
}

const char* DshowEnumBlackList[] = {"LSVCam", "WebcastMate VirtualCamera"};
const char* DshowCreateBlackList[] = {"WebcastMate VirtualCamera"};

namespace media_mgr
{

    MediaMgr::MediaMgr() {}
    MediaMgr::~MediaMgr() {}

    void MediaMgr::LogMessageHandler(int32_t severity, const char* szFile, int32_t line, const char* szText)
    {
        ParfaitLogHandler(severity, szFile, line, szText);
    }

    bool MediaMgr::Initialize(INITIALIZE_INFO info)
    {
        task_mgr_.Init();
        full_screen_detector_.Init(std::bind(&MediaMgr::OnFullScreenDetectorCallback, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3));

        std::string sdk_log_file = "";
        try
        {
            std::wstring          serverLogFile = LogDumpMgr::GetInstance()->m_serverLogFile;
            std::filesystem::path p(serverLogFile);
            std::wstring          ext;
            if (p.has_extension())
                ext = p.extension();
            std::filesystem::path p2(ext);

            p.replace_extension(L"");
            p.replace_filename(p.filename().wstring() + (L"_sdkv2"));
            sdk_log_file = p.u8string() + p2.u8string();
        }
        catch (...)
        {
        }

        {
            char verinfo[32];
            sprintf_s(verinfo, "%u", 2);
            LogDumpMgr::GetInstance()->SetParfaitContextInfo("sdk_impl_ver", verinfo, true);
        }

        PhoneCameraMgr::getInstance().SetUidDid(info.uid, info.did);
        bool success = false;
        do
        {
            sdk_controller_ = sdk_helper::MediaSDKControllerV2ImplInit();
            success = !!sdk_controller_;
            if (!success)
                break;
            init_sdk_ = true;

            sdk_controller_->SetHost(info.host);
            sdk_controller_->SetLogMessageHandler(std::bind(&MediaMgr::LogMessageHandler, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4));
            sdk_controller_->SetThreadMonitorEventHandler(std::bind(&MediaMgr::OnThreadMonitorEvent, this, std::placeholders::_1, std::placeholders::_2));

            init_param_ = info;
            init_param_.workDir = "D:\\MediaSDK_V2";
            init_param_.resDir = "D:\\MediaSDK_V2";
            init_param_.sdkLogFile = sdk_log_file;
            app_work_dir_ = info.workDir;
            enable_transition_ = info.enableTransition;

            SourceMgr::GetInstance()->Init();
            g_cief->GetThreadMgr()->AddTaskToThreadPool(new LS::MemberTask<MediaMgr>(this, &MediaMgr::StartTimer, 0));

            bool ok = sdk_controller_->Init(init_param_);
            if (ok)
            {
                init_api_ = true;
                ::SetWindowLongPtr((HWND)init_param_.topWindow, GWLP_HWNDPARENT, (LONG_PTR)init_param_.bottomWnd);

                {
                    InitEvent event{};
                    event.success = true;
                    event.dxError = 0;
                    event.pid = static_cast<UINT32>(::GetCurrentProcessId());
                    eventbus::EventBus::PostEvent(event);
                }
            }
            else
            {
                LOG(ERROR) << "[MediaMgr::Initialize] async sdk_controller_->Init failed";
            }


            sdk_controller_->GetVideoFrameProcessor()->SetGrabFrameResultCallback(
                [this](const std::string& frame_id, int32_t width, int32_t height, int32_t errcode, std::string& errmsg) {
                    
                    CreateFrameEvent evt{};
                    evt.frameID = frame_id;
                    evt.frameSize.Width = width;
                    evt.frameSize.Height = height;
                    evt.errCode = errcode;
                    evt.errMsg = errmsg;

                    auto task = task_mgr_.CreateThreadTask([evt]() {
                        eventbus::EventBus::PostEvent(evt);
                    });

                    g_cief->GetThreadMgr()->AddTaskToBackThread(task);
                });

            init_finish_ = true;
        } while (0);

        auto gpu_info_list = MonitorMgr::GetInstance()->GetGPUInfo();
        if (gpu_info_list.size() > 0)
        {

            auto mainGPU = gpu_info_list[0];
            if (mainGPU.vendorId == 0x10DE)
            {
                // NVIDIA Video Card
                enable_nvidia_driver_hang_recover_ = true;
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->Enable(true);
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->Init();
            }
        }
        else
        {
            LOG(ERROR) << "[MediaMgr::Initialize] Get default GPU Name error!";
        }

        return success;
    }

    void MediaMgr::Uninitialize()
    {
        full_screen_detector_.Uninit();
        StopTimer();
        task_mgr_.Uninit();

        if (sdk_controller_)
        {
            sdk_controller_->GetVideoFrameProcessor()->SetGrabFrameResultCallback(NULL);
            sdk_controller_->SetLogMessageHandler(NULL);
            sdk_controller_->Uninit();
        }

        sdk_helper::MediaSDKControllerV2ImplUninit();
        sdk_controller_ = NULL;
        init_sdk_ = false;

        if (enable_nvidia_driver_hang_recover_)
        {
            enable_nvidia_driver_hang_recover_ = false;
            nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->Uninit();
        }
    }

    bool MediaMgr::SetMixParameter(const uint32_t video_model_id, const VIDEO_MIX_PARAM& param)
    {
        auto canvas_ctx = GetVideoModelCtx(video_model_id);
        if (!canvas_ctx)
        {
            init_param_.fps = param.fps;
            init_param_.outputSize.cx = param.outputSize.Width;
            init_param_.outputSize.cy = param.outputSize.Height;
            LOG(INFO) << "[MediaMgr::SetMixParameter] videoModeCtx is nullptr, videoModel: " << video_model_id;
            return false;
        }

        canvas_ctx->video_param.output_size_width = param.outputSize.Width;
        canvas_ctx->video_param.output_size_height = param.outputSize.Height;
        canvas_ctx->video_param.focus_nv12 = param.focusNv12;
        canvas_ctx->video_param.fps = param.fps < EPS ? 60.0f : param.fps;

        bool success = false;

        do
        {
            canvas_ctx->video_param.color_space = param.colorConfig.colorSpace;
            canvas_ctx->video_param.color_transfer = param.colorConfig.colorTransfer;
            canvas_ctx->video_param.color_range = param.colorConfig.colorRange;
            success = true;
        } while (0);

        if (!success)
            return false;

        success = SetVideoParam(canvas_ctx);
        if (!success)
            LOG(ERROR) << "[MediaMgr::SetMixParameter] SetVideoParam failed";

        return success;
    }

    bool MediaMgr::UpdateVideoModelLayout(PREVIEW_INFO& previewInfo)
    {
        if (!init_finish_)
        {
            LOG(ERROR) << "[MediaMgr::UpdateVideoModelLayout] init_finish_ is false";
            return false;
        }

        bool isNewPreview = false;
        auto video_model_ctx = GetVideoModelCtx(previewInfo.videoModel);
        auto preview_ctx = GetPreviewCtx(previewInfo.id);
        if (previewInfo.videoModel != UINT32_MAX)
        {
            // Preview Bind to VideoModel
            if (!video_model_ctx)
            {
                video_model_ctx = std::make_shared<VideoModelContext>();
                preview_ctx = std::make_shared<PreviewContext>();
                InitDefaultCanvasContext(video_model_ctx.get(), preview_ctx.get());
                video_model_ctx->video_model = previewInfo.videoModel;
                if (previewInfo.fps <= EPS)
                {
                    video_model_ctx->video_param.fps = init_param_.fps;
                }
                else
                {
                    video_model_ctx->video_param.fps = previewInfo.fps;
                    init_param_.fps = previewInfo.fps;
                }

                if (previewInfo.outputSize.Width <= EPS && previewInfo.outputSize.Height <= EPS)
                {
                    video_model_ctx->video_param.output_size_width = init_param_.outputSize.cx;
                    video_model_ctx->video_param.output_size_height = init_param_.outputSize.cy;
                }
                else
                {
                    video_model_ctx->video_param.output_size_width = std::round(previewInfo.outputSize.Width);
                    video_model_ctx->video_param.output_size_height = std::round(previewInfo.outputSize.Height);
                    init_param_.outputSize.cx = std::round(previewInfo.outputSize.Width);
                    init_param_.outputSize.cy = std::round(previewInfo.outputSize.Height);
                }

                if (previewInfo.hwnd == NULL)
                    previewInfo.hwnd = init_param_.bottomWnd;

                preview_ctx->preview_id = previewInfo.id;
                if (!sdk_controller_->CreateModel(previewInfo))
                {
                    LOG(ERROR) << "[MediaMgr::UpdateVideoModelLayout] CreateModel failed, video_model: " << previewInfo.videoModel;
                    return false;
                }

                if (!SetVideoParam(video_model_ctx))
                {
                    LOG(ERROR) << "[MediaMgr::UpdateVideoModelLayout] SetVideoParam failed";
                    return false;
                }

                {
                    std::unique_lock<std::mutex> lock(preview_ctx_map_mutex_);
                    preview_ctx_map_[previewInfo.id] = preview_ctx;
                }

                {
                    std::unique_lock<std::mutex> lock(video_model_ctx_map_mutex_);
                    video_model_ctx_map_[previewInfo.videoModel] = video_model_ctx;
                }

                isNewPreview = true;
            }
            else if (video_model_ctx && previewInfo.videoModel != UINT32_MAX)
            {
                bool has_size_changed = false;
                if (previewInfo.outputSize.Width > EPS && std::abs(video_model_ctx->video_param.output_size_width - previewInfo.outputSize.Width) > EPS)
                {
                    video_model_ctx->video_param.output_size_width = previewInfo.outputSize.Width;
                    has_size_changed = true;
                }
                if (previewInfo.outputSize.Height > EPS && std::abs(video_model_ctx->video_param.output_size_height - previewInfo.outputSize.Height) > EPS)
                {
                    video_model_ctx->video_param.output_size_height = previewInfo.outputSize.Height;
                    has_size_changed = true;
                }

                if (has_size_changed)
                {
                    if (!SetVideoParam(video_model_ctx))
                    {
                        LOG(ERROR) << "[MediaMgr::UpdateVideoModelLayout] SetVideoParam failed";
                        return false;
                    }
                }
            }
        }
        else
        {
            // Preview no more Bind to VideoModel
            if (!preview_ctx)
            {
                preview_ctx = std::make_shared<PreviewContext>();
                InitDefaultCanvasContext(nullptr, preview_ctx.get());
                if (previewInfo.hwnd == NULL)
                    previewInfo.hwnd = init_param_.bottomWnd;

                preview_ctx->preview_id = previewInfo.id;
                if (!sdk_controller_->CreatePreview(previewInfo))
                {
                    LOG(ERROR) << "[MediaMgr::UpdateVideoModelLayout] CreatePreview failed, preview_id: " << previewInfo.id;
                    return false;
                }
                else
                {
                    previewInfo.isCreated = true;
                    if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(previewInfo.id))
                    {
                        pView->SetPreviewInfo(&previewInfo);
                    }
                }

                {
                    std::unique_lock<std::mutex> lock(preview_ctx_map_mutex_);
                    preview_ctx_map_[previewInfo.id] = preview_ctx;
                }

                isNewPreview = true;
            }
        }

        {
            auto found2 = cache_preview_display_map_.find(previewInfo.id);
            if (found2 != cache_preview_display_map_.end())
            {
                bool show = found2->second;
                cache_preview_display_map_.erase(found2);
                SetDisplay(previewInfo.id, show);
            }
            else if (isNewPreview)
            {
                SetDisplay(previewInfo.id, false);
            }
        }

        if (preview_ctx)
        {
            bool has_preview_changed = false;
            if (previewInfo.rect.X > EPS && std::abs(preview_ctx->preview_x - previewInfo.rect.X) > EPS)
            {
                preview_ctx->preview_x = previewInfo.rect.X;
                has_preview_changed = true;
            }
            if (previewInfo.rect.Y > EPS && std::abs(preview_ctx->preview_y - previewInfo.rect.Y) > EPS)
            {
                preview_ctx->preview_y = previewInfo.rect.Y;
                has_preview_changed = true;
            }
            if (previewInfo.rect.Width > EPS && std::abs(preview_ctx->preview_width - previewInfo.rect.Width) > EPS)
            {
                preview_ctx->preview_width = previewInfo.rect.Width;
                has_preview_changed = true;
            }
            if (previewInfo.rect.Height > EPS && std::abs(preview_ctx->preview_height - previewInfo.rect.Height) > EPS)
            {
                preview_ctx->preview_height = previewInfo.rect.Height;
                has_preview_changed = true;
            }

            if (previewInfo.layoutRect.X > EPS && std::abs(preview_ctx->layout_preview_x - previewInfo.layoutRect.X) > EPS)
            {
                preview_ctx->layout_preview_x = previewInfo.layoutRect.X;
                has_preview_changed = true;
            }
            if (previewInfo.layoutRect.Y > EPS && std::abs(preview_ctx->layout_preview_y - previewInfo.layoutRect.Y) > EPS)
            {
                preview_ctx->layout_preview_y = previewInfo.layoutRect.Y;
                has_preview_changed = true;
            }
            if (previewInfo.layoutRect.Width > EPS && std::abs(preview_ctx->layout_preview_width - previewInfo.layoutRect.Width) > EPS)
            {
                preview_ctx->layout_preview_width = previewInfo.layoutRect.Width;
                has_preview_changed = true;
            }
            if (previewInfo.layoutRect.Height > EPS && std::abs(preview_ctx->layout_preview_height - previewInfo.layoutRect.Height) > EPS)
            {
                preview_ctx->layout_preview_height = previewInfo.layoutRect.Height;
                has_preview_changed = true;
            }

            if (has_preview_changed)
            {
                if (!SetPreviewPosition(preview_ctx))
                {
                    LOG(ERROR) << "[MediaMgr::UpdateVideoModelLayout] SetPreviewPosition failed";
                    return false;
                }
            }
        }

        return true;
    }

    bool MediaMgr::DestroyVideoModel(uint32_t video_model)
    {
        auto video_model_ctx = GetVideoModelCtx(video_model);
        if (!video_model_ctx)
        {
            LOG(ERROR) << "[MediaMgr::DestroyVideoModel] video_model_ctx is nullptr, video_model: " << video_model;
            return false;
        }

        bool success = sdk_controller_->DestroyModel(video_model);
        if (success)
        {
            std::unique_lock<std::mutex> lock(video_model_ctx_map_mutex_);
            video_model_ctx_map_.erase(video_model);
        }

        return success;
    }

    bool MediaMgr::DestroyPreview(uint32_t preview_id)
    {
        auto preview_ctx = GetPreviewCtx(preview_id);
        if (!preview_ctx)
        {
            LOG(ERROR) << "[MediaMgr::DestroyVideoModel] preview_ctx is nullptr, preview_id: " << preview_id;
            return false;
        }

        bool success = sdk_controller_->DestroyPreview(preview_id);
        if (success)
        {
            std::unique_lock<std::mutex> lock(preview_ctx_map_mutex_);
            preview_ctx_map_.erase(preview_id);
        }

        return success;
    }

    bool MediaMgr::SetDisplay(uint32_t preview_id, bool show)
    {
        LOG(INFO) << "[MediaMgr::SetDisplay] preview_id: " << preview_id << ", show_view: " << show;
        if (!init_sdk_ || !init_api_ || !sdk_controller_)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] init_sdk_ or init_api_ or sdk_ is nullptr";
            cache_preview_display_map_[preview_id] = show;
            return false;
        }

        auto preview_ctx = GetPreviewCtx(preview_id);
        if (!preview_ctx)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] preview_ctx is nullptr, preview_id: " << preview_id;
            cache_preview_display_map_[preview_id] = show;
            return false;
        }

        if (preview_ctx->show_canvas == show)
        {
            LOG(INFO) << "[MediaMgr::SetDisplay] preview show status is not change, preview_id: " << preview_id;
            return true;
        }
        preview_ctx->show_canvas = show;

        bool success = sdk_controller_->PreviewWindowEnableShow(preview_id, show);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] PreviewWindowEnableShow failed";
            return false;
        }

        bool enable_canvas_preview = all_canvas_enable_preview_ && show;
        success = sdk_controller_->PreviewWindowEnableDraw(preview_id, enable_canvas_preview);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::SetDisplay] PreviewWindowEnableDraw failed";
            return false;
        }

        return success;
    }

    bool MediaMgr::GetDisplay(uint32_t preview_id)
    {
        auto preview_ctx = GetPreviewCtx(preview_id);
        if (!preview_ctx)
        {
            LOG(ERROR) << "[MediaMgr::GetDisplay] preview_ctx is nullptr";
            return false;
        }

        LOG(INFO) << "[MediaMgr::GetDisplay] canvas_id: " << preview_id;
        return preview_ctx->show_canvas && all_canvas_enable_preview_;
    }

    bool MediaMgr::EnableAllPreview(bool enable)
    {
        all_canvas_enable_preview_ = enable;
        for (auto it : preview_ctx_map_)
        {
            auto preview_ctx = it.second;
            bool enable_canvas_preview = enable && preview_ctx->show_canvas;
            bool success = sdk_controller_->PreviewWindowEnableDraw(preview_ctx->preview_id, enable_canvas_preview);
            if (!success)
                LOG(ERROR) << "[MediaMgr::EnablePreview] PreviewWindowEnableDraw failed";

            LOG(INFO) << "[MediaMgr::EnablePreview] preview_id: " << preview_ctx->preview_id << ", enable: " << enable << ", show_canvas: " << preview_ctx->show_canvas << ", enable_canvas_preview: " << enable_canvas_preview;
        }

        return true;
    }

    bool MediaMgr::EnablePreviewByVideoModel(UINT32 preview_id, bool enable)
    {
        for (auto it : preview_ctx_map_)
        {
            auto preview_ctx = it.second;
            if (preview_ctx->preview_id == preview_id)
            {
                bool enable_canvas_preview = enable && preview_ctx->show_canvas;
                bool success = sdk_controller_->PreviewWindowEnableShow(preview_ctx->preview_id, enable_canvas_preview);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::EnablePreview] PreviewWindowEnableDraw failed, preview_id: " << preview_id;
                break;
            }
        }

        return true;
    }

    void MediaMgr::InitDefaultCanvasContext(VideoModelContext* video_model_ctx, PreviewContext* preview_ctx)
    {
        if (video_model_ctx)
        {
            video_model_ctx->video_model = UINT32_MAX;
            video_model_ctx->video_param.output_size_width = 800.0f;
            video_model_ctx->video_param.output_size_height = 600.0f;
            video_model_ctx->video_param.focus_nv12 = false;
            video_model_ctx->video_param.fps = 60.0f;

            video_model_ctx->video_param.color_space = COLOR_SPACE::COLOR_SPACE_BT709;
            video_model_ctx->video_param.color_transfer = COLOR_TRANSFER::COLOR_TRANSFER_BT709;
            video_model_ctx->video_param.color_range = COLOR_RANGE::COLOR_RANGE_FULL;
        }

        if (preview_ctx)
        {
            preview_ctx->preview_id = UINT32_MAX;
            preview_ctx->preview_x = 0;
            preview_ctx->preview_y = 0;
            preview_ctx->preview_width = 1;
            preview_ctx->preview_height = 1;

            preview_ctx->layout_preview_x = 0;
            preview_ctx->layout_preview_y = 0;
            preview_ctx->layout_preview_width = 1;
            preview_ctx->layout_preview_height = 1;

            preview_ctx->layer_id_order_list.clear();
            preview_ctx->show_canvas = true;
        }
    }

    bool MediaMgr::SetVideoParam(VideoModelContextPtr video_model_ctx)
    {
        if (video_model_ctx->video_param.output_size_width == 0 || video_model_ctx->video_param.output_size_height == 0)
        {
            LOG(ERROR) << "[MediaMgr::SetVideoParam] output_size_width == 0 or output_size_height == 0";
            return false;
        }

        std::map<uint32_t, VideoParam> videoModelMapVideoParam;
        videoModelMapVideoParam[video_model_ctx->video_model] = video_model_ctx->video_param;
        bool success = sdk_controller_->SetVideoSetting(videoModelMapVideoParam);
        if (!success)
            LOG(ERROR) << "[MediaMgr::SetVideoParam] SetVideoSetting failed";

        // Update VideoModel FPS, MeaningWhile Update Game Capture FPS
        const auto& metas = SourceMgr::GetInstance()->GetCompositeMetas();
        for (const auto& meta : metas)
        {
            if (video_model_ctx->video_param.fps > 1.0f && meta.second.primaryType == VISUAL_GAME && SourceMgr::GetInstance()->CheckSourceExist(meta.first))
            {
                std::string source_id = "";
                Util::NumToString(meta.first, &source_id);
                GAME_SOURCE game = std::get<GAME_SOURCE>(meta.second.source);
                sdk_controller_->GameSourceLimitCaptureFPS(source_id, game.enableLimit, video_model_ctx->video_param.fps);
            }
        }

        return success;
    }

    bool MediaMgr::SetPreviewPosition(PreviewContextPtr preview_ctx)
    {
        if (sdk_controller_ == nullptr || preview_ctx == nullptr)
        {
            LOG(ERROR) << "[MediaMgr::SetPreviewPosition] sdk_controller_ or preview_ctx is nullptr";
            return false;
        }

        LOG(INFO) << "[MediaMgr::SetPreviewPosition] preview_id: " << preview_ctx->preview_id << ", preview=[" << preview_ctx->preview_x << "," << preview_ctx->preview_y << "," << preview_ctx->preview_width << "," << preview_ctx->preview_height << "], layout_preview=[" << preview_ctx->layout_preview_x << "," << preview_ctx->layout_preview_y << "," << preview_ctx->layout_preview_width << "," << preview_ctx->layout_preview_height << "]";

        Gdiplus::RectF rect{};
        rect.X = preview_ctx->preview_x;
        rect.Y = preview_ctx->preview_y;
        rect.Width = preview_ctx->preview_width;
        rect.Height = preview_ctx->preview_height;

        Gdiplus::RectF layout_rect{};
        if (preview_ctx->layout_preview_width == 0 && preview_ctx->layout_preview_height == 0)
        {
            layout_rect = rect;
        }
        else
        {
            layout_rect.X = preview_ctx->layout_preview_x;
            layout_rect.Y = preview_ctx->layout_preview_y;
            layout_rect.Width = preview_ctx->layout_preview_width;
            layout_rect.Height = preview_ctx->layout_preview_height;
        }

        bool success = sdk_controller_->PreviewWindowSetPosition(preview_ctx->preview_id, rect, layout_rect);
        if (!success)
            LOG(ERROR) << "[MediaMgr::SetPreviewPosition] PreviewWindowSetPosition failed";

        return success;
    }

    VideoModelContextPtr MediaMgr::GetVideoModelCtx(const uint32_t video_model)
    {
        std::unique_lock<std::mutex> lock(video_model_ctx_map_mutex_);
        auto                         found = video_model_ctx_map_.find(video_model);
        if (found != video_model_ctx_map_.end())
            return found->second;
        return nullptr;
    }

    PreviewContextPtr MediaMgr::GetPreviewCtx(const uint32_t preview_id)
    {
        std::unique_lock<std::mutex> lock(preview_ctx_map_mutex_);
        auto                         found = preview_ctx_map_.find(preview_id);
        if (found != preview_ctx_map_.end())
            return found->second;
        return nullptr;
    }

    bool MediaMgr::AddAudio(const AUDIO_INFO& info)
    {
        std::string audio_id = "";
        bool success = Util::NumToString(info.id, &audio_id);
        if (!success)
        {
            LOG(ERROR) << "[MediaMgr::AddAudio] Util::NumToString failed, audio_id in: " << info.id << ", out: " << audio_id;
            return false;
        }

        if (info.type == AUDIO_WAS)
        {
            WAS_AUDIO   was = std::get<WAS_AUDIO>(info.audio);
            if (was.isLyrax)
            {
                success = sdk_controller_->LyraxEngineCreateAudioSource(info);
            }
            else
            {
                success = sdk_controller_->CreateWASAPIAudioSource(info);
            }

            if (success)
            {
                AUDIO_INFO& new_audio = const_cast<AUDIO_INFO&>(info);
                new_audio.audio = was;
            }
            else
            {
                LOG(ERROR) << "[MediaMgr::AddAudio] CreateWASAPIAudioSource failed";
            }
            LOG(INFO) << "[MediaMgr::AddAudio] audio_id: " << audio_id << ", audio_track: " << info.audioTrack << ", audio device: " << info.device.toString() << ", was audio info: " << was.toString();
        }
        else if (info.type == AUDIO_APP)
        {
            success = sdk_controller_->CreateAppAudioSource(info);
            if (!success)
                LOG(ERROR) << "[MediaMgr::AddAudio] CreateAppAudioSource failed";
            LOG(INFO) << "[MediaMgr::AddAudio] audio_id: " << audio_id << ", audio_track: " << info.audioTrack << ", audio device: " << info.device.toString() << ", app audio info: " << std::get<APP_AUDIO>(info.audio).excludePID;
        }
        else if (info.type == AUDIO_PCM)
        {
            success = sdk_controller_->CreatePCMAudioSource(info);
            if (!success)
                LOG(ERROR) << "[MediaMgr::AddAudio] CreatePCMAudioSource failed";
            LOG(INFO) << "[MediaMgr::AddAudio] audio_id: " << audio_id << ", audio_track: " << info.audioTrack << ", audio_capture: " << info.audioCapture.toString();
        }

        return success;
    }

    bool MediaMgr::RemoveAudio(const uint64_t audio_id)
    {
        LOG(INFO) << "[MediaMgr::RemoveAudio] audio_id: " << audio_id;
        bool success = false;
        do
        {
            std::string audio_id_str = "";
            Util::NumToString(audio_id, &audio_id_str);

            success = sdk_controller_->AudioSourceDestroy(audio_id_str);
            if (!success)
                LOG(ERROR) << "[MediaMgr::RemoveAudio] AudioSourceDestroy failed";
        } while (0);

        return success;
    }

    bool MediaMgr::ControlAudio(const AUDIO_CONTROL_INFO& info, bool isCaptureAudio /*= false*/)
    {
		std::string audio_id = "";
        UINT32      audioTrack = info.audioInfo.audioTrack;
		Util::NumToString(info.audioInfo.id, &audio_id);
        AUDIO_SETTING audioSetting = info.audioInfo.audioSetting;

        if (isCaptureAudio)
        {
            Util::NumToString(info.captureAudioInfo.id, &audio_id);
            audioSetting = info.captureAudioInfo.audioSetting;
            audioTrack = info.captureAudioInfo.audioTrack;
        }

        bool success = false;
		if (info.cmd & AUDIO_CONTROL_SET_ALL_SETTING)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetProperty audio_id: " << audio_id << ", audioSetting: " << audioSetting.toString();
			success = sdk_controller_->AudioSourceSetProperty(audio_id, audioSetting);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetProperty failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_VOLUME)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetVolumn audio_id: " << audio_id << ", volume: " << audioSetting.volume;
			success = sdk_controller_->AudioSourceSetVolumn(audio_id, audioSetting.volume);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetVolumn failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_BALANCEING)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetBalance audio_id: " << audio_id << ", balanceing: " << audioSetting.balanceing;
			success = sdk_controller_->AudioSourceSetBalance(audio_id, audioSetting.balanceing);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetBalance failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_SYNC_OFFSET)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetSyncOffset audio_id: " << audio_id << ", sync_offset: " << audioSetting.syncOffset;
			success = sdk_controller_->AudioSourceSetSyncOffset(audio_id, audioSetting.syncOffset);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetSyncOffset failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_INTERVAL)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetInterval audio_id: " << audio_id << ", interval: " << audioSetting.interval;
			success = sdk_controller_->AudioSourceSetInterval(audio_id, audioSetting.interval);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetInterval failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_MONITOR_TYPE)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetMonitorType audio_id: " << audio_id << ", monitor_type: " << audioSetting.monitorType;
			success = sdk_controller_->AudioSourceSetMonitorType(audio_id, audioSetting.monitorType);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetMonitorType failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_DOWN_MIX_MONO)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceMixToMono audio_id: " << audio_id << ", down_mix_mono: " << audioSetting.downMixMono;
			success = sdk_controller_->AudioSourceMixToMono(audio_id, audioSetting.downMixMono);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceMixToMono failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_MUTE)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetMute audio_id: " << audio_id << ", mute: " << audioSetting.mute;
			success = sdk_controller_->AudioSourceSetMute(audio_id, audioSetting.mute);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetMute failed";
		}
        if (info.cmd & AUDIO_CONTROL_SET_AUDIO_TRACK)
        {
            LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetTracks audio_id: " << audio_id << ", audio_track: " << audioTrack;
            success = sdk_controller_->AudioSourceSetTracks(audio_id, audioTrack);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlAudio] AudioSourceSetTracks failed";
        }
		if (info.cmd & AUDIO_CONTROL_UPDATE_PCM)
		{
			LOG(INFO) << "[MediaMgr::ControlAudio] PCMAudioSourceUpdate audio_id: " << audio_id << ", audio_capture: " << info.audioInfo.audioCapture.toString();
			success = sdk_controller_->PCMAudioSourceUpdate(audio_id, info.audioInfo);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] PCMAudioSourceUpdate failed";
		}
		if (info.cmd & AUDIO_CONTROL_ENABLE_AEC)
		{
            LOG(INFO) << "[MediaMgr::ControlAudio] LyraxEngineSetAECOption audio_id: " << audio_id << ", enable_aec: " << info.audioInfo.enableAec;
            success = sdk_controller_->LyraxEngineSetAECOption(audio_id, info.audioInfo.enableAec);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetAECOption failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_AEC_REF_ID)
		{
            LOG(INFO) << "[MediaMgr::ControlAudio] AudioSourceSetAECConf audio_id: " << audio_id << ", aec_ref_id: " << info.audioInfo.aecRefID;
            WAS_AUDIO was = std::get<WAS_AUDIO>(info.audioInfo.audio);
			if (was.isLyrax)
			{
                success = sdk_controller_->LyraxEngineSetAudioRefId(audio_id, info.audioInfo.aecRefID);
				if (!success)
					LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetAudioRefId failed";
			}
		}
		if (info.cmd & AUDIO_CONTROL_SET_AGC_OPTION)
        {
            LOG(INFO) << "[MediaMgr::ControlAudio] LyraxEngineSetAGCOption audio_id: " << audio_id << ", agc_option: " << info.audioInfo.agcOption;
            success = sdk_controller_->LyraxEngineSetAGCOption(audio_id, info.audioInfo.agcOption);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetAGCOption failed";
		}
		if (info.cmd & AUDIO_CONTROL_SET_ANS_OPTION)
		{
            LOG(INFO) << "[MediaMgr::ControlAudio] LyraxEngineSetANSOption audio_id: " << audio_id << ", ans_option: " << info.audioInfo.ansOption;
            success = sdk_controller_->LyraxEngineSetANSOption(audio_id, info.audioInfo.ansOption);
			if (!success)
				LOG(ERROR) << "[MediaMgr::ControlAudio] LyraxEngineSetANSOption failed";
		}
        return success;
    }

    bool MediaMgr::GetAudioInfo(const std::string& audio_id, AUDIO_INFO* info, AUDIO_INFO_CMD cmd)
    {
        bool success = false;
        if (cmd & AUDIO_INFO_SETTING && info)
        {
            success = sdk_controller_->AudioSourceIsMute(audio_id, &info->audioSetting.mute);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceIsMute failed";

            success = sdk_controller_->AudioSourceIsEnableMixToMono(audio_id, &info->audioSetting.downMixMono);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceIsEnableMixToMono failed";

            success = sdk_controller_->AudioSourceGetBalance(audio_id, &info->audioSetting.balanceing);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetBalance failed";

            success = sdk_controller_->AudioSourceGetVolumn(audio_id, &info->audioSetting.volume);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetVolumn failed";

            success = sdk_controller_->AudioSourceGetSyncOffset(audio_id, &(info->audioSetting.syncOffset));
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetSyncOffset failed";

            success = sdk_controller_->AudioSourceGetMonitorType(audio_id, &info->audioSetting.monitorType);
            if (!success)
                LOG(ERROR) << "[MediaMgr::GetAudioInfo] AudioSourceGetMonitorType failed audio_id: " << audio_id << ", monitor_type: " << static_cast<uint32_t>(info->audioSetting.monitorType);

            LOG(INFO) << "[MediaMgr::GetAudioInfo] audio_setting: " << info->audioSetting.toString();
        }

        return success;
    }

    bool MediaMgr::CreateFilter(const UINT64 media_id, const FILTER& info)
    {
        std::string filter_id = "";
        Util::NumToString(info.id, &filter_id);

        bool success = false;
        if (info.type == FILTER_AUDIO)
        {
            std::string audio_id = "";
            Util::NumToString(media_id, &audio_id);

            AUDIO_FILTER filter = std::get<AUDIO_FILTER>(info.filter);
            switch (filter.filterType)
            {
            case AUDIO_FILTER_SPEEX_NOISE_SUPPRESS:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSpeexNoiseSuppressAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter suppressLevel: " << filter.speexNoiseSuppressFilter.suppressLevel;
                success = sdk_controller_->CreateSpeexNoiseSuppressAudioFilter(audio_id, filter_id);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSpeexNoiseSuppressAudioFilter failed";
                    return false;
                }

                success = sdk_controller_->SpeexNoiseSuppressAudioFilterSetSuppressLevel(audio_id, filter_id, filter.speexNoiseSuppressFilter.suppressLevel);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SpeexNoiseSuppressAudioFilterSetSuppressLevel failed";
            }
            break;
            case AUDIO_FILTER_SAMI_NOISE_SUPPRESS:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSamiNoiseSuppressAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter speechRatio: " << filter.samiNoiseSuppressFilter.speechRatio;
                success = sdk_controller_->CreateSamiNoiseSuppressAudioFilter(audio_id, filter_id);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSamiNoiseSuppressAudioFilter failed";
                    return false;
                }

                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetModel(audio_id, filter_id, filter.samiNoiseSuppressFilter.configFile);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SamiNoiseSuppressAudioFilterSetModel failed";
                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetSpeechRatio(audio_id, filter_id, filter.samiNoiseSuppressFilter.speechRatio);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SamiNoiseSuppressAudioFilterSetSpeechRatio failed";
            }
            break;
            case AUDIO_FILTER_SAMI_COMMON_METRICS:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSamiCommonMetricsAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->CreateSamiCommonMetricsAudioFilter(audio_id, filter_id, filter.samiCommonMetricsFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSamiCommonMetricsAudioFilter failed";
            }
            break;
            case AUDIO_FILTER_SAMI_MDSP_EFFECT:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateSamiMdspEffectAudioFilter audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->CreateSamiMdspEffectAudioFilter(audio_id, filter_id, info.enable.has_value() ? info.enable.value() : false, filter.samiMdspEffectFilter);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateSamiMdspEffectAudioFilter failed";
                    return false;
                }

                success = sdk_controller_->SamiMdspEffectAudioFilterSetParam(audio_id, filter_id, filter.samiMdspEffectFilter.mdspParam);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] SamiMdspEffectAudioFilterSetParam failed";
            }
            break;
            default:
                break;
            }
        }
        else if (info.type == FILTER_VISUAL)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

			VISUAL_FILTER filter = std::get<VISUAL_FILTER>(info.filter);
			switch (filter.filterType)
			{
			case VISUAL_FILTER_CORNER:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateCornerVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", corner filter info: " << filter.cornerFilter.toString();
				success = sdk_controller_->CreateCornerVisualFilter(layer_id, filter_id, filter.cornerFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateCornerVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_EDGE:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateEdgeVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", edge filter info: " << filter.edgeFilter.toString();
				success = sdk_controller_->CreateEdgeVisualFilter(layer_id, filter_id, filter.edgeFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateEdgeVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_COLOR_ADJUST:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateColorAdjustVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", colorAdjust filter info: " << filter.colorAdjustFilter.toString();
				success = sdk_controller_->CreateMediaColorAdjustFilter(layer_id, filter_id, filter.colorAdjustFilter, FILTER_VISUAL);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateColorAdjustVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_CHROMA_KEY:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateChromaKeyVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", chromaKey filter info: " << filter.chromaKeyFilter.toString();
				success = sdk_controller_->CreateChromaKeyVisualFilter(layer_id, filter_id, filter.chromaKeyFilter);
				if (!success)
				{
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateChromaKeyVisualFilter failed";
					break;
				}
			}
			break;
			case VISUAL_FILTER_OVERLAY:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateOverlayVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", overlay filter info: " << filter.overlayFilter.toString();
				success = sdk_controller_->CreateOverlayVisualFilter(layer_id, filter_id, filter.overlayFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateOverlayVisualFilter failed";
			}
			break;
			case VISUAL_FILTER_HINT:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateHintVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", hint visual filter info: " << filter.hintFilter.toString();
				success = sdk_controller_->CreateHintVisualFilter(layer_id, filter_id, filter.hintFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateHintVisualFilter failed!";
			}
			break;
            case VISUAL_FILTER_SCALE:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateScaleVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", scale visual filter info: " << filter.scaleFilter.toString();
                success = sdk_controller_->CreateScaleVisualFilter(layer_id, filter_id, filter.scaleFilter);
                if (!success) {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateScaleVisualFilter failed";
                }
            }
            break;
            case VISUAL_FILTER_SHAPE:
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateShapeVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", shape visual filter info: " << filter.shapeFilter.toString();
                success = sdk_controller_->CreateShapeVisualFilter(layer_id, filter_id, filter.shapeFilter);
                if (!success) {
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateShapeVisualFilter failed";
                }
            }
            break;
			case VISUAL_FILTER_COLOR_LUT:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateColorLutVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", lut visual filter info: " << filter.colorLutFilter.toString();
				success = sdk_controller_->CreateColorLutVisualFilter(layer_id, filter_id, filter.colorLutFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateColorLutVisualFilter failed";
			}
			break;
			case VISUAL_FILTER_SHARPNESS:
			{
				LOG(INFO) << "[MediaMgr::AddFilter] CreateSharpnessVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", sharpness visual filter info: " << filter.sharpnessFilter.toString();
				success = sdk_controller_->CreateSharpnessVisualFilter(layer_id, filter_id, filter.sharpnessFilter);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] CreateSharpnessVisualFilter failed";
			}
			break;
			default:
				break;
			}

			if (info.enable.has_value())
			{
				LOG(INFO) << "[MediaMgr::AddFilter] VisualFilterSetActive layer_id: " << layer_id << ", filter_id: " << filter_id << ", active: " << info.enable.value();
				if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
				{
					success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, info.enable.value(), FILTER_VISUAL, "color_adjust");
				}
				else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
				{
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, info.enable.value(), FILTER_VISUAL, "chroma_key");
				}
				else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
				{
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, info.enable.value(), FILTER_VISUAL, "color_lut");
				}
				else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
				{
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, info.enable.value(), FILTER_VISUAL, "sharpness");
				}
				else
				{
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, info.enable.value(), FILTER_VISUAL);
				}

				if (!success)
					LOG(ERROR) << "[MediaMgr::AddFilter] VisualFilterSetActive chromaKey failed";
			}
        }
        else if (info.type == FILTER_EFFECT)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

            LAYER_INFO layerInfo{};
            ModeSceneMgr::GetInstance()->GetLayerInfoByID(media_id, &layerInfo);
            
            SOURCE_INFO sourceInfo{};
            SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

			EFFECT_FILTER filter = std::get<EFFECT_FILTER>(info.filter);
            if (!filter.composers.empty() || (!filter.keyPath.key.empty() && !filter.keyPath.val.empty()))
            {
                success = EnableEffect(layer_id, true);
                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::AddFilter] EffectEnable failed";
                    return false;
                }

                if (!filter.composers.empty())
                {
                    std::stringstream        ss;
                    std::vector<std::string> composers{};
                    for (const auto& comp : filter.composers)
                    {
                        composers.push_back(sdk_controller_->ComposerToStr(comp));
                        ss << "\n"
                           << comp.toString();
                    }
                    std::vector<std::string> tags{};
                    for (const auto& comp : filter.composers)
                    {
                        tags.push_back(comp.composerTag);
                    }

                    LOG(INFO) << "[MediaMgr::AddFilter] EffectComposerSet layer_id: " << layer_id << ", composers: " << ss.str();
                    success = sdk_controller_->EffectComposerSet(layer_id, composers, tags);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::AddFilter] EffectComposerAdd failed";
                }

                if (!filter.keyPath.key.empty() && !filter.keyPath.val.empty())
                {
                    LOG(INFO) << "[MediaMgr::AddFilter] EffectSetBkImage layer_id: " << layer_id << ", filter key: " << filter.keyPath.key << ", path: " << filter.keyPath.val;
                    success = sdk_controller_->EffectSetBkImage(layer_id, filter.keyPath.key, filter.keyPath.val);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::AddFilter] EffectSetBkImage failed";
                }
            }
        }
        else if (info.type == FILTER_CANVAS)
        {
            std::string canvas_id = "";
            Util::NumToString(media_id, &canvas_id);

            CANVAS_FILTER filter = std::get<CANVAS_FILTER>(info.filter);
            if (filter.filterType = CANVAS_FILTER_TYPE::CANVAS_FILTER_TRANSITION)
            {
                success = sdk_controller_->CreateTransition(canvas_id, filter_id, filter.transitionFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateTransition failed";
            }
            else if (filter.filterType == CANVAS_FILTER_TYPE::CANVAS_FILTER_COLOR_ADJUST)
            {
                LOG(INFO) << "[MediaMgr::AddFilter] CreateMediaColorAdjustFilter canvas_id: " << canvas_id << ", colorAdjustFilter: " << filter.colorAdjustFilter.toString();
                success = sdk_controller_->CreateMediaColorAdjustFilter(canvas_id, filter_id, filter.colorAdjustFilter, FILTER_CANVAS);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::AddFilter] CreateColorAdjustVisualFilter failed";
            }

            if (info.enable.has_value())
            {
                LOG(INFO) << "[MediaMgr::AddFilter] MediaFilterSetActive canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", active: " << info.enable.value();
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    success = sdk_controller_->MediaFilterSetActive(canvas_id, filter_id, info.enable.value(), FILTER_CANVAS, "color_adjust");
                }
            }
        }
        return success;
    }

    bool MediaMgr::RemoveFilter(const UINT64 media_id, const FILTER& info)
    {
        LOG(INFO) << "[MediaMgr::RemoveFilter] media_id: " << media_id << ", filter_id: " << info.id << ", filter_type: " << info.type;
        std::string filter_id = "";
        Util::NumToString(info.id, &filter_id);

        bool success = false;
        if (info.type == FILTER_AUDIO)
        {
			std::string audio_id = "";
			Util::NumToString(media_id, &audio_id);
            success = sdk_controller_->AudioFilterDestroy(audio_id, filter_id);
            if (!success)
                LOG(ERROR) << "[MediaMgr::RemoveFilter] AudioFilterDestroy failed";
        }
        else if (info.type == FILTER_VISUAL)
        {
			std::string layer_id = "";
			Util::NumToString(media_id, &layer_id);

            VISUAL_FILTER filter = std::get<VISUAL_FILTER>(info.filter);
            if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
            {
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL, "color_adjust");
            }
            else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
            {
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL, "chroma_key");
            }
            else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
            {
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL, "color_lut");
            }
            else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
            {
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL, "sharpness");
            }
            else
            {
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL);
            }
            if (!success)
                LOG(ERROR) << "[MediaMgr::RemoveFilter] VisualFilterDestroy failed";
        }
        else if (info.type == FILTER_CANVAS)
        {
            std::string canvas_id = "";
            Util::NumToString(media_id, &canvas_id);

            CANVAS_FILTER filter = std::get<CANVAS_FILTER>(info.filter);
            if (filter.filterType == CANVAS_FILTER_TRANSITION)
            {
                success = sdk_controller_->DestroyTransition(canvas_id, filter_id);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::RemoveFilter] DestroyTransition failed, canvasID: " << canvas_id << ", filter_id: " << filter_id;
            }
            else if (filter.filterType == CANVAS_FILTER_COLOR_ADJUST)
            {
                success = sdk_controller_->MediaFilterDestroy(canvas_id, filter_id, FILTER_CANVAS, "color_adjust");
                if (!success)
                    LOG(ERROR) << "[MediaMgr::RemoveFilter] MediaFilterDestroy failed, canvasID: " << canvas_id << ", filter_id: " << filter_id;
            }
        }

        return success;
    }

    bool MediaMgr::ControlFilter(const UINT64 media_id, const FILTER& filter_info, FILTER_CONTROL_CMD cmd)
    {
        std::string filter_id = "";
        Util::NumToString(filter_info.id, &filter_id);

        bool success = false;
        if (filter_info.type == FILTER_AUDIO)
        {
			std::string audio_id = "";
			Util::NumToString(media_id, &audio_id);

            AUDIO_FILTER filter = std::get<AUDIO_FILTER>(filter_info.filter);
            if (cmd & FILTER_CONTROL_SET_SUPPRESS_LEVEL)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SpeexNoiseSuppressAudioFilterSetSuppressLevel audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter suppressLevel: " << filter.speexNoiseSuppressFilter.suppressLevel;
                success = sdk_controller_->SpeexNoiseSuppressAudioFilterSetSuppressLevel(audio_id, filter_id, filter.speexNoiseSuppressFilter.suppressLevel);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SpeexNoiseSuppressAudioFilterSetSuppressLevel failed";
            }

            if (cmd & FILTER_CONTROL_SET_SPEECH_RATIO)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetSpeechRatio audio_id: " << audio_id << ", filter_id: " << filter_id << ", filter speechRatio: " << filter.samiNoiseSuppressFilter.speechRatio;
                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetSpeechRatio(audio_id, filter_id, filter.samiNoiseSuppressFilter.speechRatio);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetSpeechRatio failed";
            }

            if (cmd & FILTER_CONTROL_SET_MODEL)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetModel audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->SamiNoiseSuppressAudioFilterSetModel(audio_id, filter_id, filter.samiNoiseSuppressFilter.configFile);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiNoiseSuppressAudioFilterSetModel failed";
            }

            if (cmd & FILTER_CONTROL_RESET_COMMON_METRICS)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiCommonMetricsAudioFilterReset audio_id: " << audio_id << ", filter_id: " << filter_id;
                success = sdk_controller_->SamiCommonMetricsAudioFilterReset(audio_id, filter_id);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiCommonMetricsAudioFilterReset failed";
            }

            if (cmd & FILTER_CONTROL_SET_MDSP_PARAM)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SamiMdspEffectAudioFilterSetParam audio_id: " << audio_id << ", filter_id: " << filter_id << ", mdsp_param: " << filter.samiMdspEffectFilter.mdspParam;
                success = sdk_controller_->SamiMdspEffectAudioFilterSetParam(audio_id, filter_id, filter.samiMdspEffectFilter.mdspParam);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SamiMdspEffectAudioFilterSetParam failed";
            }

            if (cmd & FILTER_CONTROL_SET_FILTER_ENABLE && filter_info.enable.has_value())
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] AudioFilterSetEnable audio_id: " << audio_id << ", filter_id: " << filter_id << " enable: " << filter_info.enable.value();
                success = sdk_controller_->AudioFilterSetEnable(audio_id, filter_id, filter_info.enable.value());
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] AudioFilterSetEnable failed";
            }
        }
        else if (filter_info.type == FILTER_VISUAL)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

            VISUAL_FILTER filter = std::get<VISUAL_FILTER>(filter_info.filter);
            if (cmd & FILTER_CONTROL_SET_EDGE)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] CreateEdgeVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", edge filter info: " << filter.edgeFilter.toString();
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] VisualFilterDestroy failed";
                success = sdk_controller_->CreateEdgeVisualFilter(layer_id, filter_id, filter.edgeFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] CreateEdgeVisualFilter failed";
            }
            if (cmd & FILTER_CONTROL_SET_CORNER)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] CreateCornerVisualFilter layer_id: " << layer_id << ", filter_id: " << filter_id << ", corner filter info: " << filter.cornerFilter.toString();
                success = sdk_controller_->MediaFilterDestroy(layer_id, filter_id, FILTER_VISUAL);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] VisualFilterDestroy failed";
                success = sdk_controller_->CreateCornerVisualFilter(layer_id, filter_id, filter.cornerFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] CreateCornerVisualFilter failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SATURATION)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSaturation layer_id: " << layer_id << ", filter_id: " << filter_id << ", saturation: " << filter.colorAdjustFilter.saturation;
                success = sdk_controller_->SetColorFilterSaturation(layer_id, filter_id, filter.colorAdjustFilter.saturation, FILTER_VISUAL);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSaturation failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_HUE_SHIFT)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterHueShift layer_id: " << layer_id << ", filter_id: " << filter_id << " hueShift: " << filter.colorAdjustFilter.hueShift;
                success = sdk_controller_->SetColorFilterHueShift(layer_id, filter_id, filter.colorAdjustFilter.hueShift, FILTER_VISUAL);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterHueShift failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_ADD_COLOR)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterAddColor layer_id: " << layer_id << ", filter_id: " << filter_id << " addColor: " << filter.colorAdjustFilter.addColor;
                success = sdk_controller_->SetColorFilterAddColor(layer_id, filter_id, filter.colorAdjustFilter.addColor, FILTER_VISUAL);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterAddColor failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_MUL_COLOR)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterMulColor layer_id: " << layer_id << ", filter_id: " << filter_id << " mulColor: " << filter.colorAdjustFilter.mulColor;
                success = sdk_controller_->SetColorFilterMulColor(layer_id, filter_id, filter.colorAdjustFilter.mulColor, FILTER_VISUAL);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterMulColor failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_BRIGHTNESS)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterBrightness colorAdjust layer_id: " << layer_id << ", filter_id: " << filter_id << ", brightness: " << filter.colorAdjustFilter.brightness;
                    success = sdk_controller_->SetColorFilterBrightness(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.brightness, FILTER_VISUAL);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterBrightness chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << ", brightness: " << filter.chromaKeyFilter.brightness;
                    success = sdk_controller_->SetColorFilterBrightness(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.brightness, FILTER_VISUAL);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterBrightness failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_CONTRAST)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterContrast colorAdjust layer_id: " << layer_id << ", filter_id: " << filter_id << ", contrast: " << filter.colorAdjustFilter.contrast;
                    success = sdk_controller_->SetColorFilterContrast(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.contrast, FILTER_VISUAL);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterContrast chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << ", contrast: " << filter.chromaKeyFilter.contrast;
                    success = sdk_controller_->SetColorFilterContrast(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.contrast, FILTER_VISUAL);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterContrast failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_GAMMA)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterGamma colorAdjust visual_id: " << layer_id << ", filter_id: " << filter_id << " gamma: " << filter.colorAdjustFilter.gamma;
                    success = sdk_controller_->SetColorFilterGamma(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.gamma, FILTER_VISUAL);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterGamma chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << " gamma: " << filter.chromaKeyFilter.gamma;
                    success = sdk_controller_->SetColorFilterGamma(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.gamma, FILTER_VISUAL);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterGamma failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_OPACITY)
            {
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterOpacity colorAdjust layer_id: " << layer_id << ", filter_id: " << filter_id << ", opacity: " << filter.colorAdjustFilter.opacity;
                    success = sdk_controller_->SetColorFilterOpacity(layer_id, filter_id, "coloradjust", filter.colorAdjustFilter.opacity, FILTER_VISUAL);
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterOpacity chromaKey layer_id: " << layer_id << ", filter_id: " << filter_id << ", opacity: " << filter.chromaKeyFilter.opacity;
                    success = sdk_controller_->SetColorFilterOpacity(layer_id, filter_id, "chromakey", filter.chromaKeyFilter.opacity, FILTER_VISUAL);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterOpacity failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_CHROMA)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterChroma layer_id: " << layer_id << ", filter_id: " << filter_id << ", chroma: " << filter.chromaKeyFilter.chroma;
                success = sdk_controller_->SetColorFilterChroma(layer_id, filter_id, filter.chromaKeyFilter.chroma);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterChroma failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SIMILARITY)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSimilarity layer_id: " << layer_id << ", filter_id: " << filter_id << ", similarity: " << filter.chromaKeyFilter.similarity;
                success = sdk_controller_->SetColorFilterSimilarity(layer_id, filter_id, filter.chromaKeyFilter.similarity);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSimilarity failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SMOOTHNESS)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSmoothness layer_id: " << layer_id << ", filter_id: " << filter_id << ", smoothness: " << filter.chromaKeyFilter.smoothness;
                success = sdk_controller_->SetColorFilterSmoothness(layer_id, filter_id, filter.chromaKeyFilter.smoothness);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSmoothness failed";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_SPILL)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSpill layer_id: " << layer_id << ", filter_id: " << filter_id << ", spill: " << filter.chromaKeyFilter.spill;
                success = sdk_controller_->SetColorFilterSpill(layer_id, filter_id, filter.chromaKeyFilter.spill);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSpill failed";
            }
            if (cmd & FILTER_CONTROL_SET_OVERLAY)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] VisualSetBackground layer_id: " << layer_id << ", filter_id: " << filter_id << ", overlay filter info: " << filter.overlayFilter.toString();
                success = sdk_controller_->SetOverlayFilterProperty(layer_id, filter_id, filter.overlayFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetOverlayFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_HINT)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetHintFilterProperty layer_id: " << layer_id << ", filter_id: " << filter_id << ", hint visual filter info: " << filter.hintFilter.toString();
                success = sdk_controller_->SetHintFilterProperty(layer_id, filter_id, filter.hintFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetHintFilterProperty failed!";
            }
            if (cmd & FILTER_CONTROL_SET_COLOR_LUT)
            {
                success = sdk_controller_->SetColorLutFilterProperty(layer_id, filter_id, filter.colorLutFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorLutFilterProperty failed";

            }
            if (cmd & FILTER_CONTROL_SET_SHARPNESS)
            {
                success = sdk_controller_->SetSharpnessFilterProperty(layer_id, filter_id, filter.sharpnessFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetSharpnessFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_SCALE)
            {
                success = sdk_controller_->SetScaleFilterProperty(layer_id, filter_id, filter.scaleFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetScaleFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_SHAPE)
            {
                success = sdk_controller_->SetShapeFilterProperty(layer_id, filter_id, filter.shapeFilter);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] SetShapeFilterProperty failed";
            }
            if (cmd & FILTER_CONTROL_SET_FILTER_ENABLE && filter_info.enable.has_value())
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] VisualFilterSetActive layer_id: " << layer_id << ", filter_id: " << filter_id;
                if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
                {
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, filter_info.enable.value(), FILTER_VISUAL, "color_adjust");
                }
                else if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                {
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, filter_info.enable.value(), FILTER_VISUAL, "chroma_key");
                }
                else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
                {
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, filter_info.enable.value(), FILTER_VISUAL, "color_lut");
                }
                else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
                {
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, filter_info.enable.value(), FILTER_VISUAL, "sharpness");
                }
                else
                {
                    success = sdk_controller_->MediaFilterSetActive(layer_id, filter_id, filter_info.enable.value(), FILTER_VISUAL);
                }
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] VisualFilterSetActive failed";
            }
        }
        else if (filter_info.type == FILTER_EFFECT)
        {
            std::string layer_id = "";
            Util::NumToString(media_id, &layer_id);

            EFFECT_FILTER filter = std::get<EFFECT_FILTER>(filter_info.filter);
            if (cmd & FILTER_CONTROL_ADD_COMPOSER)
            {
                if (!filter.composers.empty())
                {
                    success = EnableEffect(layer_id, true);
                    if (!success)
                    {
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                        return false;
                    }
                }

                std::stringstream        ss;
                std::vector<std::string> add_composers{};
                for (const auto& comp : filter.addComposers)
                {
                    add_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss << "\n" << comp.toString();
                }
                std::vector<std::string> add_composers_tag{};
                for (const auto& comp : filter.addComposers)
                {
                    add_composers_tag.push_back(comp.composerTag);
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerAdd layer_id: " << layer_id << ", filter_id: " << filter_id << ", add composers: " << ss.str();
                success = sdk_controller_->EffectComposerAdd(layer_id, add_composers, add_composers_tag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerAdd failed";
            }
            if (cmd & FILTER_CONTROL_REMOVE_COMPOSER)
            {
                if (filter.composers.empty())
                {
                    success = EnableEffect(layer_id, false);
                    if (!success)
                    {
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                        return false;
                    }
                }

                std::stringstream        ss;
                std::vector<std::string> remove_composers{};
                for (const auto& comp : filter.removeComposers)
                {
                    remove_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss << "\n" << comp.toString();
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerRemove layer_id: " << layer_id << ", filter_id: " << filter_id << ", remove composers: " << ss.str();
                success = sdk_controller_->EffectComposerRemove(layer_id, remove_composers);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerRemove failed";
            }
            if (cmd & FILTER_CONTROL_SET_COMPOSER)
            {
                if (!filter.composers.empty())
                {
                    success = EnableEffect(layer_id, true);
                }
                else
                {
                    success = EnableEffect(layer_id, false);
                }

                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed, enable: " << 1;
                    return false;
                }

                std::stringstream        ss;
                std::vector<std::string> reset_composers{};
                for (const auto& comp : filter.composers)
                {
                    reset_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss << "\n" << comp.toString();
                }
                std::vector<std::string> reset_composers_tag{};
                for (const auto& comp : filter.composers)
                {
                    reset_composers_tag.push_back(comp.composerTag);
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerSet layer_id: " << layer_id << ", filter_id: " << filter_id << ", reset composers: " << ss.str();
                success = sdk_controller_->EffectComposerSet(layer_id, reset_composers, reset_composers_tag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerSet failed";
            }
            if (cmd & FILTER_CONTROL_UPDATE_COMPOSER)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerSet layer_id: " << layer_id << ", filter_id: " << filter_id << ", update composer: " << filter.updateComposer.toString();
                bool val = false;
                Util::StringToNum(filter.updateComposer.keyVal.val, &val);
                string updateComposerTag = filter.updateComposer.composerTag;

                success = sdk_controller_->EffectComposerUpdate(layer_id, filter.updateComposer.effectPath, filter.updateComposer.keyVal.key, val, updateComposerTag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerUpdate failed";
            }
            if (cmd & FILTER_CONTROL_REPLACE_COMPOSERS)
            {
                if (!filter.composers.empty())
                {
                    success = EnableEffect(layer_id, true);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                }

                std::stringstream        ss1;
                std::vector<std::string> old_composers{};
                for (const auto& comp : filter.oldComposers)
                {
                    old_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss1 << "\n"
                        << comp.toString();
                }

                std::stringstream        ss2;
                std::vector<std::string> new_composers{};
                for (const auto& comp : filter.newComposers)
                {
                    new_composers.push_back(sdk_controller_->ComposerToStr(comp));
                    ss2 << "\n"
                        << comp.toString();
                }
                std::vector<std::string> new_composers_tag{};
                for (const auto& comp : filter.newComposers)
                {
                    new_composers_tag.push_back(comp.composerTag);
                }

                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerReplace visual_id: " << media_id << ", filter_id: " << filter_id << ", old composers: " << ss1.str() << ", new composers: " << ss2.str();
                success = sdk_controller_->EffectComposerReplace(layer_id, old_composers, new_composers, new_composers_tag);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerReplace failed";
            }
            if (cmd & FILTER_CONTROL_SET_COMPOSER_TEXT)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectComposerSetText layer_id: " << layer_id << ", filter_id: " << filter_id << ", set composer text key: " << filter.keyText.key << ", val: " << filter.keyText.val;
                success = sdk_controller_->EffectComposerSetText(layer_id, filter.keyText.key, filter.keyText.val);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectComposerSetText failed";
            }
            if (cmd & FILTER_CONTROL_SET_BACKGROUND_IMAGE)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectSetBkImage layer_id: " << layer_id << ", filter_id: " << filter_id << ", set bkImage key: " << filter.keyPath.key << ", val: " << filter.keyPath.val;
                if (!filter.keyPath.key.empty() && !filter.keyPath.val.empty())
                {
                    success = EnableEffect(layer_id, true);
                    if (!success)
                    {
                        LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                        return false;
                    }
                }

                success = sdk_controller_->EffectSetBkImage(layer_id, filter.keyPath.key, filter.keyPath.val);
                filter.result = success;
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectSetBkImage failed";
            }
            if (cmd & FILTER_CONTROL_SET_BRIGHT_CONFIG)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] EffectSetPicQualityBrightness layer_id: " << layer_id << ", filter_id: " << filter_id << ", brightConfig enable: " << filter.brightConfig.enable << ", isAuto: " << filter.brightConfig.isAuto << ", assetPath: " << filter.brightConfig.assetPath << ", key: " << filter.brightConfig.keyVal.key << ", val: " << filter.brightConfig.keyVal.val;
                if (filter.brightConfig.enable)
                {
                    success = EnableEffect(layer_id, true);
                }
                else if (filter.composers.empty())
                {
                    success = EnableEffect(layer_id, false);
                }

                if (!success)
                {
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectEnable failed";
                    return false;
                }

                success = sdk_controller_->EffectSetPicQualityBrightness(layer_id, filter.brightConfig);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlFilter] EffectSetPicQualityBrightness failed";
            }
            if (cmd & FILTER_CONTROL_SET_EFFECT_MSG)
            {
				LOG(INFO) << "[MediaMgr::ControlFilter] EffectSetMsg layer_id: " << layer_id << ", filter_id: " << filter_id << ", msg_id: " << filter.effectMsg.msgID << ", arg1: " << filter.effectMsg.arg1 << ", arg2: " << filter.effectMsg.arg2 << ", arg3: " << filter.effectMsg.arg3;
				success = sdk_controller_->EffectSetMsg(layer_id, filter.effectMsg);
				if (!success)
					LOG(ERROR) << "[MediaMgr::ControlFilter] EffectSetMsg failed";
            }
        }
        else if (filter_info.type == FILTER_CANVAS)
        {
            std::string canvas_id = "";
            Util::NumToString(media_id, &canvas_id);

            CANVAS_FILTER filter = std::get<CANVAS_FILTER>(filter_info.filter);
            if (filter.filterType == CANVAS_FILTER_TRANSITION)
            {
                LOG(INFO) << "[MediaMgr::ControlFilter] SetTransitionProperty canvas_id: " << canvas_id << ", filter_id: " << filter_id;
                success = sdk_controller_->SetTransitionProperty(canvas_id, filter_id, filter.transitionFilter);
                if (!success)
                    LOG(ERROR) << "[[MediaMgr::ControlFilter] SetTransitionProperty failed";
            }
            else if (filter.filterType == CANVAS_FILTER_COLOR_ADJUST)
            {
                if (cmd & FILTER_CONTROL_SET_COLOR_BRIGHTNESS)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterBrightness canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", brightness: " << filter.colorAdjustFilter.brightness;
                    success = sdk_controller_->SetColorFilterBrightness(canvas_id, filter_id, "color_adjust", filter.colorAdjustFilter.brightness, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterBrightness failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_CONTRAST)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterContrast colorAdjust canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", contrast: " << filter.colorAdjustFilter.contrast;
                    success = sdk_controller_->SetColorFilterContrast(canvas_id, filter_id, "color_adjust", filter.colorAdjustFilter.contrast, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterContrast failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_GAMMA)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterGamma colorAdjust canvas_id: " << canvas_id << ", filter_id: " << filter_id << " gamma: " << filter.colorAdjustFilter.gamma;
                    success = sdk_controller_->SetColorFilterGamma(canvas_id, filter_id, "color_adjust", filter.colorAdjustFilter.gamma, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterGamma failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_OPACITY)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterOpacity colorAdjust canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", opacity: " << filter.colorAdjustFilter.opacity;
                    success = sdk_controller_->SetColorFilterOpacity(canvas_id, filter_id, "color_adjust", filter.colorAdjustFilter.opacity, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterOpacity failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_SATURATION)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterSaturation canvas_id: " << canvas_id << ", filter_id: " << filter_id << ", saturation: " << filter.colorAdjustFilter.saturation;
                    success = sdk_controller_->SetColorFilterSaturation(canvas_id, filter_id, filter.colorAdjustFilter.saturation, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterSaturation failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_HUE_SHIFT)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterHueShift canvas_id: " << canvas_id << ", filter_id: " << filter_id << " hueShift: " << filter.colorAdjustFilter.hueShift;
                    success = sdk_controller_->SetColorFilterHueShift(canvas_id, filter_id, filter.colorAdjustFilter.hueShift, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterHueShift failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_ADD_COLOR)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterAddColor canvas_id: " << canvas_id << ", filter_id: " << filter_id << " addColor: " << filter.colorAdjustFilter.addColor;
                    success = sdk_controller_->SetColorFilterAddColor(canvas_id, filter_id, filter.colorAdjustFilter.addColor, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterAddColor failed";
                }
                if (cmd & FILTER_CONTROL_SET_COLOR_MUL_COLOR)
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] SetColorFilterMulColor canvas_id: " << canvas_id << ", filter_id: " << filter_id << " mulColor: " << filter.colorAdjustFilter.mulColor;
                    success = sdk_controller_->SetColorFilterMulColor(canvas_id, filter_id, filter.colorAdjustFilter.mulColor, FILTER_CANVAS);
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] SetColorFilterMulColor failed";
                }
                if (cmd & FILTER_CONTROL_SET_FILTER_ENABLE && filter_info.enable.has_value())
                {
                    LOG(INFO) << "[MediaMgr::ControlFilter] CanvasFilterSetActive canvas_id: " << canvas_id << ", filter_id: " << filter_id;
                    success = sdk_controller_->MediaFilterSetActive(canvas_id, filter_id, filter_info.enable.value(), FILTER_CANVAS, "color_adjust");
                    if (!success)
                        LOG(ERROR) << "[MediaMgr::ControlFilter] CanvasFilterSetActive failed";
                }
            }
        }
        return success;
    }

    bool MediaMgr::GetFilterInfo(const std::string& filter_id, FILTER* info, FILTER_INFO_CMD cmd)
    {
        if (info->mediaIDs.empty())
            return false;

        bool success = false;
        if (info->type == FILTER_AUDIO)
        {
            std::string audio_id = "";
            Util::NumToString(info->mediaIDs[0], &audio_id);

            AUDIO_FILTER filter = std::get<AUDIO_FILTER>(info->filter);
            if (cmd & FILTER_INFO_SUPPRESS_LEVEL)
            {
                success = sdk_controller_->SpeexNoiseSuppressAudioFilterGetSuppressLevel(audio_id, filter_id, &filter.speexNoiseSuppressFilter.suppressLevel);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::GetFilterInfo] SpeexNoiseSuppressAudioFilterGetSuppressLevel failed";
            }
            if (cmd & FILTER_INFO_GET_ENABLE)
            {
                success = sdk_controller_->AudioFilterGetEnable(audio_id, filter_id, &info->enable.value());
                if (!success)
                    LOG(ERROR) << "[MediaMgr::GetFilterInfo] AudioFilterGetEnable failed";
            }
            info->filter = filter;
        }
        else if (info->type == FILTER_EFFECT)
        {
            std::string layer_id = "";
            Util::NumToString(info->mediaIDs[0], &layer_id);

            EFFECT_FILTER filter = std::get<EFFECT_FILTER>(info->filter);
            if (cmd & FILTER_INFO_COMPOSER_EXCLUSION)
            {
                success = sdk_controller_->EffectComposerGetExclusion(layer_id, filter.pathTag.key, filter.pathTag.val, &filter.exclusion);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::GetFilterInfo] EffectComposerGetExclusion failed";
            }
            info->filter = filter;
            LOG(INFO) << "[MediaMgr::GetFilterInfo] effect filter exclusion: " << filter.exclusion;
        }

        return success;
    }

    bool MediaMgr::AddLayer(const uint64_t canvas_id, const LAYER_INFO& layerInfo)
    {
        std::string layer_id = "";
        Util::NumToString(layerInfo.id, &layer_id);

        std::string canvas_id_str = "";
        Util::NumToString(canvas_id, &canvas_id_str);

        std::string source_id = "";
        Util::NumToString(layerInfo.sourceID, &source_id);

        bool success = false;
        SOURCE_INFO sourceInfo{};
        SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);

        VISUAL_TYPE type = SourceMgr::GetInstance()->GetCompositeRealType(sourceInfo.id);
        if (type == VISUAL_CAMERA || layerInfo.testEffectFilter)
        {
            bool       isFallbackImage = false;
            LAYER_INFO layer_info = layerInfo;
			TRANSFORM& transform = layer_info.transform;
			if (sourceInfo.type == VISUAL_IMAGE)
			{
				auto& meta = SourceMgr::GetInstance()->GetCompositeMetas();
				if (meta[sourceInfo.id].isFallback)
				{
					IMAGE_SOURCE image = std::get<IMAGE_SOURCE>(sourceInfo.source);
					transform.scale.X = layerInfo.transform.scale.X * layerInfo.transform.size.Width / image.materialDesc.size.Width;
					transform.scale.Y = layerInfo.transform.scale.Y * layerInfo.transform.size.Height / image.materialDesc.size.Height;
					transform.angle = image.materialDesc.angle;
					transform.size.Width = image.materialDesc.size.Width;
					transform.size.Height = image.materialDesc.size.Height;
                    isFallbackImage = true;
				}
			}

            success = sdk_controller_->CreateLayerWithFilter(layer_id, canvas_id_str, source_id, layer_info, type == VISUAL_CAMERA);
            if (isFallbackImage)
            {
                sdk_controller_->MediaFilterDestroy(layer_id, "filter", FILTER_VISUAL, "");
            }
        }
        else if (type == VISUAL_VIRTUAL_CAMERA)
        {
            VIRTUAL_CAMERA_SOURCE virtual_camera = std::get<VIRTUAL_CAMERA_SOURCE>(sourceInfo.source);
            success = sdk_controller_->CreateVirtualCameraLayer(layer_id, virtual_camera);
        }
        else
        {
            success = sdk_controller_->CreateLayer(layer_id, canvas_id_str, source_id, layerInfo);
			if (sourceInfo.type == VISUAL_GRAFFITI)
			{
                GRAFFITI_SOURCE graffiti = std::get<GRAFFITI_SOURCE>(sourceInfo.source);
                success = sdk_controller_->SetLayerNeedDrawBorder(layer_id, false);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddLayer] SetLayerNeedDrawBorder failed";
                success = sdk_controller_->GraffitiSourceSetEditState(layer_id, graffiti.editable);
				if (!success)
					LOG(ERROR) << "[MediaMgr::AddLayer] GraffitiSourceSetEditState failed";
			}
        }

        if (type == VISUAL_VIRTUAL_CAMERA)
        {
            return success;
        }

        if (success)
        {
            if (!sdk_controller_->LayerSetVisible(layer_id, layerInfo.show))
                LOG(ERROR) << "[MediaMgr::AddLayer] LayerSetVisible failed";
            if (!sdk_controller_->LayerSetLock(layer_id, layerInfo.locked))
                LOG(ERROR) << "[MediaMgr::AddLayer] LayerSetLock failed";
            if (!SetLayerFlag(layerInfo.visualFlags, layer_id))
                LOG(ERROR) << "[MediaMgr::AddLayer] SetLayerFlag failed";
        }
        else
        {
            LOG(ERROR) << "[MediaMgr::AddLayer] CreateLayer failed";
            sdk_controller_->DestroyLayer(layer_id);
        }

        VISUAL_SOURCE_RESULT result = sourceInfo.result;
		if (type == VISUAL_CAMERA && sourceInfo.type == VISUAL_IMAGE)
		{
            OnLayerCreated(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL, result.errorCode, result.type, result.reason);
            OnLayerFallback(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL, result.errorCode, result.type, result.reason, true);
		}
		else
		{
            OnLayerCreated(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL,  result.errorCode, result.type, result.reason);
			OnLayerFallback(layer_id, result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL,  result.errorCode, result.type, result.reason, false);
		}

        return success;
    }

    bool MediaMgr::RemoveLayer(const uint64_t layer_id)
    {
        LOG(INFO) << "[MediaMgr::RemoveLayer] layer_id: " << layer_id;

        LAYER_INFO layerInfo;
        ModeSceneMgr::GetInstance()->GetLayerInfoByID(layer_id, &layerInfo);

        std::string layer_id_str = "";
        Util::NumToString(layer_id, &layer_id_str);
        bool success = sdk_controller_->DestroyLayer(layer_id_str);
        if (!success)
        {
            LOG(WARNING) << "[MediaMgr::RemoveLayer] DestroyLayer failed, layer_id: " << layer_id;
            return false;
        }

        for (auto& pair : visuals_flag_map_)
        {
            auto& layer_ids = pair.second;
            layer_ids.erase(std::remove(layer_ids.begin(), layer_ids.end(), layer_id_str), layer_ids.end());
        }

        OnLayerDeleted(layer_id_str);

        // Internal RemoveLayer to Check Destroy Source
        if (SourceMgr::GetInstance()->CheckSourceExist(layerInfo.sourceID))
        {
            SOURCE_INFO sourceInfo;
            SourceMgr::GetInstance()->GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);
            if (sourceInfo.layerIDs.size() == 1 && sourceInfo.layerIDs[0] == layer_id)
            {
                SourceMgr::GetInstance()->DestroySource(layerInfo.sourceID);
            }
        }

        return success;
    }

    bool MediaMgr::ControlLayer(const LAYER_INFO& layer_info, LAYER_CONTROL_CMD cmd)
    {
        std::string canvas_id = "";
        Util::NumToString(layer_info.canvasID, &canvas_id);

        std::string layer_id = "";
        Util::NumToString(layer_info.id, &layer_id);

        bool        success = false;
        if (cmd & LAYER_CONTROL_SET_CLIP_MASK)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerClipMask layer_id: " << layer_id;
            success = sdk_controller_->LayerClipMask(layer_id);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerClipMask failed";
        }

        if (cmd & LAYER_CONTROL_SET_SHOW)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetVisible layer_id: " << layer_id << ", show: " << layer_info.show;
            success = sdk_controller_->LayerSetVisible(layer_id, layer_info.show);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetVisible failed";
        }

        if (cmd & LAYER_CONTROL_SET_LOCK)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetLock layer_id: " << layer_id << ", locked: " << layer_info.locked;
            success = sdk_controller_->LayerSetLock(layer_id, layer_info.locked);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetLock failed";
        }

        if (cmd & LAYER_CONTROL_SET_MOVE_RANGE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetMoveRange layer_id: " << layer_id << ", moveRange: " << layer_info.moveRange.toString();
            success = sdk_controller_->LayerSetMoveRange(layer_id, layer_info.moveRange.x, layer_info.moveRange.y, layer_info.moveRange.z, layer_info.moveRange.w);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetMoveRange failed";
        }

        TRANSFORM trans = layer_info.transform;
        SOURCE_INFO sourceInfo{};
        SourceMgr::GetInstance()->GetSourceInfoByID(layer_info.sourceID, &sourceInfo);
        if (cmd & LAYER_CONTROL_SET_TRANSLATE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetPosition layer_id: " << layer_id << ", translate=[" << trans.translate.X << "," << trans.translate.Y << "]";
            success = sdk_controller_->LayerSetPosition(layer_id, trans.translate.X, trans.translate.Y);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetPosition failed";
        }
        if (cmd & LAYER_CONTROL_SET_FLIPH)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetHorizontalFlip layer_id: " << layer_id << ", hFlip: " << trans.hFlip;
            if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                success = sdk_controller_->SetVirtualCameraFlipH(trans.hFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] SetVirtualCameraFlipH failed";
            }
            else
            {
                success = sdk_controller_->LayerSetHorizontalFlip(layer_id, trans.hFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetHorizontalFlip failed";
            }
        }
        if (cmd & LAYER_CONTROL_SET_FLIPV)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetVerticalFlip layer_id: " << layer_id << ", vFlip: " << trans.vFlip;
            if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                success = sdk_controller_->SetVirtualCameraFlipV(trans.vFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] SetVirtualCameraFlipV failed";
            }
            else
            {
                success = sdk_controller_->LayerSetVerticalFlip(layer_id, trans.vFlip);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetVerticalFlip failed";
            }
        }
        if (cmd & LAYER_CONTROL_SET_ROTATE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetRotate layer_id: " << layer_id << ", angle: " << trans.angle;
            if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
            {
                success = sdk_controller_->SetVirtualCameraRotate(trans.angle);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] SetVirtualCameraRotate failed";
            }
            else
            {
                if (sourceInfo.type == VISUAL_FAV)
                {
                    FAV_SOURCE fav = std::get<FAV_SOURCE>(sourceInfo.source);
                    trans.angle += fav.materialDesc.angle;
                }
                else if (sourceInfo.type == VISUAL_IMAGE)
                {
                    IMAGE_SOURCE image = std::get<IMAGE_SOURCE>(sourceInfo.source);
                    trans.angle += image.materialDesc.angle;
                }

                trans.angle = fmod(trans.angle, 360);
                trans.angle = trans.angle < 0.0f ? trans.angle + 360.0f : trans.angle;
                success = sdk_controller_->LayerSetRotate(layer_id, trans.angle);
                if (!success)
                    LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetRotate failed";
            }
        }
        if (cmd & LAYER_CONTROL_SET_SCALE)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetScale layer_id: " << layer_id << ", scale=[" << trans.scale.X << "," << trans.scale.Y << "]";
            success = sdk_controller_->LayerSetScale(layer_id, trans.scale.X, trans.scale.Y);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetScale failed";
        }

        float preview_width = .0f, preview_height = .0f;
        if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(layer_info.canvasID))
        {
            CANVAS_INFO_EX canvas_info_ex;
            pCanvas->GetCanvasInfo(&canvas_info_ex);

            PREVIEW_INFO preview_info;
            ModeSceneMgr::GetInstance()->GetPreviewInfoByID(canvas_info_ex.previewID, &preview_info);
            preview_width = preview_info.layoutRect.Width;
            preview_height = preview_info.layoutRect.Height;
        }
        if (cmd & LAYER_CONTROL_SET_MIN_SCALE)
        {
            float newMinScale = .0f;
            if (trans.minScale.X > EPS)
            {
                newMinScale = preview_width * trans.minScale.X / layer_info.transform.size.Width;
            }
            else if (trans.minScale.Y > EPS)
            {
                newMinScale = preview_height * trans.minScale.Y / layer_info.transform.size.Height;
            }

            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetMinScale layer_id: " << layer_id << "], minScale=[" << trans.minScale.X << "," << trans.minScale.Y << "], newMinScale: " << newMinScale;
            success = sdk_controller_->LayerSetMinScale(layer_id, newMinScale, newMinScale);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetMinScale failed";
        }
        if (cmd & LAYER_CONTROL_SET_MAX_SCALE)
        {
            float newMaxScale = .0f;
            if (trans.maxScale.X > EPS)
            {
                newMaxScale = preview_width * trans.maxScale.X / layer_info.transform.size.Width;
            }
            else if (trans.minScale.Y > EPS)
            {
                newMaxScale = preview_height * trans.maxScale.Y / layer_info.transform.size.Height;
            }

            LOG(INFO) << "[MediaMgr::ControlLayer] VisualSetMaxScale layer_id: " << layer_id << "], maxScale=[" << trans.maxScale.X << "," << trans.maxScale.Y << "], newMaxScale: " << newMaxScale;
            success = sdk_controller_->LayerSetMaxScale(layer_id, newMaxScale, newMaxScale);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetMaxScale failed";
        }
        if (cmd & LAYER_CONTROL_SET_CLIP)
        {
            LOG(INFO) << "[MediaMgr::ControlLayer] LayerSetClip layer_id: " << layer_id << ", clipRange: " << trans.clipRange.toString();
            success = sdk_controller_->LayerSetClip(layer_id, trans.clipRange.x, trans.clipRange.y, trans.clipRange.z, trans.clipRange.w);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] LayerSetClip failed";
        }
        if (cmd & LAYER_CONTROL_SET_VISUAL_FLAGS)
        {
            success = SetLayerFlag(layer_info.visualFlags, layer_id);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] SetLayerFlag failed, layer_id: " << layer_id;
        }
        if (cmd & LAYER_CONTROL_SET_PREPARE_CLIP)
        {
            TRANSFORM prepare_trans;
            prepare_trans.clipRange.x = layer_info.prepareClip.x * layer_info.transform.size.Width;
            prepare_trans.clipRange.y = layer_info.prepareClip.y * layer_info.transform.size.Height;
            prepare_trans.clipRange.z = layer_info.prepareClip.z * layer_info.transform.size.Width;
            prepare_trans.clipRange.w = layer_info.prepareClip.w * layer_info.transform.size.Height;
            success = sdk_controller_->CameraSourceSetPrepareTransform(layer_id, prepare_trans);
            if (!success)
                LOG(ERROR) << "[MediaMgr::ControlLayer] CameraSourceSetPrepareTransform failed";

            LOG(INFO) << "[MediaMgr::ControlLayer] CameraSourceSetPrepareTransform layer_id: " << layer_id << ", prepareClip: " << layer_info.prepareClip.toString() << ", rect=[" << layer_info.transform.size.Width << "," << layer_info.transform.size.Height << "], prepare_trans clip=[" << prepare_trans.clipRange.x << "," << prepare_trans.clipRange.y << "," << prepare_trans.clipRange.z << "," << prepare_trans.clipRange.w << "]";
        }

        return success;
    }

    bool MediaMgr::SetLayerFlag(std::vector<VISUAL_FLAG> flags, const std::string& layer_id)
    {
        if (flags.empty())
        {
            LOG(INFO) << "[MediaMgr::SetLayerFlag] flags is empty";
            return true;
        }

        bool success = true;
        std::set<VISUAL_FLAG> input_flags(flags.begin(), flags.end());
        bool only_no_flag = (input_flags.size() == 1 && input_flags.count(VISUAL_FLAG_NO_FLAG));
        
        for (int i = 1; i < static_cast<int>(VISUAL_FLAG_MAX); ++i)
        {
            VISUAL_FLAG flag = static_cast<VISUAL_FLAG>(i);
            bool should_have = input_flags.count(flag);
            if (only_no_flag) should_have = false;

            auto& layer_ids = visuals_flag_map_[flag];
            auto it = std::find(layer_ids.begin(), layer_ids.end(), layer_id);
            bool currently_has = (it != layer_ids.end());

            if (should_have && !currently_has) 
            {
                layer_ids.push_back(layer_id);
            }
            else if (!should_have && currently_has) 
            {
                layer_ids.erase(it);
            } 
            else 
            {
                continue;
            }

            switch (flag) 
            {
                case VISUAL_FLAG_ALWAYS_TOP:
                    success = sdk_controller_->LayerSetAlwaysOnTop(layer_id, should_have);
                    break;
                case VISUAL_FLAG_OUTPUT_FILTER:
                    success = sdk_controller_->LayerSetAvoidOutput(layer_id, should_have);
                    break;
                case VISUAL_FLAG_RTC_NOT_OUTPUT:
                    success = sdk_controller_->LayersSetExclude(layer_ids, mediasdk::StreamIndex::kStreamIndexMain);
                    break;
                case VISUAL_FLAG_RTC_NOT_OUTPUT_TO_SCREEN:
                    success = sdk_controller_->LayersSetExclude(layer_ids, mediasdk::StreamIndex::kStreamIndexScreen);
                    break;
                default:
                    break;
            }
        }

        return success;
    }

    bool MediaMgr::InitEffectPlatform(INIT_EFFECT_PLATFORM initEffect)
    {
        bool success = sdk_controller_->EffectPlatformInit(initEffect);
        if (!success)
            LOG(ERROR) << "[MediaMgr::TryInitEffectPlatform] EffectPlatformInit failed";
        return success;
    }

    bool MediaMgr::UpdateEffectConfig(const std::string& user_id, const std::string& ttls_hardware_level)
    {
        bool success = sdk_controller_->EffectPlatformUpdateConfig(user_id, ttls_hardware_level);
        if (!success)
            LOG(ERROR) << "[MediaMgr::UpdateConfig] EffectPlatformUpdateConfig failed";
        return success;
    }

    bool MediaMgr::CreateFullScreenDetector(const std::string& detector_id)
    {
        LOG(INFO) << "[MediaMgr::CreateFullScreenDetector] detector_id: " << detector_id;
        bool success = full_screen_detector_.CreateDetector(detector_id);
        if (!success)
            LOG(ERROR) << "[MediaMgr::CreateFullScreenDetector] CreateDetector failed";

        return success;
    }

    bool MediaMgr::DestroyFullScreenDetector(const std::string& detector_id)
    {
        LOG(INFO) << "[MediaMgr::DestroyFullScreenDetector] detector_id: " << detector_id;
        full_screen_detector_.DestroyDetector(detector_id);
        return true;
    }

    bool MediaMgr::SetFullScreenDetectorIgnoreProcessList(const std::vector<std::string>& exe_names)
    {
        full_screen_detector_.SetIgnoreProcessList(exe_names);
        return true;
    }

    bool MediaMgr::StartColorPicker(HWND hwnd)
    {
        color_picker_watch_.StartColorPickerWatch(StringToWString(app_work_dir_, CP_UTF8), hwnd);
        return true;
    }

    bool MediaMgr::VideoQualityManagerInitialize(const VideoQualityManagerInitializeParam& param, VideoQualityManagerGoLiveParamsOut* default_go_live_params)
    {
        bool success = sdk_controller_->VideoQualityManagerInitialize(param, default_go_live_params);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerInitialize] VideoQualityManagerInitialize failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryCameraRecommendedParams(const VideoQualityManagerQueryCameraRecommendedParamsRequest& request, VideoQualityManagerQueryCameraRecommendedParamsResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryCameraRecommendedParams]";
        bool success = sdk_controller_->VideoQualityManagerQueryCameraRecommendedParams(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryCameraRecommendedParams] VideoQualityManagerQueryCameraRecommendedParams failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryCameraBestParamsForTarget(const VideoQualityManagerQueryCameraBestParamsForTargetRequest& request, VideoQualityManagerQueryCameraBestParamsForTargetResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryCameraBestParamsForTarget]";
        bool success = sdk_controller_->VideoQualityManagerQueryCameraBestParamsForTarget(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryCameraBestParamsForTarget] VideoQualityManagerQueryCameraBestParamsForTarget failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryGoLiveRecommendedParams(const VideoQualityManagerQueryGoLiveRecommendedParamsRequest& request, VideoQualityManagerQueryGoLiveRecommendedParamsResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryGoLiveRecommendedParams]";
        bool success = sdk_controller_->VideoQualityManagerQueryGoLiveRecommendedParams(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryGoLiveRecommendedParams] VideoQualityManagerQueryGoLiveRecommendedParams failed";
        return success;
    }

    bool MediaMgr::VideoQualityManagerQueryManuallySelectedResult(const VideoQualityManagerQueryManuallySelectedResultRequest& request, VideoQualityManagerQueryManuallySelectedResultResponse* response)
    {
        LOG(INFO) << "[MediaMgr::VideoQualityManagerQueryManuallySelectedResult]";
        bool success = sdk_controller_->VideoQualityManagerQueryManuallySelectedResult(request, response);
        if (!success)
            LOG(ERROR) << "[MediaMgr::VideoQualityManagerQueryManuallySelectedResult] VideoQualityManagerQueryManuallySelectedResult failed";
        return success;
    }

	bool MediaMgr::ReconfigVideoOutput(const VideoOutputParamsRequest& request, VideoOutputParamsResponse* response)
	{
		LOG(INFO) << "[MediaMgr::ReconfigVideoOutput]";
		bool success = sdk_controller_->ReconfigVideoOutput(request, response);
		if (!success)
			LOG(ERROR) << "[MediaMgr::ReconfigVideoOutput] ReconfigVideoOutput failed";
		return success;
	}

	bool MediaMgr::FallbackVideoEncoder(const FallbackVideoEncoderParamsRequest& request)
	{
		LOG(INFO) << "[MediaMgr::FallbackVideoEncoder]";
		bool success = sdk_controller_->FallbackVideoEncoder(request);
		if (!success)
			LOG(ERROR) << "[MediaMgr::ReconfigVideoOutput] ReconfigVideoOutput failed";
		return success;
	}

	bool MediaMgr::SetPreprocessDefaultSize(const std::string& visual_id, int cx, int cy)
	{
		bool success = sdk_controller_->SetPreprocessDefaultSize(visual_id, cx, cy);
		if (!success)
			LOG(ERROR) << "[MediaMgr::SetPreprocessDefaultSize] SetPreprocessDefaultSize failed";
		return success;
	}

	bool MediaMgr::RemovePreprocessDefaultSize(const std::string& visual_id)
	{
		bool success = sdk_controller_->RemovePreprocessDefaultSize(visual_id);
		if (!success)
			LOG(ERROR) << "[MediaMgr::RemovePreprocessDefaultSize] RemovePreprocessDefaultSize failed";
		return success;
	}

    bool MediaMgr::StartAdaptiveGearStrategyReport(const std::string& stream_id, const std::string& abr_config)
    {
        LOG(INFO) << "[MediaMgr::StartAdaptiveGearStrategyReport]";
        bool success = sdk_controller_->StartAdaptiveGearStrategyReport(stream_id, abr_config);
        if (!success)
            LOG(ERROR) << "[MediaMgr::StartAdaptiveGearStrategyReport] StartAdaptiveGearStrategyReport failed";
        return success;
    }

    void MediaMgr::SetEnableTransition(bool enable_transition)
    {
        enable_transition_ = enable_transition;
    }

    bool MediaMgr::GetEnableTransition()
    {
        return enable_transition_;
    }

    void MediaMgr::StartTimer(void* param)
    {
        StopTimer();

        ITimerMgr* mgr = reinterpret_cast<ITimerMgr*>(g_cief->QueryInterface(LSINAME_TIMERMGR));
        uint64_t   id = timer_id_counter_++;
        uint64_t   ret_id = mgr->SetTimer(id, 1000, this);
        if (ret_id != 0)
        {
            timer_id_ = ret_id;
        }
    }

    void MediaMgr::StopTimer()
    {
        if (timer_id_ != 0)
        {
            ITimerMgr* mgr = reinterpret_cast<ITimerMgr*>(g_cief->QueryInterface(LSINAME_TIMERMGR));
            mgr->KillTimer(timer_id_);
            timer_id_ = 0;
        }
    }

    void MediaMgr::OnTimer()
    {
        auto now = std::chrono::steady_clock::now();
        for (auto it : effect_ctx_map_)
        {
            auto effect_ctx = it.second;
            if (effect_ctx && !effect_ctx->enable_effect && !effect_ctx->enable_effect_operation_done)
            {
                const auto interval = now - effect_ctx->last_enable_effect_tp;
                if (interval >= std::chrono::seconds(EFFECT_ENABLE_TIME))
                {
                    LOG(WARNING) << "[MediaMgr::OnTimer] interval to 30s";
                    effect_ctx->enable_effect_operation_done = true;
                    sdk_controller_->EffectEnable(it.first, effect_ctx->enable_effect);
                }
            }
        }
    }

    void MediaMgr::HandleTimer(UINT64 id)
    {
        auto handler = [this]() {
            OnTimer();
         };
        auto task = task_mgr_.CreateThreadTask(handler);
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_IMMEDIATE);
    }

    bool MediaMgr::EnableEffect(const std::string& layer_id, bool enable)
    {
        std::unique_lock<std::mutex> lock(effect_ctx_map_mutex_);
        auto                         found = effect_ctx_map_.find(layer_id);

        bool success = false;
        EffectContextPtr effect_ctx = nullptr;
        if (found != effect_ctx_map_.end())
        {
            effect_ctx = found->second;
        }
        else
        {
            effect_ctx = std::make_shared<EffectContext>();
        }

        if (enable)
        {
            success = sdk_controller_->EffectEnable(layer_id, enable);
            effect_ctx->enable_effect = enable;
            effect_ctx->enable_effect_operation_done = true;
            effect_ctx->last_enable_effect_tp = std::chrono::steady_clock::now();
        }
        else
        {
            if (enable != effect_ctx->enable_effect)
            {
                effect_ctx->enable_effect = enable;
                effect_ctx->enable_effect_operation_done = false;
            }
            success = true;
        }
        
        effect_ctx_map_[layer_id] = effect_ctx;
        return success;
    }

    void MediaMgr::OnLayerCreated(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            CreateVisualEvent event{};
            event.visualID = visual_id;
            event.success = success;
            event.errCode = static_cast<INT32>(error_code);
            event.type = type;
            event.reason = failed_reason;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnLayerDeleted(const std::string& visual_id)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            DeleteVisualEvent event{};
            event.visualID = visual_id;
            event.success = true;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnLayerFallback(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason, bool fallbackToPlaceHolder /*= false*/)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            FallbackVisualEvent event{};
            event.visualID = visual_id;
            event.success = success;
            event.errCode = static_cast<INT32>(error_code);
            event.type = type;
            event.fallbackToPlaceHolder = fallbackToPlaceHolder;
            event.reason = failed_reason;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnFullScreenDetectorCallback(const std::string& detector_id, const bool found_target, const FullScreenDetector::WindowInfo& info)
    {
        auto task = task_mgr_.CreateThreadTask([=]() {
            FullScreenDetectorEvent event{};
            event.detectorID = detector_id;
            event.foundTarget = found_target;
            event.className = info.class_name;
            event.exeName = info.exe_path;
            event.pid = info.pid;
            event.winID = info.win_id;
            eventbus::EventBus::PostEvent(event);
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);
    }

    void MediaMgr::OnThreadMonitorEvent(MONITOR_THREAD_ID thread_id, MONITOR_THREAD_EVENT_TYPE event_type)
    {
        IThreadMonitor* threadMonitor = (IThreadMonitor*)g_cief->QueryInterface(LSINAME_THREADMONITOR);
        if (!threadMonitor)
            return;

        std::string thread_name = "";
        bool        success = true;

        bool is_render_thread = false;

        switch (thread_id)
        {
        case MONITOR_THREAD_ID::MONITOR_THREAD_ID_RENDER_THREAD:
            thread_name = THREADNAME_RENDER;
            is_render_thread = true;
            break;
        default:
            success = false;
        }

        if (!success)
            return;

        switch (event_type)
        {
        case MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_START:
            threadMonitor->OnThreadBegin(thread_name.c_str());
            if (enable_nvidia_driver_hang_recover_ && is_render_thread)
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->EnableDetect(true);
            break;
        case MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_TICK:
            threadMonitor->OnFrameBegin(threadMonitor->GetContext());
            if (enable_nvidia_driver_hang_recover_ && is_render_thread)
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->OnTick();
            break;
        case MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_END:
            threadMonitor->OnThreadEnd(threadMonitor->GetContext());
            if (enable_nvidia_driver_hang_recover_ && is_render_thread)
                nvidia_helper::NvidiaDriverHangRecover::GetInstacne()->EnableDetect(false);
            break;
        default:
            break;
        }
    }
} // namespace media_mgr