#pragma once

#include <climits>
#include <cstdint>

#include "mediasdk_array.hpp"
#include "mediasdk_string.hpp"

#pragma pack(push, 1)

namespace mediasdk {

struct MSRect {
  int32_t x;
  int32_t y;
  int32_t cx;
  int32_t cy;
};

struct MSRectF {
  float x;
  float y;
  float cx;
  float cy;
};

struct MSSize {
  int32_t cx;
  int32_t cy;
};

struct MSLUID {
  unsigned long low;
  long high;
};

struct MSAdapterInfo {
  mediasdk::MSLUID adapter_id;
  uint32_t vendor_id;
  uint32_t device_id;
  wchar_t adapter_name[128];
};

struct MSSizeF {
  float cx;
  float cy;
};

struct MSScaleF {
  float x;
  float y;
};

struct MSClip {
  int32_t x;
  int32_t y;
  int32_t z;
  int32_t w;
};

struct MSClipF {
  float x;
  float y;
  float z;
  float w;
};

struct MSTranslateF {
  float x;
  float y;
};

struct MSPointS {
  int32_t x;
  int32_t y;
};

struct MSRational {
  float numerator;
  float denominator;
};

enum RenderThreadEvent { kRenderThreadEventHeartbeat };

struct MSDevLostEvent {
  int32_t error;
  int32_t remove_reason;
  bool need_restart;

  MediaSDKString driver_date;
  MediaSDKString driver_name;
  MediaSDKString driver_ver;
};

enum MovePostion {
  kMoveUp,
  kMoveDown,
  kMoveTop,
  kMoveBottom,
};

enum EncoderType {
  kEncoderTypeH264 = 0,
  kEncoderTypeHEVC = 1,
};

enum ImageFileFormat {
  kImageFileFormatBMP,
  kImageFileFormatJPG,
  kImageFileFormatPNG,
  kImageFileFormatTIFF,
  kImageFileFormatGIF,
  kImageFileFormatWMP
};

struct MSPointNineInfo {
  bool point_nine_border_info;  // background image use passed border info
  MSPointS left;                // the range of left, [left.x, left.y]
  MSPointS top;                 // the range of top, [top.x, top.y]
};

enum AudioMonitorType {
  kAudioMonitorNone = 0,
  kAudioMonitorOutput = 1,
  kAudioMonitorRender = 2,
};

enum AudioInputType {
  kAudioInputNone = 0,
  kAudioInputMicrophone = 1,
  kAudioInputLoopback = 2,
  kAudioInputAnalog = 3,
  kAudioInputFAV = 4,
  kAudioInputLAV = 5,
  kAudioInputVLC = 6,
  kAudioInputRTC = 7,
  kAudioInputWirelineIOS = 8,
  kAudioInputWirelineAndroid = 9,
  kAudioInputWirelessIOS = 10,
  kAudioInputWirelessAndroid = 11,
  kAudioInputDECKLINK = 12,
  kAudioInputDMO = 13,
  kAudioInputPCM = 14,
  kAudioInputNDI = 15,
  kAudioInputVR = 16,
  kAudioInputFF = 17,
  kAudioInputBrowser = 18,
  kAudioInputAPP = 19,
};

struct MSTransform {
  bool flip_h;
  bool flip_v;
  float angle;
  // Deprecated `ratio`, automatic maintenance by mediasdk
  float ratio;
  MSScaleF scale;
  MSClipF clip;
  MSTranslateF translate;
};

#define EMPTY_MSTRANSFORM                            \
  MSTransform {                                      \
    false, false, 0, 0, {1, 1}, {0, 0, 0, 0}, {0, 0} \
#define EMPTY_MSTRANSFORM                            \
  MSTransform {                                      \
    false, false, 0, 0, {1, 1}, {0, 0, 0, 0}, {0, 0} \
  }

struct PreviewUIConfig {
  // r8g8b8a8
  uint32_t highlight_color;
  uint32_t selected_color;
  uint32_t adsorption_color;
  uint32_t edge_color;
};

enum VideoRange {
  kVideoRangeUnspecified = 0,
  kVideoRangePartial = 1,
  kVideoRangeFull = 2,
};

enum VideoScanType {
  kVideoScanTypeProgressive = 0,
  kVideoScanTypeInterlaced = 1
};

enum ColorSpace {
  kColorSpaceUnspecified = 0,
  kColorSpaceBT709 = 1,
  kColorSpaceBT601 = 2,
  kColorSpaceBT2020 = 3,
  kColorSpaceBT2100 = 4,
};

enum PixelFormat {
  kPixelFormatUnspecified = 0,
  kPixelFormatNV12,
  kPixelFormatI420,
  kPixelFormatI444,
  kPixelFormatI422,
  kPixelFormatI440,
  kPixelFormatI410,
  kPixelFormatI411,
  kPixelFormatI400,
  kPixelFormatYV12,
  kPixelFormatNV21,
  kPixelFormatUYVY,
  kPixelFormatYUY2,
  kPixelFormatARGB,
  kPixelFormatXRGB,
  kPixelFormatRGB24,
  kPixelFormatRGBA,
  kPixelFormatBGR24,
  kPixelFormatBGRA,
  kPixelFormatMJPEG,
  kPixelFormatI444A,
  kPixelFormatI420A,
  kPixelFormatI422A,
  kPixelFormatYVYU,
  kPixelFormatNV12TEXTURE,
  kPixelFormatY8,
  kPixelFormatY8TEXTURE,
  kPixelFormatU8V8,
  kPixelFormatRGBA16,
  kPixelFormatHDYC,
  kPixelFormatMax,

};

struct PreviewParams {
  // As the direct parent window of the MediaSDK's internal preview child window
  void* hwnd_parent;
  // Make the internal preview child window of MediaSDK top relative to other
  // child windows in the parent window specified by 'hwnd_parent'
  bool auto_top_zorder;
  // show_window created show window
  bool show_window;
  MSRect window_rect;
};

struct ModelParams {
  MSSize output_size;
  ColorSpace color_space;
  VideoRange video_range;
  // By default, use Double stage, and in high-performance encoder output,
  // it can only Single stage
  float fps;
  MSLUID adapter_luid;
  uint32_t preview_id;
  PreviewParams preview_params;
};

enum ProfileAACType {
  kProfileAACNone = 0,
  kProfileAACMain = 1,
  kProfileAACLow = 2,
  kProfileAACSSR = 3,
  kProfileAACLTP = 4,
  kProfileAACHE = 5,
  kProfileAACHE_V2 = 29,
  kProfileAACLD = 23,
  kProfileAACELD = 39,
};

// If the encoder ID is specified, reuse the already running encoder;
// otherwise, create a new encoder according to the parameters
// Each service plugin has a different "config" and needs to refer to the
// specific definition of the plugin
// The fallback logic is determined by the order of elements in
// "stream_source_config_list"
/*
{
  "id": "stream_id_model_0",
  "sink_id": 0,
  "track_id": 0,
  "audio_encoder_config": {
    "id": "audio_encoder_id_model_0",
    "name": "FFMPEG_AAC",
    "bitrate": 8000,
    "profile": ""
  },

  "video_encoder_config": {
    "id": "video_encoder_id_model_0",
    "name": "ByteVC0VideoEncoderSource",
    "preset": "ultrafast",
    "profile": "main",
    "usage": "livestreaming",
    "bitrate": 3600,
    "rate_control": "VBR",
    "max_bitrate": 5000,
    "key_sec": 2,
    "vbv_buffer_size": 4200,
    "b_frames": 2,
    "lookahead": false,
    "temporal_aq": false
  },
  "stream_source_config_list": [
    {
      "name": "RTMPQStreamServiceSource",
      "config": {
        "url": "rtmp://127.0.0.1:1935/live",
        "key": "A4BB6D4257C3",
        "room_id": "12345678",
        "extra_json": {}
      },
      "reconnect_count": 5,
      "reconnect_interval": 3000,
      "cipher": "",
      "plain_text": "",
    },
    {
      "name": "RTMPStreamServiceSource",
      "config": {
        "url": "rtmp://127.0.0.1:1935/live",
        "key": "A4BB6D4257C3",
        "room_id": "12345678",
      },
      "reconnect_count": 9999,
      "reconnect_interval": 3000,
      "cipher": "",
      "plain_text": "",
    }
  ],

  "cdn_list":["127.0.0.1", "*********"],
  "delay_ms": 5000,
  "abr_strategy": {
    "strategy": "common",
    "max_bitrate" : 10000,
    "min_bitrate": 8000,
    "offset": -100
  },
  "speed_test_target_bitrate": 50000
}
*/
typedef MediaSDKString StreamParams;

//{
//    "context": {
//      "stream_id": "xxx",
//      "reason": 0, // StreamReason
//      "stream_type": "xxx",
//      "url": "rtmp://xxx",
//      "key": "xxx",
//    }
//}
typedef MediaSDKString StreamContext;

// {
//    "id": "video_encoder_id_model_0",
//    "name": "ByteVC0VideoEncoderSource",
//    "preset": "ultrafast",
//    "profile": "main",
//    "usage": "livestreaming",
//    "bitrate": 3600,
//    "rate_control": "VBR",
//    "max_bitrate": 5000,
//    "key_sec": 2,
//    "vbv_buffer_size": 4200,
//    "b_frames": 2,
//    "lookahead": false,
//    "temporal_aq": false
//  }
typedef MediaSDKString FallbackVideoEncoderConfigParams;

struct AudioEncoderParams {
  // If the encoder ID is specified, reuse the already running encoder;
  // otherwise, create a new encoder according to the parameters
  MediaSDKString id;
  uint32_t bitrate;
  ProfileAACType aac_profile;
};

struct VideoEncoderParams {
  // If the encoder ID is specified, reuse the already running encoder;
  // otherwise, create a new encoder according to the parameters
  MediaSDKString id;
  MediaSDKString name;
  MediaSDKString preset;
  MediaSDKString profile;
  MediaSDKString usecase;
  uint32_t bitrate;
  MediaSDKString rate_control;
  uint32_t max_bitrate;
  // I frame per second
  uint32_t gop;
  uint32_t vbv_buffer_size;
  // B frame. 0, 1, 2 were supported
  uint32_t bframes;
  bool lookahead;
  bool temporal_aq;
};

enum PluginType {
  kVisual = 0,
  kAudio = 1,
  kService = 2,
  kVisualFilter = 3,
  kAudioFilter = 4,
  kVideoEncoder = 5,
  kAudioEncoder = 6,
  kCanvasFilter = 7,
  kMaxValue = kCanvasFilter
};

struct PluginInfo {
  MediaSDKString id;
  PluginType type;
  MediaSDKString name;
  MediaSDKString desc;
};

typedef MediaSDKArray<PluginInfo> PluginInfoArray;

struct MSAudioPerformance {
  bool valid;
  int32_t cost;
  int32_t offset;
  int32_t reset_times;
};

enum AUDIO_FORMAT {
  AUDIO_FORMAT_UNKNOWN = 0,
  AUDIO_FORMAT_U8,
  AUDIO_FORMAT_S16,
  AUDIO_FORMAT_S32,
  AUDIO_FORMAT_FLOAT,
  AUDIO_FORMAT_U8_PLANAR,
  AUDIO_FORMAT_S16_PLANAR,
  AUDIO_FORMAT_S32_PLANAR,
  AUDIO_FORMAT_FLOAT_PLANAR,
};

enum CHANNEL_LAYOUT {
  CHANNEL_UNKNOWN = 0,
  CHANNEL_MONO,
  CHANNEL_STEREO,
  CHANNEL_2POINT1,
  CHANNEL_2_1,
  CHANNEL_2_2,
  CHANNEL_QUAD,
  CHANNEL_4POINT0,
  CHANNEL_4POINT1,
  CHANNEL_5POINT0,
  CHANNEL_5POINT1,
  CHANNEL_5POINT0_BACK,
  CHANNEL_5POINT1_BACK,
  CHANNEL_6POINT0,
  CHANNEL_6POINT1,
  CHANNEL_7POINT0,
  CHANNEL_7POINT0_FRONT,
  CHANNEL_7POINT1,
  CHANNEL_7POINT1_WIDE,
  CHANNEL_7POINT1_WIDE_BACK,
  CHANNEL_SURROUND,
};

enum VideoFramePlane { kMaxVideoPlanes = 4 };

enum AudioFramePlane {
  kMaxAudioPlanes = 8,
  kAudioPumpUnitFrames = 1024,
};

// Note: The naming of AudioSourceFrame is not reasonable. However, considering
// the conflict with the AudioFrame class name, it will be uniformly modified
// and refined during optimization.
struct AudioSourceFrame {
  // An array of pointers to the audio data buffers, with a maximum of
  // kMaxAudioPlanes planes.
  // format of `data` is `AUDIO_FORMAT_S32`
  uint8_t* data[kMaxAudioPlanes];
  // The sample rate of the audio data.
  int32_t sample_rate;
  // The size of each audio block, typically measured in samples.
  int32_t block_size;
  // The number of audio blocks in this frame.
  int32_t count;
  // The number of audio channels in the audio data.
  int32_t channel_count;
  // The timestamp of this audio frame in nanoseconds.
  int64_t timestamp_ns;
};

enum StreamReason {
  kStartStream = 0,
  kStartRecord = 1,
  kStopNormal = 2,
  kStopStreamRTCMix = 3,
  kStopStreamForbidden = 4,
  kStopStreamOutOfTime = 5,
  kStopStreamCodecError = 6,
  kStopStreamFatalError = 7,
};

struct MSVisualFrame {
  unsigned char* buffer;
  int width;
  int height;
  int line_size;
  PixelFormat format;
};

// Adaptive control parameters, [width, height, fps] for encoder control, will
// take effect synchronously or fail synchronously. abr_strategy is for ABR
// control, which is independent of encoder control. In normal scenarios, if
// successful, it returns kVideoReconfigureErrorCodeSuccess, otherwise, it
// returns the corresponding error code. In recording scenarios, encoder control
// will not be executed, but ABR control will be executed. If successful, it
// returns kVideoReconfigureErrorCodeSuccessOnlyForAbr, otherwise, it returns
// the corresponding error code.
struct VideoOutputParams {
  MediaSDKString stream_id;
  uint32_t width;
  uint32_t height;
  uint32_t fps;
  // Representing the upper and lower limits of ABR, like "{"up": 8000,
  // "down": 4000}"
  MediaSDKString abr_strategy;
};

enum VideoReconfigureErrorCode {
  kVideoReconfigureErrorCodeSuccess = 0,
  kVideoReconfigureErrorCodeFailed,
  kVideoReconfigureErrorCodeSuccessOnlyForAbr,
  kVideoReconfigureErrorCodeInvalidEncoder,
  kVideoReconfigureErrorCodeEncoderReconfigFailed
};

struct VideoReconfigureResult {
  VideoReconfigureErrorCode error_code;
};

struct WndParams {
  bool is_popUp;
  float top_border_radius;
  float bottom_border_radius;
  float opacity;
};

enum CanvasSelectionBorderRenderType {
  kSelectionBorderRenderTopmost = 0,
  kSelectionBorderRenderSameWithSelected = 1,
};

}  // namespace mediasdk

#pragma pack(pop)

#define MEDIASDK_INVALID_DTS INT_MIN
