#include "Controller.h"
#include "ModeSceneMgr.h"
#include "SourceMgr.h"
#include "AudioMgr.h"
#include "FilterMgr.h"
#include "MonitorMgr.h"
#include "LogDumpMgr.h"
#include "WatchdogClient.h"
#include "icief.h"
#include "ciefhelper.h"
#include "SystemHook.h"
#include "AiSdkIPCMgr.h"
#include "MediaSDKControllerV2Impl.h"
#include <unordered_set>
#include <dxgi1_6.h>
#include <DirectXTex.h>
#include <filesystem>
#include "V3HookAPILayer/graphics_helper.h"
#include <ScreenGrab.h>
#include <DDSTextureLoader.h>

extern CIEF::ICIEF* g_cief;
extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;
media_mgr::MediaMgr* g_mediaMgr = nullptr;
v3::AudioFrameProcessor* g_audioFrameProcessor = nullptr;
v3::VideoFrameProcessor* g_videoFrameProcessor = nullptr;

Controller::Controller()
{
	g_mediaMgr = new media_mgr::MediaMgr;
}

Controller::~Controller() 
{}

void Controller::OnParallelInitialize(void* param){}

void Controller::Close()
{
	IWorkflowMonitor* quitFlow = (IWorkflowMonitor*)g_cief->QueryInterface(LSINAME_SHUTDOWNFLOW);
	CloseTimer();
	quitFlow->Record("Controller::Close 1");
	g_mediaMgr->Uninitialize();
	quitFlow->Record("Controller::Close 2");
}

bool Controller::Initialize(INITIALIZE_INFO* info)
{
	if (!info)
	{
		LOG(ERROR) << "[Controller::Initialize] initialize param is null";
		return false;
	}

	// MessageBoxW(0, 0, 0, 0);
	m_bottomWnd = info->bottomWnd;
	if (!g_mediaMgr->Initialize(*info))
	{
		LOG(ERROR) << "[Controller::Initialize] MediaMgr Initialize failed!";
		return false;
	}

	g_audioFrameProcessor = g_sdkController->GetAudioFrameProcessor();
    g_videoFrameProcessor = g_sdkController->GetVideoFrameProcessor();
	g_cief->GetThreadMgr()->AddTaskToThreadPool(new MemberTask<Controller>(this, &Controller::SetupTimer, 0));
	LS::LOCALKEYBORAD::GetInstance().KEYBOARD_HOOK += Delegate<void(WPARAM, LPARAM)>(this, &Controller::OnKey);
	return true;
}

void Controller::Quit()
{
    g_cief->GetEventMgr()->GetEvent(&LSEVENT_QUIT)->CallBack(0, 0);
}

void Controller::EnumWindows(BOOL icon, std::vector<WINDOW_DESC>* oWindowDescs)
{
	g_sdkController->EnumWindows(icon, oWindowDescs);
}

void Controller::EnumMonitors(std::vector<MONITOR_DESC>* oMonitorDescs)
{
	g_sdkController->EnumMonitors(oMonitorDescs);
}

void Controller::EnumVideoDevices(std::vector<DSHOW>* oDevices)
{
	g_sdkController->EnumVideoInputDevices(oDevices);
}

void Controller::EnumAudioDevices(std::vector<DSHOW>* oDevices)
{
	g_sdkController->EnumAudioInputDevices(oDevices);
}

void Controller::EnumCaptureFormats(const DSHOW& videoDevice, std::vector<VIDEO_CAPTURE_FORMAT>* videoCapFormats, std::vector<AUDIO_CAPTURE_FORMAT>* audioCapFormats)
{
    if (videoCapFormats)
    {
        if (audioCapFormats)
        {
			g_sdkController->EnumDeviceSupportFormats(videoDevice, videoCapFormats, audioCapFormats);
        }
        else
        {
			g_sdkController->EnumDeviceSupportFormats(videoDevice, videoCapFormats, nullptr); // Camera does not need to enumerate audio formats
        }
    }
}

void Controller::EnumAudioCapFormatWithDShowName(const DSHOW& device, std::vector<AUDIO_CAPTURE_FORMAT>* audioCapFormats)
{
    g_sdkController->EnumDeviceSupportFormatsByDeviceName(device, audioCapFormats);
}

void Controller::IsForegroundFullScreen(bool* full)
{
    HWND hWND = NULL;
    hWND = GetForegroundWindow();
    if ((hWND != GetDesktopWindow()) && (hWND != GetShellWindow()))
    {
        RECT windowRect = {0};
        GetWindowRect(hWND, &windowRect);
        RECT deskRect = {0};
        GetWindowRect(GetDesktopWindow(), &deskRect);
        if (windowRect.left <= deskRect.left &&
            windowRect.top <= deskRect.top &&
            windowRect.right >= deskRect.right &&
            windowRect.bottom >= deskRect.bottom)
        {
            WCHAR szTemp[100];
            if (GetClassNameW(hWND, szTemp, _countof(szTemp)) > 0)
            {
				if (wcscmp(szTemp, L"WorkerW") != 0)
					*full = true;
            }
        }
    }
}

void Controller::WindowOverlapped(HWND hwnd, bool* overlapped)
{
	if (hwnd == NULL)
	{
		*overlapped = false;
		return;
	}

	if (!IsWindowVisible(hwnd) || IsIconic(hwnd) || hwnd == GetTopWindow(GetDesktopWindow()))
	{
		*overlapped = true;
		return;
	}

	RECT rect;
	GetWindowRect(hwnd, &rect);
	HWND hCur = GetWindow(hwnd, GW_HWNDPREV);
	while (hCur != NULL)
	{
		if (IsWindowVisible(hCur) && !IsIconic(hCur))
		{
			RECT curRect;
			GetWindowRect(hCur, &curRect);
			*overlapped = !(curRect.left > rect.right ||
				curRect.right < rect.left ||
				curRect.top > rect.bottom ||
				curRect.bottom < rect.top);
			if (*overlapped)
				break;
		}
		hCur = GetNextWindow(hCur, GW_HWNDPREV);
	}
}

void Controller::GetWindowOverlappedMonitors(HWND hwnd, std::vector<std::string>* monitorDids, std::string* mainDid, std::vector<std::string>* allDidList)
{
    USER_MONITOR_INFO info{};

    if (!::GetWindowRect(hwnd, &info.windowRect))
    {
        if (monitorDids)
        {
            for (const auto& monitor : info.monitors)
            {
                monitorDids->push_back(monitor);
            }
            return;
        }
    }

    static auto MonitorEnumCallback = [](HMONITOR hMonitor, HDC hdcMonitor, LPRECT lprcMonitor, LPARAM dwData) -> BOOL {
        USER_MONITOR_INFO* info = reinterpret_cast<USER_MONITOR_INFO*>(dwData);
        MONITORINFOEXA     monitorInfo;
		monitorInfo.cbSize = sizeof(MONITORINFOEXA);

        if (::GetMonitorInfoA(hMonitor, &monitorInfo))
        {
            DISPLAY_DEVICEA displayDevice{};
			displayDevice.cb = sizeof(displayDevice);
            if (::EnumDisplayDevicesA(monitorInfo.szDevice, 0, &displayDevice,
				EDD_GET_DEVICE_INTERFACE_NAME))
            {
                info->allMonitorDidList.push_back(displayDevice.DeviceID);

                RECT intersection;
                if (::IntersectRect(&intersection, &info->windowRect, &monitorInfo.rcMonitor))
                {
                    info->monitors.push_back(displayDevice.DeviceID);
                }
            }
        }
        return TRUE;
    };

    MONITORENUMPROC enumProc = MonitorEnumCallback;
    LPARAM          lParam = reinterpret_cast<LPARAM>(&info);

    ::EnumDisplayMonitors(NULL, NULL, enumProc, lParam);
    if (monitorDids)
    {
        for (const auto& monitor : info.monitors)
        {
            monitorDids->push_back(monitor);
        }
    }
    if (mainDid)
    {
        auto h = MonitorFromWindow(NULL, MONITOR_DEFAULTTOPRIMARY);
        if (h)
        {
            MONITORINFOEXA mi;
            mi.cbSize = sizeof(MONITORINFOEXA);

            if (::GetMonitorInfoA(h, &mi))
            {
                DISPLAY_DEVICEA device{};
                device.cb = sizeof(device);
                if (::EnumDisplayDevicesA(mi.szDevice, 0, &device,
                                          EDD_GET_DEVICE_INTERFACE_NAME))
                {
                    *mainDid = device.DeviceID;
                }
            }
        }
    }
    if (allDidList)
    {
        for (const auto& did : info.allMonitorDidList)
        {
            allDidList->push_back(did);
        }
    }
}

void Controller::BonjourCheck(bool* oChecked)
{
	g_sdkController->MobileProjectorSourceCheckBonjour(oChecked);
}

void Controller::OpenMobilePreview(UINT32 videoModel, UINT64 hwnd, Gdiplus::RectF rect)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}

	PREVIEW_PARAMS params{};
	g_sdkController->PreviewWindowOpenProjector("mobile_preview", videoModel, hwnd, rect.X, rect.Y, rect.Width, rect.Height, params);
}

void Controller::CloseMobilePreview(UINT32 videoModel)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
	g_sdkController->PreviewWindowCloseProjector("mobile_preview", videoModel);
}

bool Controller::OpenCanvasPreview(const std::string& previewId, UINT32 videoModel, HWND hwnd, Gdiplus::RectF rect, const PREVIEW_PARAMS& params)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
    return g_sdkController->PreviewWindowOpenProjector(previewId, videoModel, reinterpret_cast<uint64_t>(hwnd), rect.X, rect.Y, rect.Width, rect.Height, params);
}

bool Controller::CloseCanvasPreview(const std::string& previewId, UINT32 videoModel)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
	return g_sdkController->PreviewWindowCloseProjector(previewId, videoModel);
}

bool Controller::SetCanvasPreviewParams(const std::string& previewID, UINT32 videoModel, const PREVIEW_PARAMS* params)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
    return g_sdkController->SetProjectorWndParams(previewID, videoModel, *params);
}

bool Controller::SetCanvasPreviewPosition(const std::string& previewID, UINT32 videoModel, const Gdiplus::RectF& rect)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
    return g_sdkController->SetProjectorPosition(previewID, videoModel, rect);
}

void Controller::SetDisplay(bool showView, UINT32 videoModel /*= UINT32_MAX*/)
{
    // TODO: @xuwanhui How to Get PreviewID ??? Current VideoModel is not exist

	UINT32 previewID = 0;
	if (videoModel != UINT32_MAX)
	{
		UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
		if (mainSceneModel != UINT32_MAX)
		{
			videoModel = mainSceneModel;
		}

        if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &previewID))
            g_mediaMgr->SetDisplay(previewID, showView);
	}
	else
	{
        Mode* pMode = ModeSceneMgr::GetInstance()->GetCurrentMode();
        if (pMode)
        {
            MODE_INFO_EX modeInfoEx{};
            pMode->GetModeInfo(&modeInfoEx);
            if (modeInfoEx.id == LIVE_MODE_LANDSCAPE)
            {
                if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(0, &previewID))
					g_mediaMgr->SetDisplay(previewID, showView);
            }
            else if (modeInfoEx.id == LIVE_MODE_PORTRAIT)
            {
                if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(1, &previewID))
                    g_mediaMgr->SetDisplay(previewID, showView);
            }
            else if (modeInfoEx.id == LIVE_MODE_DBCANVAS)
            {
                for (int i = LIVE_MODE_DBCANVAS - 1; i >= 0 ; --i)
                {
                    if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(i + 2, &previewID))
						g_mediaMgr->SetDisplay(previewID, showView);
                }
            }
        }
	}
}

bool Controller::GetDisplay(UINT32 videoModel)
{
	// TODO: @xuwanhui How to Get PreviewID ??? Current VideoModel is not exist
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
        videoModel = mainSceneModel;
	}

	UINT32 previewID = 0;
    if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &previewID))
	{
        return g_mediaMgr->GetDisplay(previewID);
	}
	
	return false;
}

bool Controller::VibeTriggerEffect(const VIBE_TRIGGER_EFFECT& vibeTriggerEffect, std::vector<std::pair<std::string, bool>>& resultActionIDs)
{
    return g_sdkController->VibeTriggerEffect(vibeTriggerEffect, resultActionIDs);
}

bool Controller::SetCanvasSelectBorderType(UINT64 canvasID, CANVAS_SELECTION_BORDER_TYPE borderType)
{
    UINT32 previewID = ModeSceneMgr::GetInstance()->GetPreviewIDByCanvasID(canvasID);
    return g_sdkController->SetCanvasSelectBorderType(previewID, borderType);
}

void Controller::SetDisplayMasks(const std::string& portraitFile, const std::string& landscapeFile)
{
    auto CleanupResources = [&](int canvasType) {
        auto it = m_canvasResources.find(canvasType);
        if (it != m_canvasResources.end())
        {
            auto& [layerID, sourceID] = it->second;
			if (ModeSceneMgr::GetInstance()->CheckLayerExist(layerID))
				ModeSceneMgr::GetInstance()->DestroyLayer(layerID);
			if (SourceMgr::GetInstance()->CheckSourceExist(sourceID))
				SourceMgr::GetInstance()->DestroySource(sourceID);
            m_canvasResources.erase(it);
        }
    };

    auto ProcessOrientation = [&](const std::string& file, int canvasType) {
        if (file.empty())
        {
            CleanupResources(canvasType);
            return;
        }

        MATERIAL_DESC mediaDesc{};
        if (!g_sdkController->GetMediaFileInfo(file, &mediaDesc))
            return;

        IMAGE_SOURCE image{};
        image.materialDesc = mediaDesc;

        SOURCE_INFO sourceInfo{};
        sourceInfo.type = VISUAL_IMAGE;
		sourceInfo.source = image;

		LAYER_INFO layerInfo{};
        layerInfo.transform.size = {mediaDesc.size.Width, mediaDesc.size.Height};

        UINT32 previewID = 0;
		if (!ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(canvasType, &previewID))
		{
            LOG(ERROR) << "[Controller::SetDisplayMasks] GetPreviewByVideoModel failed, canvasType: " << canvasType << ", previewID: " << previewID;
            return;
		}

		UINT64 canvasID = ModeSceneMgr::GetInstance()->GetCurCanvasIDByPreviewID(previewID);
        if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
        {
            CANVAS_INFO_EX canvasInfoEx{};
            pCanvas->GetCanvasInfo(&canvasInfoEx);

			PREVIEW_INFO previewInfo;
			if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(canvasInfoEx.previewID))
			{
                pView->GetPreviewInfo(&previewInfo);
			}

            TRANSFORM transform{};
            transform.scale.X = previewInfo.layoutRect.Width / layerInfo.transform.size.Width;
            transform.scale.Y = previewInfo.layoutRect.Height / layerInfo.transform.size.Height;
            transform.size.Width = layerInfo.transform.size.Width;
			transform.size.Height = layerInfo.transform.size.Height;
            layerInfo.transform = transform;

            if (SourceMgr::GetInstance()->CreateSource(&sourceInfo, NULL))
			{
                const UINT64 sourceID = sourceInfo.id;
				layerInfo.sourceID = sourceID;
                if (ModeSceneMgr::GetInstance()->AddLayer(&layerInfo, NULL))
				{
                    if (ModeSceneMgr::GetInstance()->BindCanvasLayer(canvasID, layerInfo.id))
                    {
                        std::string layer_id;
                        Util::NumToString(layerInfo.id, &layer_id);
						g_sdkController->LayerMoveTop(layer_id);
						g_sdkController->LayerSetLock(layer_id, true);

						std::vector<VISUAL_FLAG> visual_flags = {VISUAL_FLAG_OUTPUT_FILTER, VISUAL_FLAG_ALWAYS_TOP, VISUAL_FLAG_AVOID_DESTROY_ALL};
                        g_mediaMgr->SetLayerFlag(visual_flags, layer_id);
                        m_canvasResources[canvasType] = {layerInfo.id, sourceID};
					}
					else
					{
						ModeSceneMgr::GetInstance()->DestroyLayer(layerInfo.id);
						SourceMgr::GetInstance()->DestroySource(sourceID);
					}
				}
				else
				{
					SourceMgr::GetInstance()->DestroySource(sourceID);
				}
			}
        }
    };

    ProcessOrientation(landscapeFile, 2);
    ProcessOrientation(portraitFile, 3);
}

void Controller::EnableAllPreview(bool enable)
{
	g_mediaMgr->EnableAllPreview(enable);
}

void Controller::EnablePreviewByVideoModel(UINT32 videoModel, bool enable)
{
	UINT32 previewID = 0;
    if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &previewID))
    {
        g_mediaMgr->EnablePreviewByVideoModel(previewID, enable);
    }
}

void Controller::EnableInteract(bool enable)
{
	for (int i = 0; i < LIVE_MODE_DBCANVAS; ++i)
	{
		UINT32 videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(i);
        UINT32 previewID = 0;
        if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &previewID))
            g_sdkController->PreviewWindowEnableInteract(previewID, enable);
	}
}

void Controller::CreateFullScreenDetector(const std::string& detectorID)
{
	g_mediaMgr->CreateFullScreenDetector(detectorID);
}

void Controller::DestroyFullScreenDetector(const std::string& detectorID)
{
	g_mediaMgr->DestroyFullScreenDetector(detectorID);
}

void Controller::SetFullScreenDetectorIgnoreProcessList(const std::vector<std::string>& exeNames)
{
	g_mediaMgr->SetFullScreenDetectorIgnoreProcessList(exeNames);
}

void Controller::GetFontFamilies(std::vector<std::string>* fonts)
{
	g_sdkController->TextSourceGetFonts(fonts);
}

void Controller::UpdateDynamicConfig(const std::string& json_config, bool enableTransition)
{
	g_sdkController->UpdateDynamicConfig(json_config);
    g_mediaMgr->SetEnableTransition(enableTransition);
}

void Controller::CheckEncoderSession(const std::string& name, UINT32 count, INT32* result)
{
	g_sdkController->CheckEncoderSession(name, count, result);
}

void Controller::StartColorPicker(HWND hwnd)
{
	g_mediaMgr->StartColorPicker(hwnd);
}

UINT64 Controller::AllocPreviewID()
{
	return ModeSceneMgr::GetInstance()->AllocPreviewID();
}

void Controller::InitEffectPlatform(INIT_EFFECT_PLATFORM initEffect)
{
	g_mediaMgr->InitEffectPlatform(initEffect);
}

void Controller::UpdateEffectConfig(const std::string& userID, const std::string& ttlsHardwareLevel)
{
	g_mediaMgr->UpdateEffectConfig(userID, ttlsHardwareLevel);
}

void Controller::UnInitEffectPlatform()
{
	g_sdkController->EffectPlatformUninit();
}

void Controller::LoadEffectModels(const std::string& requestID, const std::string& modelName, const std::vector<std::string>& requirements)
{
	g_sdkController->EffectPlatformLoadModels(requestID, modelName, requirements);
}

void Controller::GetMediaFileInfo(const std::string& path, MATERIAL_DESC* desc)
{
	g_sdkController->GetMediaFileInfo(path, desc);
}

struct ColorCorrectionFilterProcessContext
{
    std::atomic_bool        finish = false;
    std::mutex              cv_mutex;
    std::condition_variable cv;

    bool                            filter_success = false;
    v3::ColorCorrectionFilterParams filter_params;

    std::mutex                             simg_mutex;
    bool                                   simg_allow_write = true;
    std::shared_ptr<DirectX::ScratchImage> simg;
};

using ColorCorrectionFilterProcessContextPtr = std::shared_ptr<ColorCorrectionFilterProcessContext>;

static void AsyncColorCorrectionFilterProcess(
    ID3D11Device*                            dev,
    ID3D11DeviceContext*                     ctx,
    ColorCorrectionFilterProcessContextPtr   actx,
    const std::function<void(bool success)>& complete_callback)
{

    bool                    success = false;
    ComPtr<ID3D11Texture2D> cpu_tex;

    do
    {

        if (!dev || !ctx || !actx->simg)
            break;

        auto simg = actx->simg.get();

        if (simg->GetImageCount() < 1)
            break;

        const DirectX::Image* img = simg->GetImages();

        ComPtr<ID3D11Resource>  img_res;
        ComPtr<ID3D11Texture2D> img_tex;
        ComPtr<ID3D11Texture2D> result_tex;

        HRESULT hr = DirectX::CreateTextureEx(dev, img, 1, simg->GetMetadata(), D3D11_USAGE_DEFAULT,
                                              D3D11_BIND_SHADER_RESOURCE, 0, 0, DirectX::CREATETEX_IGNORE_SRGB, img_res.GetAddressOf());

        // DirectX::SaveDDSTextureToFile(ctx, img_res.Get(), L"C:\\code\\src_img.dds");

        if (FAILED(hr))
            break;

        hr = img_res.As(&img_tex);
        if (FAILED(hr))
            break;

        D3D11_TEXTURE2D_DESC img_tex_desc;
        img_tex->GetDesc(&img_tex_desc);

        ComPtr<ID3D11ShaderResourceView> img_srv = graphics_helper::CreateTexture2DSRV(dev, img_tex.Get());
        if (!img_srv)
            break;

        result_tex = graphics_helper::CreateGpuTexture2D(dev, img_tex_desc.Width, img_tex_desc.Height, img_tex_desc.Format);
        if (!result_tex)
            break;

        cpu_tex = graphics_helper::CreateCpuMappableTexture2D(dev, img_tex_desc.Width, img_tex_desc.Height, img_tex_desc.Format);
        if (!cpu_tex)
            break;

        v3::ColorCorrectionFilter* filter = g_videoFrameProcessor ? g_videoFrameProcessor->GetColorCorrectionFilter() : nullptr;

        bool filter_ok = false;

        if (filter && filter->IsReady())
        {
            filter->UpdateParams(actx->filter_params);
            graphics_helper::ScopeRenderTarget scope_target(dev, ctx, result_tex.Get(), true);

            if (scope_target.IsVaild())
            {
                filter_ok = filter->Render(img_srv.Get());
            }
        }

        if (!filter_ok)
            break;

        ctx->CopyResource(cpu_tex.Get(), result_tex.Get());
        ctx->Flush();

        success = true;

    } while (0);

    if (!success)
    {
        complete_callback(false);
    }
    else
    {
        auto map_task = [complete_callback, cpu_tex, actx](ID3D11Device* dev, ID3D11DeviceContext* ctx, bool& need_continue) {
            if (actx->finish)
            {
                need_continue = false;
                return;
            }

            HRESULT init_img_hr = E_FAIL;

            D3D11_MAPPED_SUBRESOURCE map_info;
            HRESULT                  map_hr = ctx->Map(cpu_tex.Get(), 0, D3D11_MAP_READ, D3D11_MAP_FLAG_DO_NOT_WAIT, &map_info);

            if (SUCCEEDED(map_hr))
            {
                D3D11_TEXTURE2D_DESC desc;
                cpu_tex->GetDesc(&desc);

                DirectX::Image tmp_image;

                tmp_image.width = desc.Width;
                tmp_image.height = desc.Height;
                tmp_image.format = desc.Format;
                tmp_image.rowPitch = map_info.RowPitch;
                tmp_image.slicePitch = map_info.DepthPitch;
                tmp_image.pixels = (uint8_t*)map_info.pData;

                {
                    std::unique_lock<std::mutex> lk(actx->simg_mutex);
                    if (actx->simg_allow_write)
                        init_img_hr = actx->simg->InitializeFromImage(tmp_image, false);
                }

                ctx->Unmap(cpu_tex.Get(), 0);

                need_continue = false;
                complete_callback(SUCCEEDED(init_img_hr));
            }
            else
            {
                need_continue = map_hr == DXGI_ERROR_WAS_STILL_DRAWING;
                if (!need_continue)
                    complete_callback(false);
            }
        };

        g_videoFrameProcessor->SubmitLoopRenderTask(map_task);
    }
}

static bool ProcessImage(const std::wstring& inputPath,
                         const std::wstring& outputPath,
                         int32_t             raw_width,
                         int32_t             raw_height,
                         float               raw_scale_x,
                         float               raw_scale_y,
                         bool                flip_v,
                         bool                flip_h,
                         float               rotate,
                         int32_t             clip_x,
                         int32_t             clip_y,
                         int32_t             clip_z,
                         int32_t             clip_w,
                         ColorAdjustFilter*  color_adjust_filter)
{
    DirectX::ScratchImage originalImage;
    DirectX::TexMetadata  metadata;

    bool success = false;

    std::string errmsg;

    do
    {
        if (raw_width <= 0 || raw_height <= 0)
        {
            errmsg = "raw_width or raw_height <= 0";
            break;
        }

        if (raw_scale_x <= 0 || raw_scale_y <= 0)
        {
            errmsg = "raw_scale_x or raw_scale_y <= 0";
            break;
        }

        int32_t scaledWidth = (raw_width - clip_x - clip_z) * raw_scale_x;
        int32_t scaledHeight = (raw_height - clip_w - clip_y) * raw_scale_y;

        if (scaledWidth <= 0 || scaledHeight <= 0)
        {
            errmsg = "scaledWidth or scaledHeight <= 0";
            break;
        }

        HRESULT hr = DirectX::LoadFromWICFile(
            inputPath.c_str(),
            DirectX::WIC_FLAGS_NONE,
            &metadata,
            originalImage);

        if (FAILED(hr))
        {
            errmsg = "LoadFromWICFile fail";
            break;
        }

        int width = originalImage.GetMetadata().width;
        int height = originalImage.GetMetadata().height;

        if (width <= 0 || height <= 0)
        {
            errmsg = "originalImage width or height <= 0";
            break;
        }

        double scale_x = (double)width / raw_width;
        double scale_y = (double)height / raw_height;

        double scaled_clip_x = clip_x * scale_x;
        double scaled_clip_z = clip_z * scale_x;
        double scaled_clip_y = clip_y * scale_y;
        double scaled_clip_w = clip_w * scale_y;

        DirectX::Rect srcRect;
        srcRect.x = scaled_clip_x;
        srcRect.y = scaled_clip_y;
        srcRect.w = width - scaled_clip_z - scaled_clip_x;
        srcRect.h = height - scaled_clip_w - scaled_clip_y;

        if (srcRect.w <= 0 || srcRect.h <= 0 || srcRect.x > width || srcRect.y > height)
        {
            srcRect.x = 0;
            srcRect.y = 0;
            srcRect.w = width;
            srcRect.h = height;
        }

        DirectX::ScratchImage croppedImage;

        hr = croppedImage.Initialize2D(
            metadata.format,
            srcRect.w,
            srcRect.h,
            1, 1, DirectX::CP_FLAGS_NONE);
        if (FAILED(hr))
        {
            errmsg = "croppedImage.Initialize2D fail";
            break;
        }

        hr = DirectX::CopyRectangle(*originalImage.GetImages(), srcRect, *croppedImage.GetImages(), DirectX::TEX_FILTER_DEFAULT, 0, 0);
        if (FAILED(hr))
        {
            errmsg = "CopyRectangle fail";
            break;
        }

        DirectX::ScratchImage resizedImage;

        hr = DirectX::Resize(*croppedImage.GetImages(), scaledWidth, scaledHeight, DirectX::TEX_FILTER_DEFAULT, resizedImage);
        if (FAILED(hr))
        {
            errmsg = "Resize fail";
            break;
        }

        DirectX::TEX_FR_FLAGS flag = DirectX::TEX_FR_ROTATE0;

        rotate = std::fmod(rotate, 360.0);
        if (rotate < 0)
            rotate += 360.0;

        if (rotate > 89 && rotate < 91)
            flag = DirectX::TEX_FR_ROTATE90;
        else if (rotate > 179 && rotate < 181)
            flag = DirectX::TEX_FR_ROTATE180;
        else if (rotate > 269 && rotate < 271)
            flag = DirectX::TEX_FR_ROTATE270;

        if (flip_v)
            flag |= DirectX::TEX_FR_FLIP_VERTICAL;

        if (flip_h)
            flag |= DirectX::TEX_FR_FLIP_HORIZONTAL;

        auto flippedImage = std::make_shared<DirectX::ScratchImage>();

        hr = DirectX::FlipRotate(
            resizedImage.GetImages(),
            resizedImage.GetImageCount(),
            resizedImage.GetMetadata(),
            flag,
            *flippedImage);

        if (FAILED(hr))
        {
            errmsg = "FlipRotate fail";
            break;
        }

        if (color_adjust_filter)
        {

            auto actx = std::make_shared<ColorCorrectionFilterProcessContext>();

            actx->filter_params.brightness = color_adjust_filter->brightness / 100.0f;
            actx->filter_params.contrast = color_adjust_filter->contrast / 100.0f;
            actx->filter_params.hue_shift = color_adjust_filter->hueShift / 180.0f;
            actx->filter_params.saturation = color_adjust_filter->saturation / 100.0f;
            actx->filter_params.add_color = color_adjust_filter->addColor;
            actx->filter_params.mul_color = color_adjust_filter->mulColor;
            actx->filter_params.opacity = color_adjust_filter->opacity / 100.0f;
            actx->filter_params.gamma = color_adjust_filter->gamma;

            actx->simg = flippedImage;

            if (g_videoFrameProcessor)
            {
                g_videoFrameProcessor->SubmitRenderTask(
                    [actx](ID3D11Device* dev, ID3D11DeviceContext* ctx) {

                        auto complete_handler = [actx](bool success) {
                            std::unique_lock<std::mutex> lk(actx->cv_mutex);
                            if (!actx->finish)
                            {
                                actx->filter_success = success;
                                actx->finish = true;
                                actx->cv.notify_one();
                            }
                        };

                        AsyncColorCorrectionFilterProcess(dev, ctx, actx, complete_handler);
                    });

                {
                    std::unique_lock<std::mutex> lk(actx->cv_mutex);

                    actx->cv.wait_for(
                        lk,
                        std::chrono::milliseconds(2000),
                        [actx] { return actx->finish.load(); });

                    actx->finish = true;
                }

				{
                    std::unique_lock<std::mutex> lk(actx->simg_mutex);
                    actx->simg_allow_write = false;
				}
            }

            if (!actx->filter_success)
            {
                errmsg = "ColorCorrectionFilterProcess fail";
                break;
            }
        }

        hr = DirectX::SaveToWICFile(
            flippedImage->GetImages(),
            flippedImage->GetImageCount(),
            DirectX::WIC_FLAGS_FORCE_LINEAR,
            DirectX::GetWICCodec(DirectX::WIC_CODEC_JPEG),
            outputPath.c_str());

        if (FAILED(hr))
        {
            errmsg = "SaveToWICFile fail";
            break;
        }

        success = true;

    } while (0);

    if (!success)
    {
        LOG(INFO) << StringPrintf("[Controller::ProcessImage] success:0, errmsg:%s", errmsg.c_str());
    }

    return success;
}

void Controller::GetLayerSnapshot(UINT64 layerID, const std::string& path)
{
	std::string layerIDStr = "";
	Util::NumToString(layerID, &layerIDStr);
	g_sdkController->VisualSaveAsImage(layerIDStr, path);
}

bool Controller::GetLayerSnapshot2(UINT64 layerID, const std::string& path)
{
    std::string layerIDStr = "";
    Util::NumToString(layerID, &layerIDStr);

    bool success = false;

    std::wstring wtmpFile;
    std::string  tmpFile;

    do
    {
        bool exist = this->FindLayerByID(layerID);
        if (!exist)
        {
            LOG(INFO) << StringPrintf("[Controller::GetLayerSnapshot2] LayerId:%llu not existed", layerID);
            break;
        }

        LAYER_INFO layerInfo;
        GetLayerInfo(layerID, &layerInfo);

        std::error_code ec;
        auto            tmpFolderPath = std::filesystem::temp_directory_path(ec);
        if (ec)
        {
            LOG(INFO) << "[Controller::GetLayerSnapshot2] temp_directory_path fail";
            break;
        }

        static std::atomic_int64_t g_count = 0;
        ++g_count;

        std::string tmpFilename = StringPrintf("GetLayerSnapshot2_%lld.png", g_count.load());
        tmpFolderPath.append(tmpFilename);

        tmpFile = tmpFolderPath.string();
        wtmpFile = tmpFolderPath.wstring();

        bool ok = g_sdkController->VisualSaveAsImage(layerIDStr, tmpFile.c_str());
        if (!ok)
        {
            LOG(INFO) << "[Controller::GetLayerSnapshot2] VisualSaveAsImage fail";
            break;
        }

        std::wstring dstPath;

        Util::UTF8ToWString(path.c_str(), dstPath);

		ColorAdjustFilter colorAdjustFilter{};
        bool              foundColorAdjustFilter = false;

		for (const auto& filter : layerInfo.filters)
		{
            if (filter.type == FILTER_VISUAL)
            {
                colorAdjustFilter = std::get<VISUAL_FILTER>(filter.filter).colorAdjustFilter;
                foundColorAdjustFilter = true;
                break;
			}
            else if (filter.type == FILTER_CANVAS)
            {
                colorAdjustFilter = std::get<CANVAS_FILTER>(filter.filter).colorAdjustFilter;
                foundColorAdjustFilter = true;
                break;
			}
		}

        ok = ProcessImage(wtmpFile,
                          dstPath,
                          layerInfo.transform.size.Width,
                          layerInfo.transform.size.Height,
                          layerInfo.transform.scale.X,
                          layerInfo.transform.scale.Y,
                          layerInfo.transform.vFlip,
                          layerInfo.transform.hFlip,
                          layerInfo.transform.angle,
                          0,
                          0,
                          0,
                          0,
                          foundColorAdjustFilter ? &colorAdjustFilter : nullptr);

        if (!ok)
        {
            LOG(INFO) << "[Controller::GetLayerSnapshot2] ProcessImage fail";
            break;
        }

        success = true;

    } while (0);

    if (!wtmpFile.empty())
    {
        std::error_code ec;
        std::filesystem::remove(wtmpFile, ec);
    }

    return success;
}

void Controller::GetEffectProfiler(UINT64 layerID, UINT64 sourceID, EffectProfilerInfo* effectProfilerInfo)
{
	std::string source_id = "";
	Util::NumToString(sourceID, &source_id);
	if (SourceMgr::GetInstance()->GetCompositeRealType(sourceID) == VISUAL_IMAGE)
		return;

	std::string layer_id = "";
	Util::NumToString(layerID, &layer_id);
	g_sdkController->VisualGetEffectProfiler(layer_id, effectProfilerInfo);
}

void Controller::UpdateLayerSource(UINT64 layerID, UINT64 sourceID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
		ModeSceneMgr::GetInstance()->GetLayerInfoByID(layerID, &layerInfo);
		
		if (layerInfo.sourceID == sourceID)
		{
			LOG(INFO) << "[Controller::UpdateLayerSource] update layer source is same, newSourceID: " << sourceID << ", oldSourceID: " << layerInfo.sourceID;
			return;
		}

		if (SourceMgr::GetInstance()->CheckSourceExist(sourceID))
			SourceMgr::GetInstance()->TransferLayerBindingsWithTransform(layerInfo.sourceID, sourceID);
	}
}

bool Controller::CreateFrame(UINT64 mediaID, Gdiplus::SizeF resize, CLIPF clip, OBJECT_FIT_MODE fitMode, UINT64* frameID)
{
    std::string mediaIDStr = "";
    Util::NumToString(mediaID, &mediaIDStr);

    *frameID = ModeSceneMgr::GetInstance()->AllocFrameID();
    std::string frameIDStr = "";
    Util::NumToString(*frameID, &frameIDStr);
	if (!g_videoFrameProcessor->GrabFrame(mediaIDStr, frameIDStr, v3::ClipResizeOrderEnum::kClipResizeOrder_ClipFirst, static_cast<v3::FitModeEnum>(fitMode), resize.Width, resize.Height, clip.x, clip.y, clip.z, clip.w))
	{
        LOG(ERROR) << "[Controller::CreateFrame] GrabFrame failed";
        return false;
	}

    return true;
}

void Controller::HandleLayerSizeChange(UINT64 layerID)
{
    LAYER_INFO layerInfo{};
    GetLayerInfo(layerID, &layerInfo);
    SOURCE_INFO sourceInfo{};
    GetSourceInfo(layerInfo.sourceID, &sourceInfo);
    ModeSceneMgr::GetInstance()->HandleLayerSizeChange(layerID, sourceInfo.size.Width, sourceInfo.size.Height);
}

void Controller::CalLayerTransform(const LAYER_INFO& layerInfo, UINT64 cmd)
{
    Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerInfo.id);
	if (pLayer)
	{
        pLayer->CalLayerTransform(cmd, layerInfo.canvasID);
	}
}

bool Controller::ResetParfait(PARFAIT_PARAM param)
{
	g_sdkController->UpdateHost(param.host);
	return LogDumpMgr::GetInstance()->ResetParfait(param);
}

void Controller::SetParfaitContextInfo(const std::string& key, const std::string& val)
{
	LogDumpMgr::GetInstance()->SetParfaitContextInfo(key, val);
}

bool Controller::StartWatchdog(const char* pipeName, int timeout)
{
	if (WatchdogClient::GetInstance().IsRunning())
		return false;

	WatchdogClient::GetInstance().RegisterThread(THREADNAME_RENDER);
	WatchdogClient::GetInstance().RegisterThread(THREADNAME_BACK);
	WatchdogClient::GetInstance().SetTimeout(timeout);
	WatchdogClient::GetInstance().SetPipeName(StringToWString(pipeName, CP_UTF8));
	WatchdogClient::GetInstance().Create();
	return true;
}

void Controller::StopWatchdog()
{
	WatchdogClient::GetInstance().Destroy();
}

void Controller::EnableFrozenMonitor(int timeout)
{
	IThreadMonitor* threadMonitor = (IThreadMonitor*)g_cief->QueryInterface(LSINAME_THREADMONITOR);
	if (threadMonitor)
	{
		UINT64 timeoutu64 = timeout;
		threadMonitor->SetFrozenTime(timeoutu64);
	}
}

void Controller::ServerCrash()
{
	int a = 0;
	LOG(INFO) << StringPrintf("Server crash");
	LOG(INFO) << StringPrintf("%d", 10 / a);
}

void Controller::MockFrozen(int ipcTimeout, int renderTimeout)
{
	if (ipcTimeout > 0)
	{
		LOG(INFO) << "Mock ipc timeout[" << ipcTimeout << "]";
		std::this_thread::sleep_for(std::chrono::milliseconds(ipcTimeout));
	}
	if (renderTimeout > 0)
	{
		LOG(INFO) << "Mock render timeout[" << ipcTimeout << "]";
		g_sdkController->MockFrozen(ipcTimeout, renderTimeout);
	}
}

bool Controller::SelectMode(LIVE_MODE mode)
{
	bool success = ModeSceneMgr::GetInstance()->SelectMode(mode);
	return success;
}

UINT64 Controller::AddSceneWithInfo(LIVE_MODE mode, SCENE_INFO* sceneInfo)
{
	Mode* pMode = ModeSceneMgr::GetInstance()->GetModeByID(mode);
	if (pMode)
	{
        Mode* pCurMode = ModeSceneMgr::GetInstance()->GetCurrentMode();
        if (m_isStudioMode && (pCurMode == pMode))
        {
            sceneInfo->previewID = m_previewViewID;
        }
		return pMode->AddSceneWithInfo(sceneInfo);
	}
	return 0;
}

void Controller::AddSceneByID(LIVE_MODE mode, UINT64 sceneID)
{
	return ModeSceneMgr::GetInstance()->BindModeScene(mode, sceneID);
}

void Controller::RemoveScene(LIVE_MODE mode, UINT64 sceneID)
{
	Mode* pMode = ModeSceneMgr::GetInstance()->GetModeByID(mode);
	if (pMode)
	{
		Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
		pMode->RemoveScene(pScene);
	}
}

void Controller::GetPreviewInfo(UINT32 previewID, PREVIEW_INFO* info)
{
    Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(previewID);
	if (pView)
	{
        pView->GetPreviewInfo(info);
	}
}

UINT64 Controller::CreateScene(SCENE_INFO* sceneInfo)
{
    return ModeSceneMgr::GetInstance()->CreateScene(sceneInfo, NULL, &sceneInfo->mode);
}

void Controller::DeleteScene(UINT64 sceneID)
{
	ModeSceneMgr::GetInstance()->DestroyScene(sceneID);
}

UINT64 Controller::CloneScene(UINT64 sceneID)
{
	// TODO: @xuwanhui
    return 0;
}

bool Controller::FindSceneByID(UINT64 sceneID)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (!pScene)
		return false;
	return true;
}

void Controller::SelectScene(UINT64 sceneID, bool reloadWhenSwitchMode)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (!pScene)
		return;
	SCENE_INFO_EX sceneInfo = {};
	pScene->GetSceneInfo(&sceneInfo, true);
	Mode* pMode = ModeSceneMgr::GetInstance()->GetModeByID(sceneInfo.mode);
	if (!pMode)
		return;

	pMode->SelectScene(sceneID, reloadWhenSwitchMode);
    return;

	if (!m_isStudioMode)
	{
        pMode->SelectScene(sceneID, reloadWhenSwitchMode);
	}
	else
	{
		// 如果当前是 StudioMode，切换的场景应该进行预览，而非推流
        CANVAS_INFO_EX canvasInfo = sceneInfo.canvas[0];
		if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasInfo.id))
		{
            pCanvas->SetSyncMedia(true);
            if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(canvasInfo.previewID))
            {
                pView->BindCanvas(canvasInfo.id);
            }
		}
	}
}

void Controller::GetSceneInfo(UINT64 sceneID, SCENE_INFO* info)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (pScene)
	{
		SCENE_INFO_EX infoEx = {};
		pScene->GetSceneInfo(&infoEx);
		*info = *(SCENE_INFO*)&infoEx;
	}
}

void Controller::PreLoadScene(UINT64 sceneID, bool preloadWhenSwitchMode)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (pScene)
	{
		if (preloadWhenSwitchMode)
		{
			pScene->Select(true, SWITCH_SCENE_REPRELOAD);
		}
		else
		{
			pScene->Select(true, SWITCH_SCENE_PRELOAD);
		}
	}
}

UINT32 Controller::AddPreview(PREVIEW_INFO* previewInfo)
{
	if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(previewInfo->videoModel))
	{
        PREVIEW_INFO info;
        pView->GetPreviewInfo(&info);
        return info.id;
	}
	else
	{
        return ModeSceneMgr::GetInstance()->AddPreview(previewInfo, nullptr);
	}
}

void Controller::SetPreviewInfo(UINT32 previewID, const PREVIEW_INFO* info)
{
	Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(previewID);
	if (pView)
	{
		pView->SetPreviewInfo(info);
    }
}

bool Controller::SwitchStudioMode(UINT64 programCanvasID, bool enable)
{
    LOG(INFO) << "[Controller::SwitchStudioMode] programCanvasID: " << programCanvasID << ", enable: " << enable;

	if (enable)
	{
        m_isStudioMode = true;

        PREVIEW_INFO   previewViewInfo;
		CANVAS_INFO_EX programCanvasInfo;
		if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(programCanvasID))
        {
            pCanvas->GetCanvasInfo(&programCanvasInfo, true);
			PREVIEW_INFO programViewInfo;
            ModeSceneMgr::GetInstance()->GetPreviewInfoByID(programCanvasInfo.previewID, &programViewInfo);
            previewViewInfo.bkColor = programViewInfo.bkColor;
            previewViewInfo.fps = programViewInfo.fps;
            previewViewInfo.hwnd = programViewInfo.hwnd;
		}

		UINT64 previewCanvasID = CloneCanvas(programCanvasID);
		previewViewInfo.curCanvasID = previewCanvasID;
        previewViewInfo.canvasIDs.push_back(previewCanvasID);
        UINT32 previewViewID = ModeSceneMgr::GetInstance()->AddPreview(&previewViewInfo, nullptr);

        CANVAS_INFO_EX previewCanvasInfo;
        if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(previewCanvasID))
        {
            pCanvas->GetCanvasInfo(&previewCanvasInfo, true);
            pCanvas->SetPreviewID(previewViewID);
        }

		// 更新 CurScene 的 Canvas
		if (Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(programCanvasInfo.sceneID))
		{
            pScene->ReBindCanvas(programCanvasID, previewCanvasID);
		}

		// 将 ProgramView CanvasIDs 中其它的 Canvas 转到 PreviewView
		// 一方面是需要更新 PreviewInfo 中的 canvasIDs
		// 另一方面是需要更新 Canvas 对应 CanvasInfo 中的 previewID
		// 其它场景因为不存在新的画布，本身跟 Scene 的绑定是不变的
		if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(programCanvasInfo.previewID))
		{
            PREVIEW_INFO viewInfo;
            pView->GetPreviewInfo(&viewInfo);
            
			for (const auto& canvasID : viewInfo.canvasIDs)
			{
                if (canvasID == viewInfo.curCanvasID)
					continue;

				previewViewInfo.canvasIDs.push_back(canvasID);
				if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
				{
                    pCanvas->SetPreviewID(previewViewID);
				}
			}

			pView->SetCanvasIDs({programCanvasID});
		}

		if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(previewViewID))
		{
            pView->SetPreviewInfo(&previewViewInfo);
		}

		// 回调场景信息给前端
		OnSceneUpdateEvent(programCanvasInfo, previewCanvasInfo);
        m_previewViewID = previewViewID;
        m_programViewID = programCanvasInfo.previewID;

        g_sdkController->PreviewWindowEnableInteract(m_programViewID, false);
        g_sdkController->EnableFineTuning(m_programViewID, false);
        g_sdkController->SelectLayer(m_programViewID, "");
    }
	else
	{
		// 关闭 StudioMode
		// 取消当前状态为非 StudioMode
        m_isStudioMode = false;
        m_isApplyedTransition = false;

        PREVIEW_INFO previewViewInfo;
		if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(m_previewViewID))
		{
            pView->GetPreviewInfo(&previewViewInfo);
		}

		// preview 上所有的画布都要重新设置到 programView 上，仅 curCanvas 设置到 preview 上
		if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(m_programViewID))
		{
            PREVIEW_INFO programViewInfo;
            pView->GetPreviewInfo(&programViewInfo);

			// Destroy programCanvas
            if (!ModeSceneMgr::GetInstance()->DestroyCanvas(programViewInfo.curCanvasID))
            {
                LOG(ERROR) << "[Controller::SwitchStudioMode] DestroyCanvas failed, programView CurCanvasID: " << programViewInfo.curCanvasID;
                return false;
            }

            programViewInfo.canvasIDs.clear();
            for (const auto& canvasID : previewViewInfo.canvasIDs)
            {
                if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
				{
                    LOG(INFO) << "[Controller::SwitchStudioMode] SetPreviewID, canvasID: " << canvasID << ", programViewID: " << m_programViewID;
                    pCanvas->SetPreviewID(m_programViewID);
				}
                programViewInfo.canvasIDs.push_back(canvasID);

				if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
				{
                    CANVAS_INFO_EX canvasInfoEx;
                    pCanvas->GetCanvasInfo(&canvasInfoEx, true);

					for (auto& layerInfo : canvasInfoEx.layers)
                    {
                        layerInfo.isStudioMode = false;
                        SetLayerInfo(layerInfo.id, layerInfo);

						for (auto& filterInfo : layerInfo.filters)
						{
                            if (filterInfo.type == FILTER_EFFECT)
							{
                                EFFECT_FILTER filter = std::get<EFFECT_FILTER>(filterInfo.filter);
                                filter.useEmptyFrameBeforeEffectProcess = true;
                                filter.forceDisableHideOriginFrame = false;
                                filterInfo.filter = filter;
                                SetFilterInfo(filterInfo.id, &filterInfo);
							}
						}
                    }
				}
				
            }
            programViewInfo.curCanvasID = previewViewInfo.curCanvasID;
            pView->SetPreviewInfo(&programViewInfo);
			if (!pView->BindCanvas(programViewInfo.curCanvasID))
			{
                LOG(ERROR) << "[Controller::SwitchStudioMode] BindCanvas failed, curCanvasID: " << programViewInfo.curCanvasID << ", programViewID: " << m_programViewID;
                return false;
			}
		}

		ModeSceneMgr::GetInstance()->DestroyPreview(m_previewViewID);
        m_previewViewID = 0;

        g_sdkController->PreviewWindowEnableInteract(m_programViewID, true);
        g_sdkController->EnableFineTuning(m_programViewID, true);
	}

	return true;
}

void Controller::OnSceneUpdateEvent(const CANVAS_INFO_EX& oldCanvasInfo, const CANVAS_INFO_EX& newCanvasInfo)
{
    SceneUpdateEvent event;
    std::string      scene_id = "";
    Util::NumToString(oldCanvasInfo.sceneID, &scene_id);
    event.sceneID = scene_id;

	std::string old_preview_id = "";
    Util::NumToString(oldCanvasInfo.previewID, &old_preview_id);
    event.oldPreviewID = old_preview_id;
	
	std::string new_preview_id = "";
	Util::NumToString(newCanvasInfo.previewID, &new_preview_id);
    event.newPreviewID = new_preview_id;

    CanvasUpdateInfo canvasUpdateInfo;
    std::string      canvas_id = "";
    Util::NumToString(newCanvasInfo.id, &canvas_id);
    canvasUpdateInfo.canvasID = canvas_id;

    event.canvasInfos.push_back(canvasUpdateInfo);
    eventbus::EventBus::PostEvent(event);
}

bool Controller::ApplyTransition(UINT64 programCanvasID, UINT64 previewCanvasID)
{
	// 先拷贝 previewCanvasID 出 newCanvasID
    UINT64 newPreviewCanvasID = CloneCanvas(previewCanvasID);
	if (newPreviewCanvasID <= 0)
	{
        LOG(ERROR) << "[Controller::ApplyTransition] CloneCanvas failed, newPreviewCanvasID: " << newPreviewCanvasID;
        return false;
	}

	LOG(INFO) << "[Controller::ApplyTransition] newPreviewCanvasID: " << newPreviewCanvasID << ", programCanvasID: " << programCanvasID << ", previewCanvasID: " << previewCanvasID;

	CANVAS_INFO_EX previewCanvasInfo;
    ModeSceneMgr::GetInstance()->GetCanvasInfoByID(previewCanvasID, &previewCanvasInfo);

	// 回调新的 canvas 信息给前端，进行新 Canvas 中 Layer 的创建
    CANVAS_INFO_EX newPreviewCanvasInfo;
    ModeSceneMgr::GetInstance()->GetCanvasInfoByID(newPreviewCanvasID, &newPreviewCanvasInfo);
    OnSceneUpdateEvent(previewCanvasInfo, newPreviewCanvasInfo);

	// 将 previewCanvasID 作为 videoModel 输出 Preview 的 Canvas
	if (Preview* pProgramView = ModeSceneMgr::GetInstance()->GetPreviewByID(m_programViewID))
	{
		// 将 previewCanvasID 从 previewView 上解绑
        if (Preview* pPreviewView = ModeSceneMgr::GetInstance()->GetPreviewByID(m_previewViewID))
        {
            if (!pPreviewView->UnbindCanvas(previewCanvasID))
            {
                LOG(WARNING) << "[Controller::ApplyTransition] UnbindCanvas failed, previewCanvasID: " << previewCanvasID;
            }
        }
		// 前端将会通过 SwitchScene 将 newPreviewCanvas 添加到 previewView 上

		// 更新 Canvas 的 previewID
        if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(previewCanvasID))
        {
            pCanvas->SetPreviewID(m_programViewID);
        }

		// 将 previewCanvas 作为 ProgramView 的 Canvas
		if (!pProgramView->BindCanvas(previewCanvasID))
		{
            LOG(ERROR) << "[Controller::ApplyTransition] BindCanvas failed, previewCanvasID: " << previewCanvasID;
            return false;
		}

		// 删除 programCanvasID
        if (!ModeSceneMgr::GetInstance()->DestroyCanvas(programCanvasID))
        {
            LOG(ERROR) << "[Controller::ApplyTransition] DestroyCanvas failed, programCanvasID: " << programCanvasID;
            return false;
        }
	}

	// 更新 CurScene 的 Canvas
	if (Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(previewCanvasInfo.sceneID))
	{
        pScene->ReBindCanvas(previewCanvasID, newPreviewCanvasID);
    }

	m_isApplyedTransition = true;
	return true;
}

UINT64 Controller::CreateCanvas(CANVAS_INFO* info)
{
	return ModeSceneMgr::GetInstance()->AddCanvas(info, 0);
}

UINT64 Controller::GetCanvasID(UINT64 sceneID, UINT32 oldCanvasIdx)
{
    if (oldCanvasIdx >= LIVE_MODE_MAX)
        return 0;
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (pScene)
	{
		Canvas* pCanvas = pScene->GetCanvas(oldCanvasIdx);
		if (pCanvas)
		{
			CANVAS_INFO_EX canvasInfo;
			pCanvas->GetCanvasInfo(&canvasInfo);
			return canvasInfo.id;
		}
	}
	return 0;
}

bool Controller::DestroyCanvas(UINT64 canvasID)
{
	return ModeSceneMgr::GetInstance()->DestroyCanvas(canvasID);
}

void Controller::PreviewCanvas(PREVIEW_INFO* info)
{
    if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(info->curCanvasID))
    {
        g_mediaMgr->SetDisplay(info->id, true);
        UpdatePreviewLayout(*info);
        pCanvas->Select(true);
    }
}

void Controller::DestroyPreview(UINT32 previewID)
{
	if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(previewID))
	{
        PREVIEW_INFO previewInfo;
        pView->GetPreviewInfo(&previewInfo);

		ModeSceneMgr::GetInstance()->DestroyPreview(previewID);
		
		for (const auto& canvasID : previewInfo.canvasIDs)
		{
            DestroyCanvas(canvasID);
		}
	}
}

void Controller::UpdatePreviewLayout(const PREVIEW_INFO& previewInfo)
{
    if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(previewInfo.id))
	{
        pView->UpdatePreviewLayout(previewInfo);
	}
}

void Controller::GetCanvasInfo(UINT64 canvasID, CANVAS_INFO* info)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && info)
	{
		CANVAS_INFO_EX infoEx = {};
		pCanvas->GetCanvasInfo(&infoEx);
		*info = *(CANVAS_INFO*)&infoEx;
	}
}

void Controller::SetCanvasInfo(UINT64 canvasID, const CANVAS_INFO* info)
{
    if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
	{
		pCanvas->SetCanvasInfo(info);
	}
}

UINT64 Controller::CloneCanvas(UINT64 canvasID)
{
    if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
    {
        CANVAS_INFO_EX originCanvasInfo;
        pCanvas->GetCanvasInfo(&originCanvasInfo, true);

		// Create Real New Canvas
        CANVAS_INFO newCanvasInfo;
        newCanvasInfo.sceneID = originCanvasInfo.sceneID;
        newCanvasInfo.oldCanvasIdx = originCanvasInfo.oldCanvasIdx;
        newCanvasInfo.layoutRect = originCanvasInfo.layoutRect;
		newCanvasInfo.previewID = m_previewViewID;
        UINT64      newCanvasID = ModeSceneMgr::GetInstance()->AddCanvas(&newCanvasInfo, nullptr);
        if (Canvas* pNewCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(newCanvasID))
        {
            if (!pNewCanvas->Create())
            {
                LOG(ERROR) << "[Controller::CloneCanvas] Canvas Create failed, canvasID: " << newCanvasID;
                return 0;
            }

			// 确保前端在 SwitchScene 之前能往新创建出的画布上添加源
			pNewCanvas->SetSyncMedia(true);
			LOG(INFO) << "[Controller::CloneCanvas] Create New Canvas, new canvasID: " << newCanvasID << ", original canvasID: " << canvasID;
        }

		return newCanvasID;
    }

    return 0;
}

void Controller::EnumLayers(UINT64 canvasID, CANVAS_INFO_EX* infoEx)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && infoEx)
	{
		pCanvas->GetCanvasInfo(infoEx, true);
	}
}

bool Controller::FindCanvasByID(UINT64 canvasID)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (!pCanvas)
		return false;

	return true;
}

bool Controller::CheckCanvasByID(UINT64 canvasID)
{
    Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    if (!pCanvas)
        return false;

    CANVAS_INFO_EX canvasInfoEx{};
    pCanvas->GetCanvasInfo(&canvasInfoEx);
    return canvasInfoEx.isCreated;
}

void Controller::UpdateCanvasLayout(const CANVAS_INFO& canvasInfo)
{
	if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasInfo.id))
	{
        pCanvas->UpdateCanvasLayerLayout(canvasInfo);
	}
}

void Controller::UpdateLayersOrder(UINT64 canvasID, const std::vector<UINT64>& layerIDs, std::vector<UINT64>* moreLayerIDs, std::vector<UINT64>* lessLayerIDs)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
		pCanvas->UpdateWholeLayersOrder(layerIDs, moreLayerIDs, lessLayerIDs);
}

void Controller::MoveLayerOrder(UINT64 canvasID, UINT64 layerID, MOVE_ORDER move)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
		pCanvas->MoveLayerOrder(layerID, move);
}

UINT64 Controller::AddLayerWithInfo(UINT64 canvasID, LAYER_INFO* layerInfo)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas && layerInfo)
		return pCanvas->CreateLayerWithInfo(layerInfo, true, 0);
	return 0;
}

bool Controller::AddLayerByID(UINT64 canvasID, UINT64 layerID)
{
    if (m_isStudioMode)
    {
		if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID))
		{
            LAYER_INFO layerInfo;
            pLayer->GetLayerInfo(&layerInfo);

            SOURCE_INFO sourceInfo;
            GetSourceInfo(layerInfo.sourceID, &sourceInfo);

            CANVAS_INFO canvasInfo;
            GetCanvasInfo(canvasID, &canvasInfo);

			if (canvasInfo.previewID == m_previewViewID && (sourceInfo.type == VISUAL_CAMERA || (sourceInfo.type == VISUAL_BYTELINK && m_isApplyedTransition)))
			{
                layerInfo.isStudioMode = true;
                pLayer->SetLayerInfo(&layerInfo);
			}
		}
    }

	bool success = ModeSceneMgr::GetInstance()->BindCanvasLayer(canvasID, layerID);
	return success;
}

void Controller::RemoveLayer(UINT64 canvasID, UINT64 layerID)
{
	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	if (pCanvas)
	{
		Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
		pCanvas->DestroyLayer(pLayer);
    }
}

bool Controller::CreateLayer(LAYER_INFO* layerInfo)
{
	layerInfo->id = ModeSceneMgr::GetInstance()->AddLayer(layerInfo, NULL);
	return layerInfo->id > 0;
}

void Controller::AddBrowserLayer(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo)
{
    if (sourceInfo && SourceMgr::GetInstance()->FindSourceByID(sourceInfo->id))
    {
        ModeSceneMgr::GetInstance()->OnBrowserSourceUpdate(layerInfo, sourceInfo);
    }
}

void Controller::DeleteLayer(UINT64 layerID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (!pLayer)
		return;

	ModeSceneMgr::GetInstance()->DestroyLayer(layerID);
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(layerID);
    if (pAudio)
        AudioMgr::GetInstance()->DestoryAudio(pAudio);
}

void Controller::ControlLayer(UINT64 layerID, LAYER_INFO& layerInfo, LAYER_CONTROL_CMD cmd)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
        if (!(cmd & LAYER_CONTROL_SET_LAYOUT) && (cmd & LAYER_CONTROL_SET_ROTATE || cmd & LAYER_CONTROL_SET_TRANSLATE || cmd & LAYER_CONTROL_SET_SCALE))
        {
            layerInfo.layout = LAYOUT_NONE;
        }
        pLayer->ControlLayer(layerInfo, cmd);

		Source* pSource = SourceMgr::GetInstance()->GetSourceByID(layerInfo.sourceID);
		if (pSource)
		{
            SOURCE_INFO sourceInfo{};
            pSource->GetSourceInfo(&sourceInfo);
            if (sourceInfo.type == VISUAL_FAV && (cmd & LAYER_CONTROL_SET_SHOW))
            {
                FAV_SOURCE fav = std::get<FAV_SOURCE>(sourceInfo.source);
                fav.seekTime = .0f;
                fav.absolute = true;
                sourceInfo.source = fav;
                UINT64 sourceCMD = SOURCE_CONTROL_SEEK;
				pSource->ControlSource(sourceInfo, static_cast<SOURCE_CONTROL_CMD>(sourceCMD));
            }
		}
	}
}

void Controller::GetLayerInfo(UINT64 layerID, LAYER_INFO* layerInfo)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if(pLayer && layerInfo)
		pLayer->GetLayerInfo(layerInfo);
}

void Controller::SetLayerInfo(UINT64 layerID, const LAYER_INFO& layerInfo)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
		pLayer->SetLayerInfo(&layerInfo);
}

bool Controller::FindLayerByID(UINT64 layerID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (!pLayer)
		return false;
	return true;
}

void Controller::ReopenLayer(UINT64 canvasID, LAYER_INFO* layerInfo)
{
    Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    if (pCanvas && layerInfo)
    {
        if (FindLayerByID(layerInfo->id))
        {
			std::vector<UINT64> eOrder{};
            std::vector<UINT64> order{};
            pCanvas->GetLayersOrder(&order);
			
			ModeSceneMgr::GetInstance()->DestroyLayer(layerInfo->id);
			pCanvas->CreateLayerWithInfo(layerInfo, true, &layerInfo->id);
			UpdateLayersOrder(canvasID, order, NULL, NULL);
        }
    }
}

void Controller::SelectLayer(UINT64 layerID)
{
    if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID))
    {
        SelectLayerWithValidLayer(layerID, pLayer);
    }
    else
    {
        SelectLayerWithoutValidLayer(layerID);
    }
}

void Controller::SelectLayerWithValidLayer(UINT64 layerID, Layer* pLayer)
{
    LAYER_INFO layerInfo;
    pLayer->GetLayerInfo(&layerInfo);

    UINT64 canvasID = layerInfo.canvasID;
    Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    if (!pCanvas)
    {
        LOG(ERROR) << "[Controller::SelectLayer] Canvas not found, canvasID: " << canvasID;
        return;
    }

    CANVAS_INFO_EX canvasInfoEx;
    pCanvas->GetCanvasInfo(&canvasInfoEx);

    // Select layer on the current canvas
    std::string layer_id = "";
    Util::NumToString(layerID, &layer_id);
    if (!g_sdkController->SelectLayer(canvasInfoEx.previewID, layer_id))
    {
        LOG(ERROR) << "[Controller::SelectLayer] SelectLayer failed, layerID: " << layerID
                   << ", previewID: " << canvasInfoEx.previewID;
    }

    // Handle additional preview selection for DBCANVAS mode
    HandleDBCanvasModeSelection(layerID, canvasInfoEx.previewID);
}

void Controller::SelectLayerWithoutValidLayer(UINT64 layerID)
{
    if (m_isStudioMode)
    {
        HandleStudioModeSelection(layerID);
    }
    else
    {
        HandleNormalModeSelection(layerID);
    }
}

void Controller::HandleDBCanvasModeSelection(UINT64 layerID, UINT32 currentPreviewID)
{
    Mode* pMode = ModeSceneMgr::GetInstance()->GetCurrentMode();
    if (!pMode)
        return;

    MODE_INFO_EX modeInfoEx{};
    pMode->GetModeInfo(&modeInfoEx);

    if (modeInfoEx.id != LIVE_MODE_DBCANVAS)
        return;

    // Get preview info for current preview
    PREVIEW_INFO previewInfo;
    GetPreviewInfo(currentPreviewID, &previewInfo);

    // Try to select layer on other DBCANVAS previews (video models 2 and 3)
    const std::vector<UINT32> dbCanvasVideoModels = {2, 3};

    for (UINT32 videoModel : dbCanvasVideoModels)
    {
        UINT32 previewID = 0;
        if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &previewID) != nullptr
            && previewID != currentPreviewID)
        {
            if (!g_sdkController->SelectLayer(previewID, ""))
            {
                LOG(ERROR) << "[Controller::SelectLayer] SelectLayer failed, layerID: " << layerID
                           << ", previewID: " << previewID;
            }
            break; // Only select on the first available different preview
        }
    }
}

void Controller::HandleStudioModeSelection(UINT64 layerID)
{
    const std::vector<UINT32> studioPreviewIDs = {m_previewViewID, m_programViewID};

    for (UINT32 previewID : studioPreviewIDs)
    {
        if (!g_sdkController->SelectLayer(previewID, ""))
        {
            LOG(ERROR) << "[Controller::SelectLayer] SelectLayer failed, layerID: " << layerID
                       << ", previewID: " << previewID;
        }
    }
}

void Controller::HandleNormalModeSelection(UINT64 layerID)
{
    std::vector<UINT32> previewIDs = GetPreviewIDsForCurrentMode();

    for (UINT32 previewID : previewIDs)
    {
        if (!g_sdkController->SelectLayer(previewID, ""))
        {
            LOG(ERROR) << "[Controller::SelectLayer] SelectLayer failed, layerID: " << layerID
                       << ", previewID: " << previewID;
        }
    }
}

std::vector<UINT32> Controller::GetPreviewIDsForCurrentMode()
{
    std::vector<UINT32> previewIDs;

    Mode* pMode = ModeSceneMgr::GetInstance()->GetCurrentMode();
    if (!pMode)
        return previewIDs;

    MODE_INFO_EX modeInfoEx{};
    pMode->GetModeInfo(&modeInfoEx);

    UINT32 previewID = 0;

    switch (modeInfoEx.id)
    {
        case LIVE_MODE_LANDSCAPE:
            if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(0, &previewID))
                previewIDs.push_back(previewID);
            break;

        case LIVE_MODE_PORTRAIT:
            if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(1, &previewID))
                previewIDs.push_back(previewID);
            break;

        case LIVE_MODE_DBCANVAS:
            // For DBCANVAS mode, get both video models 2 and 3
            for (UINT32 videoModel = 2; videoModel <= 3; ++videoModel)
            {
                if (ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &previewID))
                    previewIDs.push_back(previewID);
            }
            break;

        default:
            LOG(WARNING) << "[Controller::GetPreviewIDsForCurrentMode] Unknown mode: " << modeInfoEx.id;
            break;
    }

    return previewIDs;
}

void Controller::RemoveGraffiti(UINT64 sourceID)
{
	std::string sourceIDStr = "";
	Util::NumToString(sourceID, &sourceIDStr);
	g_sdkController->GraffitiSourceRemove(sourceIDStr);
}

void Controller::RemoveAllGraffiti(UINT64 sourceID, bool enable)
{
	std::string sourceIDStr = "";
	Util::NumToString(sourceID, &sourceIDStr);
	g_sdkController->GraffitiSourceRemoveAll(sourceIDStr, enable);
}

bool Controller::StartLayerPreview(UINT64 layerID, UINT64 parent, const LAYER_PREVIEW* preview, UINT64* previewID, const PREVIEW_PARAMS& params)
{
	if (previewID)
	{
        *previewID = ModeSceneMgr::GetInstance()->AllocPreviewID();
        Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
        if (pLayer)
        {
            pLayer->SetLayerPreview(*previewID, preview);
        }

		std::string layerIDStr = "";
        Util::NumToString(layerID, &layerIDStr);

        std::string previewIDStr = "";
        Util::NumToString(*previewID, &previewIDStr);
        return g_sdkController->StartLayerPreview(layerIDStr, previewIDStr, parent, *preview, params);
	}

	return false;
}

bool Controller::StopLayerPreview(UINT64 layerID, UINT64 previewID)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_PREVIEW preview{};
		pLayer->SetLayerPreview(previewID, &preview);
	}

	std::string previewIDStr = "";
	Util::NumToString(previewID, &previewIDStr);
	return g_sdkController->StopLayerPreview(previewIDStr);
}

bool Controller::SetLayerPreviewLayout(UINT64 layerID, UINT64 previewID, const LAYER_PREVIEW* preview)
{
    Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		pLayer->SetLayerPreview(previewID, preview);
	}

	std::string previewIDStr = "";
	Util::NumToString(previewID, &previewIDStr);
	return g_sdkController->SetLayerPreviewSetting(previewIDStr, *preview);
}

void Controller::GetLayerPreview(UINT64 layerID, UINT64 previewID, LAYER_PREVIEW* preview)
{
    Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
		pLayer->GetLayerPreview(previewID, preview);
}

bool Controller::SetLayerPreviewParams(const std::string& layerID, const std::string& previewID, const PREVIEW_PARAMS* params)
{
	return g_sdkController->SetLayerPreviewParams(previewID, *params);
}

bool Controller::CreateSource(SOURCE_INFO& sourceInfo)
{
	if (SourceMgr::GetInstance()->FindSourceByID(sourceInfo.id))
		return true;
	
	return SourceMgr::GetInstance()->CreateSource(&sourceInfo, NULL) > 0;
}

bool Controller::PauseSource(const std::string& sourceID)
{
	return g_sdkController->MediaSourcePause(sourceID);
}

bool Controller::ResumeSource(const string& sourceID)
{
	return g_sdkController->MediaSourceResume(sourceID);
}

bool Controller::ReopenSource(UINT64 sourceID, SOURCE_INFO& sourceInfo)
{
	return SourceMgr::GetInstance()->ReopenSource(sourceID, &sourceInfo);
}

void Controller::DestroySource(UINT64 sourceID)
{
	SourceMgr::GetInstance()->DestroySource(sourceID);
}

void Controller::ControlSource(UINT64 sourceID, SOURCE_INFO& sourceInfo, SOURCE_CONTROL_CMD cmd)
{
	Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceID);
	if (pSource)
	{
		pSource->ControlSource(sourceInfo, cmd);
	}
}

void Controller::SetSourceInfo(UINT64 sourceID, const SOURCE_INFO* sourceInfo)
{
	Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceID);
	if (pSource)
	{
		pSource->SetSourceInfo(sourceInfo);
	}
}

bool Controller::FindSourceByID(UINT64 sourceID)
{
    return SourceMgr::GetInstance()->FindSourceByID(sourceID);
}

void Controller::GetSourceInfo(UINT64 sourceID, SOURCE_INFO* info, SOURCE_INFO_CMD cmd /*= SOURCE_INFO_NONE*/)
{
	Source* pSource = SourceMgr::GetInstance()->GetSourceByID(sourceID);
	if (pSource)
	{
		pSource->GetSourceInfo(info, cmd);
	}
}

std::unordered_map<UINT64, COMPOSITE_SOURCE_META> Controller::GetCompositeMetas()
{
	return SourceMgr::GetInstance()->GetCompositeMetas();
}

bool Controller::IsMatchedSource(SOURCE_INFO& sourceInfo)
{
	return SourceMgr::GetInstance()->IsMatchedSource(sourceInfo);
}

void Controller::SetCompositeMetas(const std::unordered_map<UINT64, COMPOSITE_SOURCE_META>& metas)
{
	SourceMgr::GetInstance()->SetCompositeMetas(metas);
}

void Controller::EnumAppAudio(std::vector<DSHOW>* devices)
{
	g_sdkController->AppAudioSourceEnum(devices);
}

void Controller::EnumCaptureAudio(std::vector<DSHOW>* devices)
{
	g_sdkController->WASAPIAudioSourceEnumInputDevices(devices);
}

void Controller::EnumRenderAudio(std::vector<DSHOW>* devices)
{
	g_sdkController->WASAPIAudioSourceEnumOutputDevices(devices);
}

void Controller::GetDefaultInput(DSHOW* device)
{
	g_sdkController->WASAPIAudioSourceGetDefaultInputDevice(device);
}

void Controller::GetDefaultOutput(DSHOW* device)
{
	g_sdkController->WASAPIAudioSourceGetDefaultOutputDevice(device);
}

void Controller::SystemSupportAppAudio(bool* support)
{
	g_sdkController->AppAudioSourceIsSystemSupport(support);
}

UINT64 Controller::AddAudio(AUDIO_INFO* audioInfo)
{
	if (audioInfo->type == AUDIO_VIS)
	{
		if (FindLayerByID(audioInfo->id))
			return AudioMgr::GetInstance()->CreateAudio(audioInfo, &audioInfo->id);
	}
	else
	{
		return AudioMgr::GetInstance()->CreateAudio(audioInfo);
	}

	return 0;
}

void Controller::DeleteAudio(UINT64 audioID)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
    if (pAudio)
		AudioMgr::GetInstance()->DestoryAudio(pAudio);
}

bool Controller::ControlAudio(UINT64 audioID, AUDIO_CONTROL_INFO* info)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(info->audioInfo.id);
    if (!pAudio)
        return false;

    if (!pAudio->ControlAudio(info))
        return false;

	AUDIO_INFO audioInfo{};
	pAudio->GetAudioInfo(&audioInfo);
	if (audioInfo.audioCaptureID > 0)
	{
		if (CaptureAudio* pCaptureAudio = AudioMgr::GetInstance()->GetCaptureAudioByID(audioInfo.audioCaptureID))
		{
			CAPTURE_AUDIO_INFO captureAudioInfo{};
			pCaptureAudio->GetCaptureAudioInfo(&captureAudioInfo);

			UINT64 cmd = AUDIO_CONTROL_NONE;
			if (info->cmd & AUDIO_CONTROL_SET_SYNC_OFFSET)
			{
				cmd |= AUDIO_CONTROL_SET_SYNC_OFFSET;
			}
			if (info->cmd & AUDIO_CONTROL_SET_AUDIO_TRACK)
			{
				cmd |= AUDIO_CONTROL_SET_AUDIO_TRACK;
				if (audioInfo.audioTrack == 0)
				{
					captureAudioInfo.audioTrack = 0;
				}
				else
				{
					captureAudioInfo.audioTrack = CAPTURE_AUDIO_TRACK_0;
				}

				if (!ChangeAudioTrack(audioID, CAPTURE_AUDIO_TRACK_0, CAPTURE_AUDIO_TRACK_5))
					LOG(WARNING) << "[Controller::ControlAudio] ChangeAudioTrack audioID: " << audioID << " AUDIO_TRACK_0 to AUDIO_TRACK_5 failed";
			}

			if (cmd != AUDIO_CONTROL_NONE)
			{
				captureAudioInfo.audioSetting = audioInfo.audioSetting;
				info->captureAudioInfo = captureAudioInfo;
				info->cmd = static_cast<AUDIO_CONTROL_CMD>(cmd);
				pCaptureAudio->ControlCaptureAudio(info);
			}
		}
	}

	return true;
}

void Controller::SetRenderDeviceID(const std::string& deviceID)
{
	g_sdkController->AudioSourceSetRenderDeviceID(deviceID);
}

bool Controller::SetANSOption(const AUDIO_ANS_OPTION& ansOption)
{
	return g_sdkController->LyraxEngineSetANSOption("", ansOption);
}

void Controller::GetAudioInfo(UINT64 audioID, AUDIO_INFO* info, AUDIO_INFO_CMD cmd)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
    if (pAudio)
		pAudio->GetAudioInfo(info, cmd);
}

bool Controller::FindAudioByID(UINT64 audioID)
{
    Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
    if (!pAudio)
		return false;
	return true;
}

bool Controller::StartListenAudio(const std::string& audioID, const std::string& audio_capture_id)
{
    if (!g_audioFrameProcessor->AudioInputStartListen(audioID, v3::AudioFrameProcessor::AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw, audio_capture_id))
	{
		LOG(ERROR) << "[Controller::StartListenAudio] AudioInputStartListen failed, audioID: " << audioID;
		return false;
	}

	return true;
}

bool Controller::StopListenAudio(const std::string& audioID)
{
	if (!g_audioFrameProcessor->AudioInputStopListen(audioID))
	{
        LOG(ERROR) << "[Controller::StopListenAudio] AudioInputStopListen failed, audioID: " << audioID;
        return false;
	}

	return true;
}

bool Controller::ChangeAudioTrack(UINT64 audioID, UINT32 oriTrack, UINT32 tarTrack)
{
	if (audioID == 0)
		return true;
	
	Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID);
	if (!pAudio)
	{
		LOG(ERROR) << "[Controller::ChangeAudioTrack] audio is invalid, audioID: " << audioID;
		return false;
	}

	AUDIO_INFO audioInfo{};
	pAudio->GetAudioInfo(&audioInfo);

	if ((audioInfo.audioTrack & oriTrack) != 0)
	{
		audioInfo.audioTrack = (audioInfo.audioTrack & ~oriTrack) | tarTrack;

		AUDIO_CONTROL_INFO audioControlInfo{};
		audioControlInfo.audioInfo = audioInfo;
		audioControlInfo.cmd = AUDIO_CONTROL_SET_AUDIO_TRACK;
		return pAudio->ControlAudio(&audioControlInfo);
	}

	return true;
}

UINT64 Controller::CreateCaptureAudio(const CAPTURE_AUDIO_INFO* captureAudioInfo)
{
    UINT64 captureAudioID = AudioMgr::GetInstance()->CreateCaptureAudio(captureAudioInfo);
	if (captureAudioID > 0)
	{
		if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(captureAudioInfo->originAudioID))
		{
			AUDIO_INFO audioInfo{};
			pAudio->GetAudioInfo(&audioInfo);
			audioInfo.audioCaptureID = captureAudioID;
			pAudio->SetAudioInfo(&audioInfo);

			std::string audioIDStr = "";
            Util::NumToString(audioInfo.id, &audioIDStr);

			std::string audioCaptureIDStr = "";
            Util::NumToString(captureAudioID, &audioCaptureIDStr);
            if (!StartListenAudio(audioIDStr, audioCaptureIDStr))
			{
                LOG(ERROR) << "[Controller::CreateCaptureAudio] StartListenAudio failed, audioID: " << audioInfo.id;
                return 0;
			}
		}
		
		return captureAudioID;
	}

	return 0;
}

bool Controller::DeleteCaptureAudio(UINT64 captureAudioID)
{
	if (CaptureAudio* pCaptureAudio = AudioMgr::GetInstance()->GetCaptureAudioByID(captureAudioID))
	{
		CAPTURE_AUDIO_INFO captureAudioInfo{};
		pCaptureAudio->GetCaptureAudioInfo(&captureAudioInfo);

		if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(captureAudioInfo.originAudioID))
        {
            AUDIO_INFO audioInfo{};
            pAudio->GetAudioInfo(&audioInfo);
            audioInfo.audioCaptureID = 0;
            pAudio->SetAudioInfo(&audioInfo);

            std::string audioIDStr = "";
            Util::NumToString(audioInfo.id, &audioIDStr);
            if (!StopListenAudio(audioIDStr))
            {
                LOG(ERROR) << "[Controller::DeleteCaptureAudio] StopListenAudio failed, audioID: " << audioInfo.id;
                return false;
            }
        }

		if (!AudioMgr::GetInstance()->DestroyCaptureAudio(pCaptureAudio, captureAudioInfo.enableWrite))
		{
            LOG(ERROR) << "[Controller::DeleteCaptureAudio] DestroyCaptureAudio failed, captureAudioID: " << captureAudioID;
			return false;
		}
	}

	return true;
}

bool Controller::FindCaptureAudioByID(UINT64 captureAudioID)
{
	return AudioMgr::GetInstance()->GetCaptureAudioByID(captureAudioID) != nullptr;
}

void Controller::GetCaptureAudioInfo(UINT64 captureAudioID, CAPTURE_AUDIO_INFO* info)
{
	CaptureAudio* pCaptureAudio = AudioMgr::GetInstance()->GetCaptureAudioByID(captureAudioID);
	if (pCaptureAudio)
		pCaptureAudio->GetCaptureAudioInfo(info);
}

bool Controller::CaptureAudioEnableAutoMute(UINT64 captureAudioID, bool enable)
{
	if (CaptureAudio* pCaptureAudio = AudioMgr::GetInstance()->GetCaptureAudioByID(captureAudioID))
	{
		return pCaptureAudio->EnableAutoMute(captureAudioID, enable);
	}
	return false;
}

bool Controller::CaptureAudioSetOriginAudio(UINT64 captureAudioID, UINT64 originAudioID)
{
	if (CaptureAudio* pCaptureAudio = AudioMgr::GetInstance()->GetCaptureAudioByID(captureAudioID))
	{
		if (pCaptureAudio->SetOriginAudio(originAudioID))
		{
			if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(originAudioID))
			{
				AUDIO_INFO audioInfo{};
				pAudio->GetAudioInfo(&audioInfo);
				audioInfo.audioCaptureID = captureAudioID;
				pAudio->SetAudioInfo(&audioInfo);
			}

			return true;
		}
	}

	return false;
}

bool Controller::CaptureAudioSetReplaceRange(UINT64 captureAudioID, INT64 beginPTSMs, INT64 endPTSMs, bool* hit, INT64* bufferBeginPTSMs, INT64* bufferEndPTSMs, bool enableDump, const std::string& dumpTag, const std::string& dumpPath)
{
	if (CaptureAudio* pCaptureAudio = AudioMgr::GetInstance()->GetCaptureAudioByID(captureAudioID))
	{
		return pCaptureAudio->SetReplaceRange(captureAudioID, beginPTSMs, endPTSMs, hit, bufferBeginPTSMs, bufferEndPTSMs,
			enableDump, dumpTag, dumpPath);
	}

	return false;
}

bool Controller::EnableAudioInputEchoDetection(UINT64 audioID, const int interval)
{
    std::string audio_id = "";
    Util::NumToString(audioID, &audio_id);
    return g_sdkController->LyraxEngineEnableAudioInputEchoDetection(audio_id, interval);
}

bool Controller::EnableAudioInputNoiseDetection(UINT64 audioID, const int interval, const int duration)
{
    std::string audio_id = "";
    Util::NumToString(audioID, &audio_id);
    return g_sdkController->LyraxEngineEnableAudioInputNoiseDetection(audio_id, interval, duration);
}

bool Controller::SetAudioInputRenderDeviceID(UINT64 audioID, const std::string& deviceID)
{
    std::string audio_id = "";
    Util::NumToString(audioID, &audio_id);
    return g_sdkController->LyraxEngineSetAudioInputRenderDeviceID(audio_id, deviceID.c_str());
}

void Controller::BindFilter(UINT64 mediaID, UINT64 filterID)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
	{
		FILTER info;
		pFilter->GetFilterInfo(&info);
		if (info.type == FILTER_VISUAL || info.type == FILTER_EFFECT)
		{
			Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(mediaID);
			if (pLayer)
				pLayer->BindFilter(&info);
		}
		else if (info.type == FILTER_AUDIO)
		{
			Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(mediaID);
			if (pAudio)
				pAudio->BindFilter(&info);
		}
		else if (info.type == FILTER_CANVAS)
		{
			Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(mediaID);
			if (pCanvas)
				pCanvas->BindFilter(&info);
		}
	}
}

void Controller::UnBindFilter(UINT64 mediaID, UINT64 filterID)
{
    Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
	{
        FILTER info;
        pFilter->GetFilterInfo(&info);
		if (info.type == FILTER_VISUAL || info.type == FILTER_EFFECT)
		{
            Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(mediaID);
            if (pLayer)
                pLayer->UnBindFilter(&info);
		}
        else if (info.type == FILTER_AUDIO)
        {
            Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(mediaID);
            if (pAudio)
                pAudio->UnBindFilter(&info);
        }
        else if (info.type == FILTER_CANVAS)
        {
            Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(mediaID);
            if (pCanvas)
                pCanvas->UnBindFilter(&info);
        }
	}
}

void Controller::ResetFilterOrder(UINT64 layerID, const std::vector<std::string>& filterIDs)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
	    pLayer->GetLayerInfo(&layerInfo);

		std::string layerIDStr = "";
		Util::NumToString(layerID, &layerIDStr);

		std::vector<std::string> convertedFilterIDs{};
		for (const auto filterID : filterIDs)
		{
			auto it = std::find_if(layerInfo.filters.begin(), layerInfo.filters.end(), [filterID](FILTER filterInfo) {
				UINT64 id = 0;
				bool success = Util::StringToNum(filterID, &id);
				if (!success)
				{
					LOG(ERROR) << "[Controller::ResetFilterOrder] Util::StringToNum failed, filterID in: " << filterID << ", out: " << id;
					return false;
				}
				return filterInfo.id == id;
			});

			if (it != layerInfo.filters.end())
			{
				if (it->type == FILTER_VISUAL)
				{
                    auto filter = std::get<VISUAL_FILTER>(it->filter);
                    if (filter.filterType == VISUAL_FILTER_CHROMA_KEY)
                    {
						convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_chromakey");
                    }
					else if (filter.filterType == VISUAL_FILTER_COLOR_ADJUST)
					{
					    convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_coloradjust");
					}
					else if (filter.filterType == VISUAL_FILTER_COLOR_LUT)
					{
						convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_color_lut");
					}
					else if (filter.filterType == VISUAL_FILTER_SHARPNESS)
					{
						convertedFilterIDs.push_back(layerIDStr + "_" + filterID + "_sharpness");
					}
					else
					{
					    convertedFilterIDs.push_back(layerIDStr + "_" + filterID);
					}
				}
				else if (it->type == FILTER_EFFECT)
				{
				    convertedFilterIDs.push_back(layerIDStr + "_filter");
				}
			}
		}
		g_sdkController->ResetFilterOrder(layerIDStr, convertedFilterIDs);

		layerInfo.filters = ResetLayerFilters(layerInfo.filters, filterIDs);
		pLayer->SetLayerInfo(&layerInfo);
	}
}

std::vector<FILTER> Controller::ResetLayerFilters(std::vector<FILTER> filters, std::vector<std::string> filterIDs)
{
	std::vector<FILTER> result{};
	std::vector<FILTER> reOrder{};
	std::unordered_set<std::string> filterIDSet(filterIDs.begin(), filterIDs.end());

	for (const auto& filter : filters)
	{
		std::string filterIDStr = "";
		bool ret = Util::NumToString(filter.id, &filterIDStr);
		if (!ret)
		{
			LOG(ERROR) << "[Controller::ResetLayerFilters] til::StringToNum failed, filters filterID in: " << filter.id << ", out: " << filterIDStr;
			continue;
		}

		if (filterIDSet.count(filterIDStr))
		{
			reOrder.push_back(filter);
		}
		else
		{
			result.push_back(filter);
		}
	}

	int insertPos = 0;
	for (const std::string& filterID : filterIDs)
	{
		for (int i = 0; i < reOrder.size(); ++i)
		{
			std::string filterIDStr = "";
			bool ret = Util::NumToString(reOrder[i].id, &filterIDStr);
			if (!ret)
			{
                LOG(ERROR) << "[Controller::ResetLayerFilters] til::StringToNum failed, reOrder filterID in: " << filterID << ", out: " << filterIDStr;
                continue;
			}

			if (filterIDStr == filterID)
			{
				result.insert(result.begin() + insertPos, reOrder[i]);
				insertPos++;
			}
		}
	}

	return result;
}

UINT64 Controller::CreateFilter(FILTER* filterInfo)
{
    if (m_isStudioMode && filterInfo->type == FILTER_EFFECT)
    {
        EFFECT_FILTER filter = std::get<EFFECT_FILTER>((*filterInfo).filter);
        filter.useEmptyFrameBeforeEffectProcess = false;
        filter.forceDisableHideOriginFrame = true;
        filterInfo->filter = filter;
    }
	return FilterMgr::GetInstance()->CreateFilter(filterInfo);
}

void Controller::DeleteFilter(UINT64 filterID)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		FilterMgr::GetInstance()->DeleteFilter(pFilter);
}

void Controller::ControlFilter(UINT64 filterID, const FILTER& info, FILTER_CONTROL_CMD cmd)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		pFilter->ControlFilter(info, cmd);
}

void Controller::GetFilterInfo(UINT64 filterID, FILTER* info, FILTER_INFO_CMD cmd)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		pFilter->GetFilterInfo(info, cmd);
}

void Controller::SetFilterInfo(UINT64 filterID, const FILTER* info)
{
	Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		pFilter->SetFilterInfo(info);
}

bool Controller::FindFilterByID(UINT64 filterID)
{
    Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterID);
	if (pFilter)
		return true;
	return false;
}

void Controller::SetMixParameter(UINT32 videoModel, VIDEO_MIX_PARAM param)
{
	UINT32 mainSceneModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	if (mainSceneModel != UINT32_MAX)
	{
		videoModel = mainSceneModel;
	}
	
	g_mediaMgr->SetMixParameter(videoModel, param);
}

void Controller::OutputThumbnail(LIVE_MODE mode, UINT64 sceneID, UINT64 canvasID, const std::string& path, IMAGE_FORMAT format, void* cbparam)
{
	Scene* scene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	if (!scene)
		return;

	Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    if (pCanvas)
    {
        CANVAS_INFO_EX canvasInfo;
        pCanvas->GetCanvasInfo(&canvasInfo, true);
        if (ModeSceneMgr::GetInstance()->IsCurrentScene(sceneID))
        {
            ::Sleep(50);

			UINT32 previewID = canvasInfo.previewID;
			if (m_isStudioMode)
                previewID = m_previewViewID;

            if (!g_sdkController->PreviewWindowSaveAsImage(previewID, format, path))
                LOG(ERROR) << "[Controller::OutputThumbnail] PreviewWindowSaveAsImage failed, previewID: " << previewID << ", is_studio_mode: " << m_isStudioMode;
			
            struct OutputThumbnailEvent evt;
            evt.functor = cbparam;
            evt.success = true;
            eventbus::EventBus::PostEvent(evt);
        }
        else
        {
            canvasInfo.allSync = true;
            pCanvas->SetCanvasInfo(&canvasInfo);

			// 切换场景以绑定 画布到 Preview 上
            scene->Select(true, SWITCH_SCENE_NORMAL);
			if (!g_sdkController->PreviewWindowEnableShow(canvasInfo.previewID, false))
			{
                LOG(ERROR) << "[Controller::OutputThumbnail] PreviewWindowEnableShow failed, previewID: " << canvasInfo.previewID << ", show: " << 0;
			}

			// 更新画布上源 Order
            pCanvas->UpdateWholeLayersOrder(canvasInfo.order, NULL, NULL);
            m_thumbnailInfo.mode = mode;
            m_thumbnailInfo.sceneID = sceneID;
            m_thumbnailInfo.previewID = canvasInfo.previewID;
            m_thumbnailInfo.path = path;
            m_thumbnailInfo.format = format;
            m_thumbnailInfo.cbparam = cbparam;
            m_timer_thumbnail = TimerMgr_SetTimer(0, 3000, this);
        }
    }
}

void Controller::OutputThumbnailInternal(void* param)
{
    Scene* scene = ModeSceneMgr::GetInstance()->GetSceneByID(m_thumbnailInfo.sceneID);
    if (!scene)
        return;

    // 对画布进行截图
    g_sdkController->PreviewWindowSaveAsImage(m_thumbnailInfo.previewID, m_thumbnailInfo.format, m_thumbnailInfo.path);

    // 前端会删除场景
    // DeleteScene(m_thumbnailInfo.sceneID);

    // 删除 Preview
    PREVIEW_INFO previewInfo;
    ModeSceneMgr::GetInstance()->GetPreviewInfoByID(m_thumbnailInfo.previewID, &previewInfo);
    if (previewInfo.videoModel != UINT32_MAX)
    {
        g_mediaMgr->DestroyVideoModel(previewInfo.videoModel);
    }
    g_mediaMgr->DestroyPreview(m_thumbnailInfo.previewID);

	struct OutputThumbnailEvent evt;
	evt.functor = m_thumbnailInfo.cbparam;
	evt.success = true;
	eventbus::EventBus::PostEvent(evt);
}

void Controller::HandleTimer(UINT64 id)
{
    if (id == m_timer_thumbnail)
    {
        TimerMgr_KillTimer(m_timer_thumbnail);
        g_cief->GetThreadMgr()->AddTaskToBackThread(new MemberTask<Controller>(this, &Controller::OutputThumbnailInternal, 0));
    }
}

typedef BOOL(WINAPI* LPFN_GLPI)(PSYSTEM_LOGICAL_PROCESSOR_INFORMATION, PDWORD);
INT Controller::GetCpuDeviceInfoExt(CPU_INFO& cpuinfo)
{
    LPFN_GLPI                             glpi;
    BOOL                                  done = FALSE;
    PSYSTEM_LOGICAL_PROCESSOR_INFORMATION buffer = NULL;
    PSYSTEM_LOGICAL_PROCESSOR_INFORMATION ptr = NULL;
    DWORD                                 returnLength = 0;
    DWORD                                 logicalProcessorCount = 0;
    DWORD                                 processorCoreCount = 0;
    DWORD                                 processorPackageCount = 0;
    DWORD                                 byteOffset = 0;
    PCACHE_DESCRIPTOR                     Cache;

    glpi = (LPFN_GLPI)GetProcAddress(GetModuleHandle(TEXT("kernel32")), "GetLogicalProcessorInformation");
    if (NULL == glpi)
    {
        LOG_LINE(ERROR) << StringPrintf("[Controller::GetCpuDeviceInfoExt] GetLogicalProcessorInformation is not supported.");
        return (1);
    }

    while (!done)
    {
        DWORD rc = glpi(buffer, &returnLength);

        if (FALSE == rc)
        {
            if (GetLastError() == ERROR_INSUFFICIENT_BUFFER)
            {
                if (buffer)
                    free(buffer);

                buffer = (PSYSTEM_LOGICAL_PROCESSOR_INFORMATION)malloc(
                    returnLength);

                if (NULL == buffer)
                {
                    LOG_LINE(ERROR) << StringPrintf("[Controller::GetCpuDeviceInfoExt] GetLogicalProcessorInformation: Allocation failure");
                    return (2);
                }
            }
            else
            {
                LOG_LINE(ERROR) << StringPrintf("[Controller::GetCpuDeviceInfoExt] GetLogicalProcessorInformation get error %d"), GetLastError();
                return (3);
            }
        }
        else
        {
            done = TRUE;
        }
    }

    ptr = buffer;

    while (byteOffset + sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION) <= returnLength)
    {
        if (ptr->Relationship == RelationProcessorCore)
        {
			++cpuinfo.coreNum;
        }
        byteOffset += sizeof(SYSTEM_LOGICAL_PROCESSOR_INFORMATION);
		++ptr;
    }

    free(buffer);
    buffer = NULL;

    int cpuInfo[4] = {0};
    __cpuid(cpuInfo, 1);

    cpuinfo.family = (cpuInfo[0] >> 8) & 0xf;
    cpuinfo.model = (cpuInfo[0] >> 4) & 0xf;
    cpuinfo.stepping = cpuInfo[0] & 0xf;
    return 0;
}

void Controller::EnumVideoEnc(std::vector<VIDEO_ENCODER_INFO>* oVideoEncoders)
{
	g_sdkController->EnumVideoEncoders(oVideoEncoders);
}

void Controller::GetVideoCodecParam(const std::string& codecID, INT32* oErrCode, VIDEO_CODEC_PARAM* oParam)
{
	g_sdkController->EncoderGetInfo(codecID, oErrCode, oParam);
}

void Controller::StartLive(std::vector<OUTPUT_INFO> infos)
{
	for (auto& info : infos)
	{
		info.videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(info.videoModel);
		m_streamIDMap[info.streamID] = info;
	}
	g_sdkController->StartStream(infos);
}

void Controller::StopLive(const std::vector<STOP_STREAM_PARAM>& streamParams)
{
	g_sdkController->StopStream(streamParams);
	for (const auto& param : streamParams)
	{
		m_streamIDMap.erase(param.streamID);
	}
}

void Controller::SetStreamRoomID(const std::string& roomID)
{
	g_sdkController->StreamSetRoomID(roomID);
}

void Controller::SetMultipleIFrameSEI(std::vector<SEI_INFO> infos)
{
	std::vector<SEI_INFO> seiInfos{};
    for (auto& info : infos)
    {
        info.videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(info.videoModel);
		std::string streamID = "";
		{
            for (const auto& iter : m_streamIDMap)
            {
                const auto& outputInfo = iter.second;
                if (outputInfo.videoModel == info.videoModel)
                {
                    if (outputInfo.streamInfo.type != STREAM_TYPE::STREAM_RECORD_FFMPEG && outputInfo.streamInfo.type != STREAM_TYPE::STREAM_RECORD_FLV)
                    {
                        streamID = outputInfo.streamID;
                        break;
                    }
                }
            }
		}
        if (streamID.empty())
            continue;

		info.streamID = streamID;
		seiInfos.push_back(info);
    }

	bool success = g_sdkController->SetIFrameSEI(seiInfos);
	if (!success)
		LOG(ERROR) << "[Controller::SetMultipleIFrameSEI] SetIFrameSEI failed";
}

void Controller::StartBwProbeStream(OUTPUT_INFO info, bool* running)
{
	info.videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(info.videoModel);
	g_sdkController->BwProbeStartStream(info, running);
}

void Controller::StopBwProbeStream(const std::string& streamID, bool* fallback)
{
	g_sdkController->BwProbeStopStream(streamID, fallback);
}

void Controller::IsStreamInProcess(const std::string& streamID, bool* is_streaming)
{
	g_sdkController->IsStreamInProcess(streamID, is_streaming);
}

void Controller::ABRConfig(const std::string& streamID, UINT32 offset)
{
	g_sdkController->StreamSetAbrConfig(streamID, offset);
}

void Controller::GetActiveStatistic(const std::string& streamID, ACTIVE_STATISTIC* statistic)
{
	g_sdkController->GetStreamActiveStatistic(streamID, statistic);
}

bool Controller::StartRTC(RTC_LINK link)
{
	link.videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(link.videoModel);
	return g_sdkController->RTCControllerStart(link);
}

bool Controller::StopRTC()
{
	return g_sdkController->RTCControllerStop();
}

bool Controller::StartScreenShare(UINT32 videoModel, const CLIP_AREA_INFO& info, bool enableAudio, UINT32 audioTrack)
{
	videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	return g_sdkController->RTCControllerStartScreenShare(videoModel, info, enableAudio, audioTrack);
}

bool Controller::StopScreenShare(UINT32 videoModel)
{
	videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(videoModel);
	return g_sdkController->RTCControllerStopScreenShare(videoModel);
}

bool Controller::UpdateScreenShareClipInfo(const CLIP_AREA_INFO& info)
{
	return g_sdkController->RTCControllerUpdateScreenShareClipInfo(info);
}

bool Controller::UpdateClipScaleInfo(const CLIP_AREA_INFO& info)
{
    return g_sdkController->RTCControllerUpdateClipScaleInfo(info);
}

bool Controller::StartLiveTranscoding(const std::string& args)
{
	return g_sdkController->RTCControllerStartLiveTranscoding(args);
}

bool Controller::UpdateLiveTranscoding(const std::string& args)
{
	return g_sdkController->RTCControllerUpdateLiveTranscoding(args);
}

bool Controller::StopLiveTranscoding(const std::string& args)
{
	return g_sdkController->RTCControllerStopLiveTranscoding(args);
}

bool Controller::StartForwardStreamToRooms(const std::vector<ROOM_INFO>& infos, INT32* result)
{
	return g_sdkController->RTCControllerStartForwardStreamToRooms(infos, result);
}

bool Controller::UpdateForwardStreamToRooms(const std::vector<ROOM_INFO>& infos, INT32* result)
{
	return g_sdkController->RTCControllerUpdateForwardStreamToRooms(infos, result);
}

bool Controller::StopForwardStreamToRooms()
{
	return g_sdkController->RTCControllerStopForwardStreamToRooms();
}

bool Controller::PublishVideoStream(MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerPublishVideoStream(type);
}

bool Controller::UnPublishVideoStream(MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerUnPublishVideoStream(type);
}

bool Controller::SendRTCUserMessage(const std::string& uid, const std::string& msg)
{
	return g_sdkController->RTCControllerSendRTCUserMessage(uid, msg);
}

bool Controller::SendRTCRoomMessage(const std::string& msg)
{
	return g_sdkController->RTCControllerSendRTCRoomMessage(msg);
}

bool Controller::SubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerSubscribeScreen(uid, type);
}

bool Controller::UnSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type)
{
	return g_sdkController->RTCControllerUnSubscribeScreen(uid, type);
}

bool Controller::SetAudioOutput(const std::string& deviceID)
{
	return g_sdkController->RTCControllerSetAudioOutput(deviceID);
}

bool Controller::SetAudioPropertiesReport(UINT32 interval)
{
	return g_sdkController->RTCControllerSetAudioPropertiesReport(interval);
}

bool Controller::MuteRemoteAudio(const std::string& userID, UINT32 streamIdx, bool muteRemoteAudio)
{
	return g_sdkController->RTCControllerMuteRemoteAudio(userID, streamIdx, muteRemoteAudio);
}

bool Controller::EnableLocalAudio(bool enable)
{
	return g_sdkController->RTCControllerEnableLocalAudio(enable);
}

bool Controller::EnableLocalVideo(bool enable)
{
	return g_sdkController->RTCControllerEnableLocalVideo(enable);
}

void Controller::CameraSourceIsUsingRealCamera(UINT64 layerID, bool* value)
{
	Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
		pLayer->GetLayerInfo(&layerInfo);

		SOURCE_INFO sourceInfo{};
		GetSourceInfo(layerInfo.sourceID, &sourceInfo);
		if (sourceInfo.type == VISUAL_CAMERA && sourceInfo.isCreated)
			*value = true;
	}

	*value = false;
}

void Controller::GetCPUUsage(float* processUsage, float* sysUsage1, float* sysUsage2)
{
	MonitorMgr::GetInstance()->GetCPUUsage(processUsage, sysUsage1, sysUsage2);
}

void Controller::GetMemUsage(float* processUsage, float* sysUsage, UINT64* totalSize, float* pageFault, float* commitMemoryUsage)
{
	MonitorMgr::GetInstance()->GetMemUsage(processUsage, sysUsage, totalSize, pageFault, commitMemoryUsage);
}

void Controller::GetActiveFPS(float* fps)
{
	g_sdkController->GetActiveFPS(fps);
}

void Controller::GetNotReadyFPS(float* fps)
{
	g_sdkController->GetPresentNotReadyFPS(fps);
}

std::vector<VIDEO_ADAPTER_INFO> Controller::GetGPUInfo()
{
	return MonitorMgr::GetInstance()->GetGPUInfo();
}

CPU_INFO Controller::GetCpuInfo()
{
	static CPU_INFO s_cpuInfo;
	if (!s_cpuInfo.processorName.empty())
	{
		return s_cpuInfo;
	}

	CPU_INFO cpuInfo;
	HKEY hKey = { 0 };
	long lRet = RegOpenKeyEx(HKEY_LOCAL_MACHINE, L"HARDWARE\\DESCRIPTION\\System\\CentralProcessor\\0", 0, KEY_READ, &hKey);
	if (lRet == ERROR_SUCCESS)
	{
		DWORD dwType = 0;
		WCHAR chCPUName[1024] = { 0 };
		DWORD dwSize = 500;

		lRet = RegQueryValueEx(hKey, L"ProcessorNameString", NULL, &dwType, (LPBYTE)&chCPUName[0], &dwSize);
		if (ERROR_SUCCESS == lRet)
		{
			cpuInfo.processorName = chCPUName;
		}
		else
		{
			LOG_LINE(ERROR) << StringPrintf("RegQueryValueEx ProcessorNameString FAILED: %d", lRet);
		}

		dwType = REG_DWORD;
		DWORD dwValue;
		lRet = RegQueryValueEx(hKey, L"~MHz", NULL, &dwType, (LPBYTE)&dwValue, &dwSize);
		if (ERROR_SUCCESS == lRet)
		{
			cpuInfo.maxClockSpeed = dwValue;
		}
		else
		{
			LOG_LINE(ERROR) << StringPrintf("RegQueryValueEx MHz FAILED: %d", lRet);
		}
	}
	else
	{
		LOG_LINE(ERROR) << StringPrintf("RegOpenKeyEx FAILED: %d", lRet);
	}
	(void)RegCloseKey(hKey);

	SYSTEM_INFO si;
	memset(&si, 0, sizeof(SYSTEM_INFO));
	GetSystemInfo(&si);
	cpuInfo.processNum = si.dwNumberOfProcessors;
	GetCpuDeviceInfoExt(cpuInfo);
	s_cpuInfo = cpuInfo;
	return cpuInfo;
}

std::vector<MONITOR_INFO> Controller::GetMonitorsInfo()
{
	std::vector<MONITOR_INFO> monitors;
	IDXGIFactory* pdxFactory;
	HRESULT hr = CreateDXGIFactory(IID_PPV_ARGS(&pdxFactory));
	if (SUCCEEDED(hr))
	{
		int nAdapter = 0;
		while (1)
		{
			IDXGIAdapter* pdxAdapter;
			hr = pdxFactory->EnumAdapters(nAdapter, &pdxAdapter);
			if (FAILED(hr))
				break;

			DXGI_ADAPTER_DESC dxAdapterDesc;
			hr = pdxAdapter->GetDesc(&dxAdapterDesc);
			if (FAILED(hr))
			{
				LOG_LINE(ERROR) << "GetDesc GPU FAILED";
			}
			int nOutput = 0;
			while (1)
			{
				IDXGIOutput* pdxOutput;
				if (FAILED(pdxAdapter->EnumOutputs(nOutput, &pdxOutput)))
					break;
				MONITOR_INFO monitor;
				monitor.gpuIndex = nOutput;
				DXGI_OUTPUT_DESC dxOutputDesc;
				hr = pdxOutput->GetDesc(&dxOutputDesc);
				DEVMODEW mode;
				if (EnumDisplaySettingsW(dxOutputDesc.DeviceName, ENUM_CURRENT_SETTINGS, &mode))
				{
					monitor.refresh = mode.dmDisplayFrequency;
				}

				int modeIndex = 0;
				int maxWidth = 0, maxHeight = 0;

				// 获取物理最大分辨率
				while (EnumDisplaySettings(dxOutputDesc.DeviceName, modeIndex, &mode))
				{
					if (mode.dmPelsWidth > maxWidth)
					{
						maxWidth = mode.dmPelsWidth;
						maxHeight = mode.dmPelsHeight;
					}
					modeIndex++;
				}

				MONITORINFO mi = { sizeof(mi) };
				GetMonitorInfo(dxOutputDesc.Monitor, &mi);
				monitor.monitor = dxOutputDesc.Monitor;
				monitor.left = mi.rcMonitor.left;
				monitor.right = mi.rcMonitor.right;
				monitor.top = mi.rcMonitor.top;
				monitor.bottom = mi.rcMonitor.bottom;
				monitor.physWidth = maxWidth;
				monitor.physHeight = maxHeight;

				TinyComPtr<IDXGIOutput6> output6;
				if (SUCCEEDED(pdxOutput->QueryInterface(&output6)))
				{
					DXGI_OUTPUT_DESC1 desc1;
					if (SUCCEEDED(output6->GetDesc1(&desc1)) && (desc1.Monitor == dxOutputDesc.Monitor))
					{
						monitor.isHDR = desc1.ColorSpace == DXGI_COLOR_SPACE_RGB_FULL_G2084_NONE_P2020 ? TRUE : FALSE;
					}
				}

				monitors.push_back(monitor);
				pdxOutput->Release();
				nOutput++;
			}
			pdxAdapter->Release();
			nAdapter++;
		}
		pdxFactory->Release();
	}
	return monitors;
}

HMONITOR Controller::GetCurrentMonitor()
{
	return ::MonitorFromWindow(m_bottomWnd, MONITOR_DEFAULTTONULL);
}

UINT Controller::GetHAGS()
{
	HKEY  hKey;
	DWORD HwSchModeValue = 0;

	LONG lRes = RegOpenKeyExW(HKEY_LOCAL_MACHINE, LR"(SYSTEM\CurrentControlSet\Control\GraphicsDrivers)", 0, KEY_READ, &hKey);
	if (lRes == ERROR_SUCCESS)
	{
		DWORD dwBufferSize(sizeof(DWORD));
		DWORD nResult(0);
		LONG  nError = ::RegQueryValueExW(hKey,
			L"HwSchMode",
			0,
			NULL,
			reinterpret_cast<LPBYTE>(&nResult),
			&dwBufferSize);
		if (ERROR_SUCCESS == nError)
		{
			HwSchModeValue = nResult;
		}
	}
	return HwSchModeValue;
}

void Controller::StartRenderProfiler()
{
	g_sdkController->ReportTeaDataRenderProfiler();
}

void Controller::StartCollectPerformanceMatrics(const std::vector<PERFORMANCE_MATRICS>& params)
{
	g_sdkController->ReportTeaDataPerformanceMatrics(params);
}

void Controller::SetTTNtpMS(UINT64 ntpMs, UINT64 localMs)
{
	g_sdkController->SetTTNtpMS(ntpMs, localMs);
}

void Controller::GetAudioPerformance(const std::string& audioID, AUDIO_PERFORMANCE_INFO* info)
{
	g_sdkController->AudioSourceGetPerformance(audioID, info);
}

void Controller::OnKey(WPARAM wParam, LPARAM lParam)
{
	g_sdkController->OnKeyboardEvent(wParam, lParam);
}

void* Controller::GetMediaMgrImpl()
{
    return g_mediaMgr;
}

void Controller::StartAiIpc()
{
	AiSdkIPCMgr::GetInstance()->StartLoop();
}

void Controller::StopAiIpc()
{
	AiSdkIPCMgr::GetInstance()->StopLoop();
}

void Controller::SetupTimer(void* param)
{
	ITimerMgr* timeMgr = (ITimerMgr*)g_cief->QueryInterface(LSINAME_TIMERMGR);
	m_timer = timeMgr->SetTimer(0, 5000, (ITimerHandler*)this);
}

void Controller::CloseTimer()
{
    if (m_timer)
    {
        ITimerMgr* timeMgr = (ITimerMgr*)g_cief->QueryInterface(LSINAME_TIMERMGR);
        timeMgr->KillTimer(m_timer);
        m_timer = 0;
    }
}