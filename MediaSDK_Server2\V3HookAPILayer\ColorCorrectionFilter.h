#pragma once

#include <d3d11.h>
#include <stdint.h>
#include <memory>
#include <mutex>
#include <functional>
#include <atomic>

#include <BufferHelpers.h>
#include <SpriteBatch.h>

namespace v3
{

struct ColorCorrectionFilterParams
{
    float    brightness = 0.0f;
    float    contrast = 0.0f;
    float    hue_shift = 0.0f;
    float    saturation = 0.0f;
    uint32_t add_color = 0x0;
    uint32_t mul_color = 0xFFFFFF;
    float    opacity = 1.0f;
    float    gamma = 0.0f;
};

class ColorCorrectionFilter
{
public:
    ColorCorrectionFilter();
    ~ColorCorrectionFilter();

    bool Init(ID3D11Device* dev, ID3D11DeviceContext* ctx);
    bool IsReady();
    bool Render(ID3D11ShaderResourceView* src_srv);
    bool UpdateParams(const ColorCorrectionFilterParams& params, bool force = false);
    void Uninit();

private:
    void UninitNolock();
    void UpdateGPUParams();

private:
    std::mutex       mutex_;
    std::atomic_bool ready_ = false;
    std::atomic_bool need_update_params_ = false;

    ID3D11Device*        dev_ = nullptr;
    ID3D11DeviceContext* ctx_ = nullptr;

    ColorCorrectionFilterParams params_;
    bool                        params_uploaded_ = false;

#pragma pack(push)
#pragma pack(16)

    struct PSParam
    {
        DirectX::XMMATRIX color_matrix;
        float             gamma;
        float             opacity;
    };

#pragma pack(pop)

    std::unique_ptr<DirectX::SpriteBatch>             render_;
    std::unique_ptr<DirectX::ConstantBuffer<PSParam>> ps_cb_;
    std::function<void()>                             set_custom_shader_callback_;
    Microsoft::WRL::ComPtr<ID3D11PixelShader>         pixel_shader_;
};

} // namespace v3