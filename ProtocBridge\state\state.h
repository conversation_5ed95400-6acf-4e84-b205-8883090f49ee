#pragma once
#include "pb_export.h"
#include "pbmodule.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "PBBridge.h"
#include "ls_state.pb.h"
#include "ls_browser.pb.h"

namespace LS {
class State : public PB::Module {
private:
    virtual RequestList GetRequestHandlers() const override;
    virtual void ModuleCreate() override;

    static void ConvertTransform(ls_base::Transform& target_tranform, const TRANSFORM& source_transform);

public:
    
};
} // namespace MediaSDK