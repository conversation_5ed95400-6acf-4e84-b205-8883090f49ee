/**
 * All Right Reserved by bytedance 2021~2031
 * 
    // needs implement by user
    1. interface: XXLogCallback and callbacks_s

    // pipeline
    1. CastMateMirror_SetLogCallback (called first, if you need log)
    2. CastMateMirror_BindSdk (must be called before others)
    3. CastMateMirror_EnvCheck
    4. CastMateMirror_SetXXXX  (option)
    5. CastMateMirror_StartService
    6. CastMateMirror_PauseService (option)
    7. CastMateMirror_StopService
    8. CastMateMirror_UnBindSdk
 */

 #pragma once
 #include "bytecast_defines.h"
 #include <stdint.h>
 
 #if defined(Win32) || defined(_WIN32)
#ifdef BUILD_BYTEMATE_DLL
#define BYTE_API __declspec(dllexport)
#elif defined(BUILD_BYTEMATE_LIB)
#define BYTE_API
#else
#define BYTE_API __declspec(dllimport)
#endif
 #elif __APPLE__
 #include <TargetConditionals.h>
 #if TARGET_OS_MAC && !(TARGET_OS_IPHONE || TARGET_IPHONE_SIMULATOR)
 #define BYTE_API __attribute__((__visibility__("default"))) extern "C"
 #else
 #define BYTE_API __attribute__((__visibility__("default")))
 #endif
 #else
 #define BYTE_API __attribute__((__visibility__("default")))
 #endif
 
 #ifdef __cplusplus
 namespace bytecast 
 {
 #endif
 
 #define CASTMATE_KEY_LATENCY "latency"
 #define CASTMATE_KEY_ENABLE_RANDOM_PORT "enable_random_port"
 #define CASTMATE_KEY_AUDIO_SOURCE "set_audio_source"
 #define CASTMATE_KEY_ENABLE_SERVER "enable_server"
 #define CASTMATE_KEY_MIC_SCALE "mic_scale"
 #define CASTMATE_KEY_HOST_URL "host"
 #define CASTMATE_KEY_DEVICE_ID "did"
 #define CASTMATE_KEY_USER_ID "uid"
 #define CASTMATE_KEY_APP_ID "appid"
 #define CASTMATE_KEY_DESCRIPTION "description"
 #define CASTMATE_KEY_URI "uri"
 #define CASTMATE_KEY_FRAME_STUCK_DELAY "delay_ms"
 #define CASTMATE_KEY_FRAME_STUCK_INTERVAL "interval_ms"
 #define CASTMATE_KEY_PERFORMANCE_CALLBACK "performance_callback_ms"
 #define CASTMATE_KEY_ENABLE_NEW_MUXD "enable_new_muxd"
 #define CASTMATE_KEY_ENABLE_FAST_MIRROR "enable_fast_mirror"
 #define CASTMATE_KEY_SET_DECODE_MODE "mode"
 #define CASTMATE_KEY_PERFORMANCE_EVENT_PERIOD "period_ms"
 
 enum CastMateOptionType {
     /**
      * @brief set maximum latency that castmate mirror can cover
      * json value is as below:
      * @code {.cpp}
      * {
      *  "latency":100 //ms
      * }
      * @endcode
      */
     CASTMATE_OPTION_LATENCY = 0,
     /**
      * @brief set enable random port
      * json value is as below:
      * @code {.cpp}
      * {
      *  "enable_random_port":true/false
      * }
      * @endcode
      */
     CASTMATE_OPTION_ENABLE_RANDOM_PORT = 1,
     /** DEPRECATED!!!!
      * @brief set audio source
      * json value is as below:
      * set_audio_source : 0-mute，1-system audio，2-microphone，3-mixed audio]
      * mic_scale : mic volume percent in mixed mode xx%， range（0-100）
      * @code {.cpp}
      * {
      *  "set_audio_source":0/1/2/3,
      *  "mic_scale":[0-100]
      * }
      * @endcode
      */
     CASTMATE_OPTION_SET_AUDIO_SOURCE = 2, 
     /**
      * @brief set host url for eventtrack upload
      * json value is as below:
      * @code {.cpp}
      * {
      *  "host": "www.xxx.com"
      *  "did": "did"
      *  "uid": "uid"
      *  "appid": "appid"
      * }
      * @endcode
      */
     CASTMATE_OPTION_RESET_HOST = 3,
     /**
      * @brief set audio source
      * json value is as below:
      * enable_server : 0-all，1-wired_android，2-wired_ios]
      * @code {.cpp}
      * {
      *  "enable_server":0/1/2,
      * }
      * @endcode
      */
     CASTMATE_OPTION_ENABLE_SERVER = 4, 
     /**
      * @brief android set device info
      * json value is as below:
      * @code {.cpp}
      * {
      *  "description":"Download",
      *  "uri":"http://www.baidu.com",
      * }
      * @endcode
      */
     CASTMATE_OPTION_SET_DEVICE_INFO = 5,
     /**
      * @brief set video caton paramter
      * json value is as below:
      * @code {.cpp}
      * {
      *  "delay_ms":xxx,
      *  "interval_ms":xxx,
      * }
      * @endcode
      */
     CASTMATE_OPTION_SET_FRAME_STUCK = 6,
     /**
      * @brief set upload event interval
      * json value is as below:
      * @code {.cpp}
      * {
      *  "performance_callback_ms":xxx,
      * }
      * @endcode
      */
     CASTMATE_OPTION_SET_PERFORMANCE_CALLBACK = 7,
     /**
      * @brief set video frame output type
      * json value is as below:
      * @code {.cpp}
      * {
      *  "type": 0 - Encoded Data  1 - decoded data
      * }
      * @endcode
      */
     CASTMATE_OPTION_VIDEO_OUTPUT_TYPE = 8,
      /**
      * @brief set video frame output type
      * json value is as below:
      * @code {.cpp}
      * {
      *  "type": 0 - Encoded Data  1 - PCM
      * }
      * @endcode
      */
     CASTMATE_OPTION_AUDIO_OUTPUT_TYPE = 9,
     /**
      * @brief enable new muxd for wired_ios
      * json value is as below:
      * @code {.cpp}
      * {
      *  "enable_new_muxd":true/false
      * }
      * @endcode
      */
     CASTMATE_OPTION_ENABLE_NEW_MUXD = 10,
     /**
      * @brief enable new andriod wiredcast
      * json value is as below:
      * @code {.cpp}
      * {
      *  "enable_new_andriod_wiredcast":true/false
      * }
      * @endcode
      */
     CASTMATE_OPTION_ENABLE_NEW_ANDROID_WIREDCAST = 11,
     /**
      * @brief enable FastMirror for wireless_ios
      * json value is as below:
      * @code {.cpp}
      * {
      *  "enable_fast_mirror":true/false
      * }
      * @endcode
      */
     CASTMATE_OPTION_ENABLE_FAST_MIRROR = 12,
     
     /**
      * @brief enable decode software, hw-texture by default
      * json value is as below:
      * mode: 0-sw，1-hw-memory，2-hw-texture
      * @code {.cpp}
      * {
      *  "mode": x
      * }
      * @endcode
      */
     CASTMATE_OPTION_SET_DECODE_MODE = 13,

     /**
     * @brief set performance event upload interval
     * json value is as below:
     * @code {.cpp}
     * {
     *  "period_ms":xxx,
     * }
     * @endcode
     */
     CASTMATE_OPTION_SET_PERFORMANCE_EVENT = 14,

     /**
     * @brief Using for AB Test
     * json value is as below:
     * @code {.cpp}
     * {
     *  "enable_new_muxd":true/false
     *  "xxxx":xxx
     * }
     * @endcode
     */
    CASTMATE_OPTION_SET_AB_CONFIG = 15,
        /**
     * @brief Using for VideoOutput
     * json value is as below:
     * mode: 0-copy，1-ref
     * @code {.cpp}
     * {
     *  "mode":0/1
     * }
     * @endcode
     */
    CASTMATE_OPTION_SET_VOUT_MODE = 16,
        /**
     * @brief Using for VCamera
     * json value is as below:
     * camera_name: "ByteCast VirtualCameraX"
     * @code {.cpp}
     * {
     *  "camera_name":"ByteCast VirtualCamera1"
     * }
     * @endcode
     */
    CASTMATE_OPTION_SET_CAMERA_NAME = 17
 };
 
 enum CastMateProtocolType {
     /************************** choice 1: detail protocol support ************************ */
     TYPE_WIRELESS_ANDROID = 0,          // Android wireless mirror
     TYPE_WIRELESS_IOS = 1,              // iOS wireless mirror
     TYPE_WIRED_ANDROID = 2,             // Android wired mirror
     TYPE_WIRED_IOS = 3,                 // iOS wired mirror
     TYPE_WIRELESS_CAMERA = 4,           // bytelink wireless camera for Android & iOS
     TYPE_WIRED_ANDROID_CAMERA = 5,      // Android wired camera
     TYPE_WIRED_IOS_CAMERA = 6,          // iOS wired camera
     
     /************************** choice 2: compound protocol support ************************ */
     // for onPlugIn functionality 
     TYPE_WIRELESS_IOS_MIRROR = 10,        // iOS wireless mirror for airplay
     TYPE_WIRELESS_CAST       = 20,        // android wireless mirror&camera and ios wireless camera
     TYPE_WIRED_ANDROID_CAST  = 30,        // android wired mirror&camera
     TYPE_WIRED_IOS_CAST      = 40,        // iOS wired mirror&camera
 
     TYPE_NOT_REACHED = 7                // invalid
 };
 
 // angle value in Video_Process
 enum CastMateRotationType {
     //Rotation constant: 0 degree rotation (natural orientation)
     ROTATION_0 = 0,
     // Rotation constant: 90 degree rotation.
     ROTATION_90 = 1,
     // Rotation constant: 180 degree rotation.
     ROTATION_180 = 2,
     // Rotation constant: 270 degree rotation.
     ROTATION_270 = 3
 };
 
 // codec type
 enum CastMateCodecType {
     // video payloadtype
     CODEC_VIDEO_EXTRADATA_H264 = 0x0,  //SPS & PPS
     CODEC_VIDEO_EXTRADATA_H265 = 0x1,  // SPS & PPS & VPS
     CODEC_VIDEO_H264 = 0x2,
     CODEC_VIDEO_H265 = 0x3,
     CODEC_VIDEO_I420 = 0x4,
     CODEC_VIDEO_NV21 = 0x5,
     CODEC_VIDEO_NV12 = 0x6,
     CODEC_VIDEO_Texture2D = 0x7,
     CODEC_VIDEO_TextureOES = 0x8,
 
     // audio payloadtype
     CODEC_AUDIO_PCM     = 0x0100,
     CODEC_AUDIO_AAC     = 0x0200,
     CODEC_AUDIO_ALAC    = 0x0300,
     CODEC_AUDIO_AAC_ELD = 0x0400,
     CODEC_AUDIO_AAC_LC  = 0x0200,
     CODEC_AUDIO_INDEC   = 0x0800,
 
     CODEC_UNKNOWN       = 0xFFFF
 };
 
 enum CastMateExitReason {
     EXIT_REASON_DISCONNECT,  // source active disconnect
     EXIT_REASON_NETWORK_ERROR,  // source network error or be killed
     EXIT_REASON_KICK_OUT,  // active kickout source
     EXIT_REASON_FATAL_ERROR
 };
 
 enum CastMateAuthResult : uint8_t {
     eSuccess = 0,
     eErrorInvalidUsers,
     eTimeout,
     eOhters
 };
 
 /** 
  * @brief video frame format
  */
 enum CastMateVideoFrameType {
     /** 
      * @brief unknown
      */
     kVideoFrameTypeUnKnown,
     /** 
      * @brief raw type，according memory arrangement
      */
     kVideoFrameTypeRawMemory,
     /** 
      * @brief CVPixelBufferRef type，support iOS and macOS
      */
     kVideoFrameTypeCVPixelBuffer,
     /** 
      * @brief direct3d 11 data format
      */
     kVideoFrameTypeD3D11
 };
 
 enum CastMateVideoRotation {
     kVIDEO_ROTATION_0,
     kVIDEO_ROTATION_90,
     kVIDEO_ROTATION_180,
     kVIDEO_ROTATION_270
 };
 
 enum CastMateVideoColorSpace {
     kColorSpaceUnknown = 0,
     /**
     * @brief SMPTE 170M
     */
     kVideoColorSpaceYCbCrBT601 = 1,
     /**
     * @brief BT.709
     */
     kVideoColorSpaceYCbCrBT709 = 2,
     /**
     * @brief ITU-R BT.2020 NCL ( or BT.2020 CL or SMPTE ST 2085)
     */
     kVideoColorSpaceYCbCrBT2020 = 3,
     /**
     * @brief ITU-R BT.470 BG
     */
     kVideoColorSpaceBT470 = 4,
     /**
     * @brief FCC color space (SMPTE 170M is almost the same)
     */
     kVideoColorSpaceFCC = 5,
     /**
     * @brief SMPTE 240M
     */
     kVideoColorSpaceSMPTE240M = 6,  
     /**
     * @brief FILM
     */
     kVideoColorSpaceFILM = 7,
     /**
     * @brief YCGCO
     */
     kVideoColorSpaceYCGCO = 8,
     /**
     * @brief ITU-R BT.2100 HLG
     */
     kVideoColorSpaceBT2100 = 9,
     /**
     * @brief SMPTE ST 432-1
     */
     kVideoColorSpaceSMPTE432 = 10,
     /**
     * @brief Number of color spaces
     */
     kVideoColorSpaceSPC_NB = kVideoColorSpaceSMPTE432
 };
 
 enum CastMateVideoRange {
     kVideoRangeUnknown = 0,
     /**
     * @brief limited range[16-235]
     */
     kVideoRangeLimitedRange = 1,
     /**
     * @brief full range[0-255]
     */
     kVideoRangeFullRange = 2
 };
 
 enum CastMateVideoColorTransfer {
     kVideoColorTransferUnknown = 0,
     /**
     * @brief ITU-R BT.709 / ITU-R BT1361
     */
     kVideoColorTransferBT709 = 1,
     /**
     * @brief ITU-R BT.470 M / ITU-R BT1700 625 PAL & SECAM / AVCOL_TRC_GAMMA22
     */
     kVideoColorTransferBT470M = 4,
      /**
     * @brief ITU-R BT.470 BG / AVCOL_TRC_GAMMA28
     */
     kVideoColorTransferBT470BG = 5,
     /**
     * @brief SMPTE 170M / ITU-R BT601-6 525 or 625 / ITU-R BT1358 525 or 625 / ITU-R BT1700 NTSC
     */
     kVideoColorTransferSMPTE170M = 6,
     /**
     * @brief SMPTE 240M
     */
     kVideoColorTransferSMPTE240M = 7,
     /**
     * @brief Linear
     */
     kVideoColorTransferLinear = 8,
     /**
     * @brief logarithmic (100:1 range)
     */ 
     kVideoColorTransferLogarithmic = 9,
     /**
     * @brief logarithmic (100 * sqrt(10):1)
     */
     kVideoColorTransferLogSQRT = 10,
     /**
     * @brief IEC 61966-2-4
     */
     kVideoColorTransferIEC61966_2_4 = 11,
     /**
      * @brief ITU-R BT1361 Extended Colour Gamut
      */
     kVideoColorTransferBT1361_ECG = 12,
     /**
      * @brief IEC 61966-2-1 (sRGB or sYCC)
      */
     kVideoColorTransferIEC61966_2_1 = 13,
     /**
     * @brief ITU-R BT2020 for 10-bit system
     */
     kVideoColorTransferBT2020_10 = 14,
     /**
     * @brief ITU-R BT2020 for 12-bit system
     */
     kVideoColorTransferBT2020_12 = 15,
     /**
     * @brief SMPTE ST 2084（PQ）/ SMPTE ST 2084 for 10-, 12-, 14- and 16-bit systems
     */
     kVideoColorTransferSMPTE2084 = 16,
    /**
     * @brief SMPTE ST 428-1
     */
     kVideoColorTransferSMPTE428     = 17, 
    /**
     * @brief ARIB STD-B67, known as "Hybrid log-gamma"
     */
     kVideoColorTransferARIB_STD_B67 = 18

 };

 enum CastMateVideoColorPrimaries {
     kVideoColorPrimariesUnknown = 0,
     /**
     * @brief ITU-R BT.709
     */
     kVideoColorPrimariesBT709 = 1,
     /**
     * @brief ITU-R BT.470 M
     */ 
     kVideoColorPrimariesBT470M = 2,
     /**
     * @brief ITU-R BT.470 BG
     */
     kVideoColorPrimariesBT470BG = 3,
     /**
     * @brief SMPTE 170M
     */
     kVideoColorPrimariesSMPTE170M = 4,
     /**
     * @brief SMPTE 240M
     */
     kVideoColorPrimariesSMPTE240M = 5,
     /**
     * @brief FILM
     */
     kVideoColorPrimariesFILM = 6,
     /**
     * @brief BT.2020
     */
     kVideoColorPrimariesBT2020 = 7,
     /**
     * @brief SMPTE ST 428-1
     */
     kVideoColorPrimariesSMPTEST428_1 = 8,
     /**
     * @brief SMPTE ST 431-2
     */
     kVideoColorPrimariesSMPTEST431_2 = 9,
     /**
     * @brief SMPTE ST 432-1
     */
     kVideoColorPrimariesSMPTEST432_1 = 10,
     /**
     * @brief ITU-R BT.2100 HLG
     */
     kVideoColorPrimariesBT2100 = 11
 };

 #define VideoFrameMaxPlanes 4
 #define VideoTransMatrixCol 4
 #define VideoTransMatrixRow 4
 
 /** 
  * @brief video frame
  */
 struct CastMateVideoFrame {
     /** 
      * @brief video frame type，see VideoFrameType{@link #VideoFrameType}
      */
     CastMateVideoFrameType mFrameType;
     /** 
      * @brief video frame format，see CodecType{@link #CodecType}
      */
     CastMateCodecType mPixelFmt;
     /** 
      * @brief rotaion，see VideoRotation{@link #VideoRotation}
      */
     CastMateVideoRotation mRotation;
     /** 
      * @brief ColorSpace，see ColorSpace{@link #ColorSpace}
      */
     CastMateVideoColorSpace mColorSpace;
     /** 
      * @brief ColorRange，see VideoRange{@link #VideoRange}
      */
     CastMateVideoRange mVideoRange;
     /**
      * @brief ColorTransfer，see ColorTransfer{@link #ColorTransfer}
      */
     CastMateVideoColorTransfer mColorTransfer;
     /**
      * @brief ColorPrimaries，see ColorPrimaries{@link #ColorPrimaries}
      */
     CastMateVideoColorPrimaries mColorPrimaries;
     /**
      * @brief timestamp，unit：microseconds
      */
     int64_t mTimestampUs;
     /**
     * @brief decode cost
     */
     uint32_t decode_ms;
     /**
     * @brief sequence number
     */
     uint32_t mIndex;
     /** 
      * @brief width，unit：px
      */
     int mWidth;
     /** 
      * @brief height，unit：px
      */
     int mHeight;
 
     /** 
      * @brief color plane number
      * @notes yuv
      */
     int mNumberOfPlanes;
     /** 
      * @brief plane data pointer
      * @param [in] plane_index plane data index
      */
     uint8_t* mPlaneData[VideoFrameMaxPlanes];
     /** 
      * @brief plane stride
      * @param [in] plane_index plane data index
      */
     int mPlaneStride[VideoFrameMaxPlanes];
 
     /**
      * @brief texture id
      */
     uint32_t mTextureId;
 
     /**
      * @brief texture
      * 
      */
     void* mTexture;
 
     /**
      * @brief texture matrix
      */
     float mTextureMatrix[VideoTransMatrixCol * VideoTransMatrixRow];
 
     /** 
      * @brief extra data
      */
     uint8_t* mExtraDataInfo;  // NOLINT
     /** 
      * @brief  supplement data
      */
     int mExtraDataSize;

     void* priv;

     void (*AddRef)(void* priv);

     void (*ReleaseRef)(void* priv);
 };
 
 typedef struct {
     uint32_t capture_ms;
     uint32_t encode_ms;
     uint32_t transfer_ms;
     uint32_t jitterbuf_ms;
     uint64_t decode_ms;
     uint32_t render_ms;
     bool inner_decode;
 } frame_stat_info_t;
 
 struct callbacks_s {
    void* cls;
 
    void(*Auth_Handler)(void* cls, CastMateAuthResult result);
 
    // when open or resume the mirror, it will be called
    void(*Audio_Open)(void* handle, void* cls, int bits, int channels, int samplerate,
                 int samples, CastMateCodecType type);
    void(*Audio_Process)(void* handle, void* cls, const void* buffer, int buflen, double timestamp, uint32_t seqnum, frame_stat_info_t frame_stat);
    // when close or pause the mirror, it will be called
    void(*Audio_Close)(void* handle, void* cls);
    // when open the mirror, it will be called, it contains pps/sps info, payloadtype indicate h264/h265
    void(*Video_Open)(void* handle, void* cls, const void* buffer, int buffersize, CastMateCodecType type, double timestamp);
    // it usually contains frame data, while it contains pps/sps while  payloadtype=VIDEO_CODEC_PSPS, triggered by rotation happens
    void(*Video_Process)(void* handle, void* cls, const void* buffer, int buffersize, CastMateCodecType type, int angle, double timestamp, frame_stat_info_t frame_stat);
    // when close the mirror, it will be called
    void(*Video_Close)(void* handle, void* cls);
 
    void(*OnConnect)(void* handle, void* cls, const char* userId, const char* name);
 
    void(*OnDisconnect)(void* handle, void* cls, const char* userId, int code, const char* msg);
 
    void(*OnError)(void* handle, void* cls, int32_t error_num);
 
    void(*OnEvent)(void* handle, void* cls, int32_t event_code, const char* event_msg);
 
    void(*OnMirrorPort)(void* handle, void* cls, CastMateProtocolType type, uint16_t port, const char* all_local_ip);

    // Recvd metadata from peer
    void(*OnRecvMetaData)(void* handle, void* cls, const char* userId, const char* msg);

    /**
     * @brief decoded video data 
     * 
     * @param[in] handle service handle
     * @param[in] cls  service private data
     * @param[in] frame @see @class CastMateVideoFrame
     */
    void(*Video_Output)(void* handle, void* cls, const struct CastMateVideoFrame *frame);

    void (*OnLibraExposure)(void* handle, void* cls, const char* event_key);

    void (*OnReportTea)(void* handle, void* cls, const char* event_key, const char* json_str);
    
    /**
     * @brief device plugin
     * @note wired cast
     * 
     * @param[in] handle service handle
     * @param[in] cls  service private data
     * @param[in] info @see @class source_device_info_t
     */
    void(*OnPlugIn)(void* handle, void* cls, const source_device_info_t* info);
    /**
     * @brief device plugin
     * @note wired cast
     * 
     * @param[in] handle service handle
     * @param[in] cls  service private data
     * @param[in] deviceKey 
     */
    void(*OnPlugOut)(void* handle, void* cls, const char* deviceKey);
    /**
     * @brief device preempt
     * @note  useless right now
     * 
     * @param[in] handle service handle
     * @param[in] cls  service private data
     * @param[in] info @see @class source_device_info_t
     */
    void(*OnPreempt)(void* handle, void* cls, const source_device_info_t* info);

    // multi-session when open or resume the mirror, it will be called
    void(*Audio_Open_Multi)(void* handle, void* cls, const char* userId, int bits, int channels, int samplerate, int samples, CastMateCodecType type);
    // multi-session decoded audio data 
    void(*Audio_Process_Multi)(void* handle, void* cls, const char* userId, const void* buffer, int buflen, double timestamp, uint32_t seqnum, frame_stat_info_t frame_stat);
    // multi-session when close or pause the mirror, it will be called
    void(*Audio_Close_Multi)(void* handle, void* cls, const char* userId);
    // multi-session when open the mirror, it will be called, it contains pps/sps info, payloadtype indicate h264/h265
    void(*Video_Open_Multi)(void* handle, void* cls, const char* userId, const void* buffer, int buffersize, CastMateCodecType type, double timestamp);
    // multi-session decoded video data 
    void(*Video_Output_Multi)(void* handle, void* cls, const char* userId, const struct CastMateVideoFrame *frame);
    // multi-session  when close the mirror, it will be called
    void(*Video_Close_Multi)(void* handle, void* cls, const char* userId);
    // multi-session  when error happens
    void(*OnError_Multi)(void* handle, void* cls, const char* userId, int32_t error_num);
    // multi-session  when some events occur
    void(*OnEvent_Multi)(void* handle, void* cls, const char* userId, int32_t event_code, const char* event_msg);
 };
 
 struct IQRListener {
     void (*OnQRReady)(const char *pCode, int len);
 };
 
 /* -------------------------------------------------------------------------------------------- */
 
 // LogLevel
 typedef void (* XXLogCallback)(int level, const void* priv, const char* log);
 
 #ifdef __cplusplus
 extern "C"
 {
 #endif
     /**
      * @brief logcallback 
      * it should be called first, when you want all the logs.
      * 
      * @param callback  callback function
      * @param priv  private data
      */    
     BYTE_API void CastMateMirror_SetLogCallback(XXLogCallback callback, const void *priv);
 
     /**
      * @brief bind  (is not thread safe)
      * call it before any cast service releated API
      * 
      * @param appId  application identifier, can be null
      * @param appSecret  application secret, can be null
      * @param deviceId  device identifier, should not be null
      * @param userId   user identifier, should not be null
      * @param cb       callback function
      * @retval bool 
      */    
     BYTE_API bool CastMateMirror_BindSdk(const char* appId, const char* appSecret, const char* deviceId, const char* userId, callbacks_s* cb);
 
     /**
      * @brief unbind (is not thread safe)
      * when the sdk is not used any more, it should be called paired to CastMateMirror_BindSdk
      */    
     BYTE_API void CastMateMirror_UnBindSdk();
 
     /**
      * @brief cast service running environment check
      * must be called after CastMateMirror_BindSdk 
      * call it before any cast service releated API
      * 
      * @param item   @see @enum ENV_ITEM
      * @param timeOutMs  API timeout in milliseconds, no useful now
      * @retval int
      */    
     BYTE_API int CastMateMirror_EnvCheck(ENV_ITEM item, int timeOutMs);
 
     /**
      * @brief cast service running environment repair
      * must be called after CastMateMirror_BindSdk 
      * call it before any cast service releated API
      * 
      * @param item  @see @enum ENV_ITEM
      * @param timeOutMs  API timeout in milliseconds, no useful now
      * @retval int
      */    
     BYTE_API int CastMateMirror_EnvRepair(ENV_ITEM item, int timeOutMs);
 
     /**
      * @brief some setting before cast service running
      * before CastMateMirror_StartService, then the options will take effect
      * 
      * @param option  @see @enum CastMateOptionType
      * @param json  API timeout in milliseconds, no useful now
      */
     BYTE_API void CastMateMirror_SetOption(CastMateOptionType option, const char* json);
 
     /**
      * @brief start service
      * 
      * @param type  @see @enum CastMateProtocolType
      * @param name  service name which can be discovered by mdns search
      * @param port  serve port
      * @param streamParams etc "{\"width\":1920,\"height\":1080,\"fps\":60,\"bitrate\":10240,\"micScale\":0,\"audioSource\":1,\"landscape\":0,\"serverMode\":1}";
      * @param cb    callback function
      * @retval void* service handle
      */
     BYTE_API void* CastMateMirror_StartService_New(CastMateProtocolType type, const char* name, uint16_t port, const char* streamParams, const callbacks_s* cb);
 
     /**
      * @brief start service
      * 
      * @param[in] type  @see @class CastMateProtocolType
      * @param[in] name  service name which can be discovered by mdns search
      * @param[in] port  serve port
      * @param[in] fps   serve fps
      * @param[in] width serve width
      * @param[in] height serve height
      * @param[in] payloadType  @see @enum CastMateCodecType
      * @param[in] cb    callback function
      * @retval void* service handle
      */
     BYTE_API void* CastMateMirror_StartService(CastMateProtocolType type, const char* name, uint16_t port, int fps,
                                 int width, int height, int bitrate, int payloadType, const callbacks_s* cb);
     
     /**
      * @brief set qrcode listener
      * 
      * @param[in] listener  @see @class IQRListener
      */
     BYTE_API void CastMateMirror_SetQRListener(IQRListener* listener);
     
     /**
      * @brief set bitrate
      * 
      * @param[in]  handle  service handle
      * @param[in]  bitrate unit:kbps
      * @param[in]  clientId
      * @retval bool
      */
     BYTE_API bool CastMateMirror_SetBitrate(void* handle, int bitrate, const char* clientId = nullptr);
 
     /**
      * @brief set bitrate
      * 
      * @param[in]  handle  service handle
      * @param[in]  fps
      * @param[in]  clientId
      * @retval bool
      */
     BYTE_API bool CastMateMirror_SetFPS(void* handle, int fps, const char* clientId = nullptr);
 
     /**
      * @brief request idr
      * 
      * @param[in]  handle  service handle
      * @param[in]  clientId
      * @retval bool
      */
     BYTE_API bool CastMateMirror_RequestIDR(void* handle, const char* clientId = nullptr);
 
     /**
      * @brief set resolution
      * 
      * @param[in]  handle  service handle
      * @param[in]  cx  width
      * @param[in]  cy  height
      * @param[in]  clientId
      * @retval bool
      */
     BYTE_API bool CastMateMirror_SetResolution(void* handle, int cx,int cy, const char* clientId = nullptr);
 
     /**
      * @brief pause service
      * 
      * @param[in]  handle  service handle
      * @param[in]  pause
      * @retval bool
      */
     BYTE_API bool CastMateMirror_PauseService(void* handle, bool pause);
 
     /**
      * @brief stop service
      * 
      * @param[in]  handle  service handle
      * @retval bool
      */
     BYTE_API bool CastMateMirror_StopService(void* handle);
 
     /**
      * @brief retrieve castmate verion
      *
      * @retval char*
      */
     BYTE_API const char* CastMateMirror_GetVersion(void);
 
     /**
      * @brief retrieve local machine ip
      * 
      * @retval char*
      */
     BYTE_API const char* CastMateMirror_GetDeviceIp(void);
 
     /**
      * @brief pause service
      * 
      * @param[in]  handle  service handle
      * @param[in]  mode:  0-dynamic adjustment auto  1-quality fix 
      * @param[in]  clientId
      * @retval bool
      */
     BYTE_API bool CastMateMirror_SetJitterMode(void* handle, int mode, const char* clientId = nullptr);
 
     /**
      * @brief set invite listener
      * 
      * @param[in] listener  @see @class IInviteListener
      */
     BYTE_API void CastMateMirror_SetInviteListener(IInviteListener* listener);
 
     /**
      * @brief start browse
      * 
      * @param[in] type  @see @enum CastMateProtocolType
      * @retval bool
      */
     BYTE_API bool CastMateMirror_StartBrowse(CastMateProtocolType type = TYPE_NOT_REACHED);
     
     /**
      * @brief stop browse
      * 
      * @param[in] type  @see @enum CastMateProtocolType
      * @retval bool
      */
     BYTE_API bool CastMateMirror_StopBrowse(CastMateProtocolType type = TYPE_NOT_REACHED);
 
     /**
      * @brief invite cast
      * 
      * @param[in]  handle  service handle
      * @param[in]  deviceKey @note devicekey which can be by onPlugIn and onBrowse
      * @param[in]  msg  @note private message
      * @param[in]  mode @note 0: mirror mode; 1: camera mode
      * @retval bool
      */
     BYTE_API bool  CastMateMirror_InviteMirror(void* handle, const char* deviceKey, const char* msg, int mode = 1);
 
     /**
      * @brief stop cast
      * 
      * @param[in]  handle  service handle
      * @param[in]  deviceKey @note devicekey which can be by onPlugIn and onBrowse
      * @retval bool
      */
     BYTE_API bool CastMateMirror_StopMirror(void* handle, const char* deviceKey);
 
     /**
      * @brief disconnect cast
      * @note 
      * 
      * @param[in]  handle  service handle
      * @param[in]  deviceKey @note devicekey which can be by onPlugIn and onBrowse
      * @retval bool
      */
     BYTE_API bool CastMateMirror_DisConnect(void* handle, const char* deviceKey);
 
     /**
      * @brief is android phone device
      * 
      * @param vid  device vendor identifier
      * @param pid  device product identifier
      * @retval bool
      */
     BYTE_API bool CastMateMirror_IsAndroidPhone(int vid, int pid);
 
     /**
      * @brief retrieve protocol type
      * @param[in] handle  service handle
      *
      * @retval @see @enum CastMateProtocolType
      */
     BYTE_API CastMateProtocolType CastMateMirror_GetProtocolType(void* handle);
 
     // video frame decode infos
     BYTE_API void CastMateMirror_VideoFrameDecodeIn(void* handle, uint64_t frameIndex);
     BYTE_API void CastMateMirror_VideoFrameDecodeOut(void* handle, uint64_t frameIndex);
     BYTE_API void CastMateMirror_VideoFrameDecodeRendered(void* handle, uint64_t frameIndex);

    // Send metadata to peer
    BYTE_API bool CastMateMirror_SendMetaData(void* handle, const char* deviceKey, const char* msg);

     /* -------------------------------------------------------------------------------------------- */
 #ifdef __cplusplus
 }; // end of extern "C"
 #endif
 
 #ifdef __cplusplus
 } // namespace bytecast
 #endif