﻿#pragma once

#include <string>
#include <vector>
#include <gdiplus.h>
#include "LSPublicHeader.h"
#include "systemutil.h"
#include "Mode.h"
#include "Preview.h"

enum ENUMOBJECT
{
	OBJECT_SCENE = 0,
    OBJECT_PREVIEW,
	OBJECT_CANVAS,
	OBJECT_LAYER,
	OBJECT_SOURCE,
	OBJECT_MAX
};

class ModeSceneMgr
{
public:
	static ModeSceneMgr* GetInstance();
	ModeSceneMgr();
	~ModeSceneMgr();

	UINT64 CreateScene(SCENE_INFO* sceneInfo, const UINT64* sceneID, const LIVE_MODE* mode);
	void   BindModeScene(LIVE_MODE mode, UINT64 sceneID);
	void   DestroyScene(UINT64 sceneID);
	
	UINT32 AddPreview(PREVIEW_INFO* info, const UINT32* id);
    void   RemovePreview(UINT32 previewID);
    void   DestroyPreview(UINT32 previewID);
    void   GetPreviewInfoByID(UINT32 previewID, PREVIEW_INFO* info);

	UINT64 AddCanvas(CANVAS_INFO* info, const UINT64* id);
	bool   DestroyCanvas(UINT64 canvasID);
    bool   BindPreviewCanvas(UINT32 previewID, UINT64 canvasID);
    void   GetCanvasInfoByID(UINT64 canvasID, CANVAS_INFO_EX* infoEx);

	UINT64 AddLayer(LAYER_INFO* layerInfo, const UINT64* layerID);
	bool   BindCanvasLayer(UINT64 canvasID, UINT64 layerID);
	void   DestroyLayer(UINT64 layerID);
    void   HandleLayerSizeChange(UINT64 layerID, float width, float height);

	bool   SelectMode(LIVE_MODE mode);
	Mode*  GetCurrentMode();
	Scene* GetCurrentScene();
	bool   IsCurrentScene(UINT64 sceneID);

	UINT64 AllocSceneID();
	UINT64 AllocCanvasID();
	UINT64 AllocLayerID();
	UINT64 AllocPreviewID();
    UINT64 AllocFrameID();

	void BindObject(UINT64 id, void* object, ENUMOBJECT bo);
	void UnBindObject(UINT64 id, ENUMOBJECT bo);

	Mode*    GetModeByID(UINT64 id);
	Scene*   GetSceneByID(UINT64 id);
    Preview* GetPreviewByID(UINT64 id);
	Canvas*	 GetCanvasByID(UINT64 id);
	Layer*	 GetLayerByID(UINT64 id);
	void	 GetLayerInfoByID(UINT64 id, LAYER_INFO* info);
	bool	 CheckLayerExist(UINT64 id);

	Preview* GetPreviewByVideoModel(UINT32 videoModel, UINT32* previewID = nullptr);
    UINT32   GetVideoModelByPreviewID(UINT64 previewID);
    UINT32   GetPreviewIDByCanvasID(UINT64 canvasID);
    UINT64   GetCurCanvasIDByPreviewID(UINT32 previewID);
    UINT32   GetVideoModelByOldCanvasIdx(UINT32 canvasIdx, LIVE_MODE mode = LIVE_MODE_MAX);
    UINT32   GetOldCanvasIdxByModel(UINT32 videoModel);
    UINT32   GetOldCanvasIdxByPreviewID(UINT32 previewID);

	void OnBrowserSourceUpdate(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo);

protected:
	Mode* m_modeScene[LIVE_MODE_MAX];
	LIVE_MODE m_curMode = LIVE_MODE_MAX;
	MODE_INFO_EX m_modeInfos[LIVE_MODE_MAX];

	UINT m_sceneCounter = Util::GetMicrosecondTimestamp();
	UINT m_canvasCounter = Util::GetMicrosecondTimestamp();
	UINT m_layerCounter = Util::GetMicrosecondTimestamp();
	UINT m_sourceCounter = Util::GetMicrosecondTimestamp();
	UINT m_previewCounter = Util::GetMicrosecondTimestamp();
    UINT m_frameCounter = Util::GetMicrosecondTimestamp();

	std::map<UINT64, void*> m_bindObjects[OBJECT_MAX];
};