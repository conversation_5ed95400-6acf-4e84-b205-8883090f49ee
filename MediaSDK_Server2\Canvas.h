﻿#pragma once

#include <vector>
#include "Layer.h"

enum CANVAS_MODE
{
	CANVAS_MODE_LANSSCAPE,
	CANVAS_MODE_PORTRAIT,
};

class Canvas
{
public:
	Canvas();
	~Canvas();

	bool   Create();
	bool   Destroy();

	void   SetCanvasInfo(const CANVAS_INFO* info);
	void   GetCanvasInfo(CANVAS_INFO_EX* info, bool child = false);
    void   UpdateCanvasLayerLayout(const CANVAS_INFO& canvasInfo);
	void   SetSceneID(UINT64 id);
    void   SetPreviewID(UINT32 id);

	UINT64 CreateLayerWithInfo(LAYER_INFO* layerInfo, bool calcLayout, const UINT64* id);
	bool   CreateLayer(Layer* pLayer, bool calcLayout);
	void   DestroyLayer(Layer* layer);

	void   Select(bool in);
	void   SyncLayerSource(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo);
	void   PreLoadCanvas(bool in);

	void   ReLoadCanvas(bool in);
	void   RePreLoadCanvas(bool in, bool isReload = false);

	void   GetLayersOrder(std::vector<UINT64>* order);
	void   UpdateWholeLayersOrder(std::vector<UINT64> layerIDs, std::vector<UINT64>* moreIDs, std::vector<UINT64>* lessIDs);
	void   MoveLayerOrder(UINT64 layerID, MOVE_ORDER move);

	void   BindFilter(FILTER* info);
    void   UnBindFilter(FILTER* info);
    void   AddFilter(FILTER* info);
    void   RemoveFilter(FILTER* info);

	void   SetSyncMedia(bool sync);

protected:
	CANVAS_INFO m_canvasInfo;
	std::vector<UINT64> m_order;
	std::vector<Layer*> m_layers;
	bool m_syncMedia = false;
};