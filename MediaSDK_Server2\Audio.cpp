﻿#include "Audio.h"
#include "MediaMgr.h"
#include "FilterMgr.h"
#include "Layer.h"
#include "ModeSceneMgr.h"
#include "stringutil.h"
#include "MediaSDKControllerV2Impl.h"

extern media_mgr::MediaMgr* g_mediaMgr;
extern v3::AudioFrameProcessor* g_audioFrameProcessor;

Audio::Audio(){}

Audio::Audio(AUDIO_INFO audioInfo)
{
    m_audioInfo = audioInfo;
}

Audio::~Audio(){}

bool Audio::SyncToMedia(bool add)
{
    bool success = false;
    if (add)
    {
        success = g_mediaMgr->AddAudio(m_audioInfo);
    }
    else
    {
        success = g_mediaMgr->RemoveAudio(m_audioInfo.id);
    }

    return success;
}

bool Audio::ControlAudio(AUDIO_CONTROL_INFO* info)
{
    SetAudioInfo(&info->audioInfo);
    if (info->audioInfo.type == AUDIO_VIS)
    {
        Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(info->audioInfo.id);
        if (pLayer)
            pLayer->UpdateVISAudioInfo(&info->audioInfo);
    }

    bool success = true;
    if (m_audioInfo.isCreated)
        success = g_mediaMgr->ControlAudio(*info);

    return success;
}

void Audio::SetAudioInfo(const AUDIO_INFO* info)
{
    m_audioInfo = *info;
}

void Audio::GetAudioInfo(AUDIO_INFO* info, AUDIO_INFO_CMD cmd)
{
    *info = m_audioInfo;
    if (cmd != AUDIO_INFO_NONE)
    {
        std::string audioID = "";
        Util::NumToString(info->id, &audioID);
        g_mediaMgr->GetAudioInfo(audioID, info, cmd);
    }
}

void Audio::BindFilter(FILTER* info)
{
    AddFilter(info);

    if (m_audioInfo.isCreated)
    {
        if (!g_mediaMgr->CreateFilter(m_audioInfo.id, *info))
        {
            LOG(ERROR) << "[Audio::AddFilter] AddFilter failed, filterID: " << info->id << " filterType: " << info->type;
            info->isCreated = false;
            return;
        }

        info->isCreated = true;
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
}

void Audio::UnBindFilter(FILTER* info)
{
    RemoveFilter(info);
    if (info->isCreated)
    {
        if (g_mediaMgr->RemoveFilter(m_audioInfo.id, *info))
            LOG(ERROR) << "[Audio::UnbindFilter] RemoveFilter failed, audioID: " << m_audioInfo.id << ", filterID: " << info->id << ", filterType: " << info->type;
    }
}

void Audio::AddFilter(FILTER* info)
{
    auto st = std::find(info->mediaIDs.begin(), info->mediaIDs.end(), m_audioInfo.id);
    if (st == info->mediaIDs.end())
    {
        info->mediaIDs.push_back(m_audioInfo.id);
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
    else
    {
        LOG(ERROR) << "[Audio::AddFilter] same filter add to same audio";
        return;
    }

    auto it = std::find_if(m_audioInfo.filters.begin(), m_audioInfo.filters.end(), [info](FILTER filterInfo) {
        if (info)
            return info->id == filterInfo.id;
        return false;
    });
    if (it == m_audioInfo.filters.end())
    {
        m_audioInfo.filters.push_back(*info);
    }
    else
    {
        LOG(ERROR) << "[Audio::AddFilter] same filter add to same audio";
        return;
    }
}

void Audio::RemoveFilter(FILTER* info)
{
    auto it = std::find_if(m_audioInfo.filters.begin(), m_audioInfo.filters.end(), [info](const FILTER& filterInfo) {
        if (info)
            return info->id == filterInfo.id;
        return false;
    });
    if (it != m_audioInfo.filters.end())
    {
        m_audioInfo.filters.erase(it);
    }
    else
    {
        LOG(ERROR) << "[Audio::RemoveFilter] audio id: " << m_audioInfo.id << " have not filter id: " << info ? info->id : 0;
        return;
    }

    auto st = std::find(info->mediaIDs.begin(), info->mediaIDs.end(), m_audioInfo.id);
    if (info && (st != info->mediaIDs.end()))
    {
        info->mediaIDs.erase(st);
        FilterMgr::GetInstance()->SetFilterInfo(info);
    }
    else
    {
        LOG(ERROR) << "[Audio::RemoveFilter] filter id: " << (info ? info->id : 0) << " do not belong to any audio";
        return;
    }
}

void Audio::UpdateVISAudio(VISUAL_TYPE sourceType)
{
	m_audioInfo.isCreated = true;

	// 为场景源音频添加音频滤镜
	for (auto& filterInfo : m_audioInfo.filters)
	{
		bool ret = g_mediaMgr->CreateFilter(m_audioInfo.id, filterInfo);
		if (!ret)
		{
			LOG(ERROR) << "[Audio::UpdateVISAudio] audio AddFilter failed, audioID: " << m_audioInfo.id << ", filterID: " << filterInfo.id;
			filterInfo.isCreated = false;
		}
		filterInfo.isCreated = true;

		// 更新音频滤镜信息
		if (Filter* pFilter = FilterMgr::GetInstance()->GetFilterByID(filterInfo.id))
			pFilter->SetFilterInfo(&filterInfo);
	}

	// 更新场景源音频真实属性
	AUDIO_CONTROL_INFO controlInfo{};
	controlInfo.audioInfo = m_audioInfo;
	controlInfo.cmd = (AUDIO_CONTROL_CMD)(AUDIO_CONTROL_SET_ALL_SETTING | AUDIO_CONTROL_SET_AUDIO_TRACK);

	if (sourceType == VISUAL_TYPE::VISUAL_RTC)
		controlInfo.cmd = AUDIO_CONTROL_CMD(AUDIO_CONTROL_SET_MUTE | AUDIO_CONTROL_SET_VOLUME | AUDIO_CONTROL_SET_AUDIO_TRACK);

	ControlAudio(&controlInfo);
}

CaptureAudio::CaptureAudio() {}

CaptureAudio::CaptureAudio(CAPTURE_AUDIO_INFO captureAudioInfo)
{
    m_captureAudioInfo = captureAudioInfo;
}

CaptureAudio::~CaptureAudio() {}

bool CaptureAudio::SyncToAudioFrameProcessor(bool add)
{
	std::string capture_audio_id = "";
	Util::NumToString(m_captureAudioInfo.id, &capture_audio_id);

    if (add)
    {
        if (!g_audioFrameProcessor->CreateCaptureAudioInput(m_captureAudioInfo.audioTrack, capture_audio_id, m_captureAudioInfo.bufferTimeMs))
		{
			LOG(ERROR) << "[CaptureAudio::SyncToAudioFrameProcessor] CreateCaptureAudioInput failed, audioTrack: " << m_captureAudioInfo.audioTrack << ", captureAudioID: " << capture_audio_id << ", bufferTimeMs: " << m_captureAudioInfo.bufferTimeMs;
			return false;
		}

		if (!SetOriginAudio(m_captureAudioInfo.originAudioID))
		{
			LOG(ERROR) << "[CaptureAudio::SyncToAudioFrameProcessor] SetOriginAudio failed, captureAudioID: " << capture_audio_id << ", originAudioID: " << m_captureAudioInfo.originAudioID;
			return false;
		}

		if (!g_audioFrameProcessor->CaptureAudioInputSetReplaceAudio(capture_audio_id, m_captureAudioInfo.replacePath))
		{
			LOG(ERROR) << "[CaptureAudio::SyncToAudioFrameProcessor] CaptureAudioInputSetReplaceAudio failed, captureAudioID: " << capture_audio_id << ", replacePCMPath: " << m_captureAudioInfo.replacePath;
			return false;
		}

        return true;
    }
    else
    {
		if (!g_audioFrameProcessor->DestroyCaptureAudioInput(capture_audio_id))
		{
            LOG(ERROR) << "[CaptureAudio::SyncToAudioFrameProcessor] DestroyCaptureAudioInput failed, captureAudioID: " << capture_audio_id;
            return false;
		}

        return true;
    }
}

bool CaptureAudio::ControlCaptureAudio(AUDIO_CONTROL_INFO* info)
{
	SetCaptureAudioInfo(&info->captureAudioInfo);
	if (m_captureAudioInfo.isCreated)
		return g_mediaMgr->ControlAudio(*info, true);

	return false;
}

void CaptureAudio::SetCaptureAudioInfo(const CAPTURE_AUDIO_INFO* info)
{
    m_captureAudioInfo = *info;
}

void CaptureAudio::GetCaptureAudioInfo(CAPTURE_AUDIO_INFO* info)
{
    *info = m_captureAudioInfo;

    std::string capture_audio_id = "";
    Util::NumToString(m_captureAudioInfo.id, &capture_audio_id);
    std::string origin_audio_id = "";
    g_audioFrameProcessor->CaptureAudioInputGetSource(capture_audio_id, &origin_audio_id);
    Util::StringToNum(origin_audio_id, &info->originAudioID);
}

bool CaptureAudio::SetOriginAudio(UINT64 originAudioID)
{
    m_captureAudioInfo.originAudioID = originAudioID;

	std::string capture_audio_id = "";
	Util::NumToString(m_captureAudioInfo.id, &capture_audio_id);

	std::string origin_audio_id = "";
	Util::NumToString(originAudioID, &origin_audio_id);
	return g_audioFrameProcessor->CaptureAudioInputSetSource(capture_audio_id, origin_audio_id);
}

bool CaptureAudio::SetReplaceRange(UINT64 captureAudioID, INT64 beginPTSMs, INT64 endPTSMs, bool* hit, INT64* bufferBeginPTSMs, INT64* bufferEndPTSMs,
                               bool enableDump, const std::string& dumpTag, const std::string& dumpPath)
{
    std::string capture_audio_id = "";
    Util::NumToString(captureAudioID, &capture_audio_id);
    return g_audioFrameProcessor->CaptureAudioInputRange(capture_audio_id, beginPTSMs, endPTSMs, hit, bufferBeginPTSMs, bufferEndPTSMs, enableDump, dumpTag, dumpPath);
}

bool CaptureAudio::EnableAutoMute(UINT64 captureAudioID, bool enable)
{
    m_captureAudioInfo.enableAutoMute = enable;

    std::string capture_audio_id = "";
    Util::NumToString(captureAudioID, &capture_audio_id);
    return g_audioFrameProcessor->CaptureAudioInputEnableAutoMute(capture_audio_id, enable);
}
