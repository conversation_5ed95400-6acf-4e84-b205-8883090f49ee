#pragma once

#include "Audio.h"
#include "systemutil.h"

class AudioMgr
{
public:
	static AudioMgr* GetInstance();
	AudioMgr();
	~AudioMgr();

	UINT64 AllocAudioID(UINT64 track);
	Audio* GetAudioByID(UINT64 id);
	UINT64 CreateAudio(const AUDIO_INFO* info, const UINT64* id = NULL);
	void   DestoryAudio(Audio* audio);
	void   CreateVISAudio(const SOURCE_INFO* sourceInfo);

	UINT64		  AllocCaptureAudioID(UINT32 track);
	CaptureAudio* GetCaptureAudioByID(UINT64 id);
	UINT64		  CreateCaptureAudio(const CAPTURE_AUDIO_INFO* info, const UINT64* id = NULL);
	bool DestroyCaptureAudio(CaptureAudio* captureAudio, bool enableWrite = false);
protected:
	UINT                     m_audioCounter = Util::GetMicrosecondTimestamp();
	std::map<UINT64, Audio*> m_audios{};

	std::map<UINT64, CaptureAudio*> m_captureAudios{};
	std::map<UINT64, UINT64>        m_captureMapOriginByID{};
};