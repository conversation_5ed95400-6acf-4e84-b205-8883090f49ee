# SelectLayer 方法重构说明

## 重构前的问题

原始的 `SelectLayer` 方法存在以下问题：

1. **方法过长**：单个方法包含了太多逻辑，难以理解和维护
2. **嵌套过深**：多层 if-else 嵌套，代码可读性差
3. **重复代码**：多处相似的错误日志记录和预览选择逻辑
4. **职责不清**：一个方法承担了多种不同场景的处理逻辑
5. **硬编码**：魔法数字（如 2, 3）散布在代码中

## 重构后的改进

### 1. 方法分解
将原来的单一方法分解为多个职责明确的小方法：

- `SelectLayer(UINT64 layerID)` - 主入口方法
- `SelectLayerWithValidLayer(UINT64 layerID, Layer* pLayer)` - 处理有效图层的选择
- `SelectLayerWithoutValidLayer(UINT64 layerID)` - 处理无效图层的选择
- `HandleDBCanvasModeSelection(UINT64 layerID, UINT32 currentPreviewID)` - 处理双画布模式
- `HandleStudioModeSelection(UINT64 layerID)` - 处理工作室模式
- `HandleNormalModeSelection(UINT64 layerID)` - 处理普通模式
- `GetPreviewIDsForCurrentMode()` - 获取当前模式的预览ID列表

### 2. 代码结构优化

#### 主方法简化
```cpp
void Controller::SelectLayer(UINT64 layerID)
{
    if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID))
    {
        SelectLayerWithValidLayer(layerID, pLayer);
    }
    else
    {
        SelectLayerWithoutValidLayer(layerID);
    }
}
```

#### 早期返回模式
使用早期返回减少嵌套：
```cpp
Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
if (!pCanvas)
{
    LOG(ERROR) << "[Controller::SelectLayer] Canvas not found, canvasID: " << canvasID;
    return;
}
```

#### Switch 语句替代多重 if-else
```cpp
switch (modeInfoEx.id)
{
    case LIVE_MODE_LANDSCAPE:
        // 处理横屏模式
        break;
    case LIVE_MODE_PORTRAIT:
        // 处理竖屏模式
        break;
    case LIVE_MODE_DBCANVAS:
        // 处理双画布模式
        break;
    default:
        LOG(WARNING) << "[Controller::GetPreviewIDsForCurrentMode] Unknown mode: " << modeInfoEx.id;
        break;
}
```

### 3. 常量和配置提取

将魔法数字提取为有意义的常量：
```cpp
const std::vector<UINT32> dbCanvasVideoModels = {2, 3};
const std::vector<UINT32> studioPreviewIDs = {m_previewViewID, m_programViewID};
```

### 4. 错误处理改进

统一的错误日志格式，包含更多上下文信息：
```cpp
LOG(ERROR) << "[Controller::SelectLayer] SelectLayer failed, layerID: " << layerID 
           << ", previewID: " << previewID;
```

## 重构的好处

1. **可读性提升**：每个方法职责单一，逻辑清晰
2. **可维护性增强**：修改某个特定场景的逻辑时，只需要修改对应的方法
3. **可测试性改善**：可以单独测试每个子方法
4. **代码复用**：公共逻辑被提取到独立方法中
5. **扩展性更好**：添加新的模式或场景更容易

## 测试建议

建议为重构后的代码编写单元测试，覆盖以下场景：

1. 有效图层ID的选择
2. 无效图层ID的处理
3. 不同模式下的预览选择（横屏、竖屏、双画布）
4. 工作室模式的处理
5. 错误情况的处理（空指针、无效ID等）

## 注意事项

1. 重构保持了原有的业务逻辑不变
2. 所有的错误处理和日志记录都得到保留
3. 方法签名保持兼容，不影响调用方
4. 性能影响微乎其微，主要是方法调用的轻微开销
