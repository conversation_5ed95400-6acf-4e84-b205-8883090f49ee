#include "AudioFrameProcessor.h"

#include <processthreadsapi.h>
#include "AudioDecoder.h"
#include <V3HookAPILayer\WAVWriter.h>
#include <array>

namespace v3
{

AudioFrameProcessor::AudioFrameProcessor()
{
}

AudioFrameProcessor ::~AudioFrameProcessor()
{
}

bool AudioFrameProcessor::Init()
{
    bool s1 = StartAudioProcessThread();
    bool s2 = StartCustomAudioInputProcessThread();
    bool s3 = StartDeleteHandleThread();
    bool s4 = StartDumpFileThread();
    bool success = s1 && s2 && s3 && s4;

    return success;
}

void AudioFrameProcessor::Uninit()
{
    {
        std::vector<std::string> custom_audio_input_id_list;
        CaptureAudioInputGetIDList(&custom_audio_input_id_list);
        for (const auto& id : custom_audio_input_id_list)
        {
            DestroyCaptureAudioInput(id, false);
        }
    }

    {
        std::vector<std::string> audio_input_id_list;
        AudioInputGetListeningList(&audio_input_id_list);
        for (const auto& id : audio_input_id_list)
        {
            AudioInputStopListen(id);
        }
    }

    StopAudioProcessThread();
    StopCustomAudioInputProcessThread();
    StopDeleteHandleThread();
    StopDumpFileThread();
}

bool AudioFrameProcessor::CaptureAudioInputGetIDList(std::vector<std::string>* audio_input_id_list)
{
    if (!audio_input_id_list)
        return false;

    std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);

    for (auto it : custom_audio_input_map_)
    {
        audio_input_id_list->push_back(it.second->custom_audio_input_id);
    }

    return true;
}

void AudioFrameProcessor::SetAddCustomAudioInputHandler(AddCustomAudioInputHandler h)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_add_remove_handler_mutex_);
    custom_audio_input_add_handler_ = h;
}

void AudioFrameProcessor::SetRemoveCustomAudioInputHandler(RemoveCustomAudioInputHandler h)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_add_remove_handler_mutex_);
    custom_audio_input_remove_handler_ = h;
}

void AudioFrameProcessor::SetSetMainAudioTrackDelayHandler(SetMainAudioTrackDelayHandler h)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_add_remove_handler_mutex_);
    custom_audio_input_set_track_delay_handler_ = h;
}

bool AudioFrameProcessor::AddCustomAudioInput(const std::string& audio_input_id, uint32_t track_id, mediasdk::hook_api::CustomAudioInputDelegate* delegate)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_add_remove_handler_mutex_);

    if (!custom_audio_input_add_handler_)
        return false;

    return custom_audio_input_add_handler_(audio_input_id, track_id, delegate);
}

bool AudioFrameProcessor::RemoveCustomAudioInput(const std::string& audio_input_id)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_add_remove_handler_mutex_);

    if (!custom_audio_input_remove_handler_)
        return false;

    custom_audio_input_remove_handler_(audio_input_id);

    return true;
}

bool AudioFrameProcessor::SetMainAudioTrackDelay(const int64_t delay_ms)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_add_remove_handler_mutex_);

    if (!custom_audio_input_set_track_delay_handler_)
        return false;

    return custom_audio_input_set_track_delay_handler_(delay_ms);
}

bool AudioFrameProcessor::CreateCaptureAudioInput(
    uint32_t audio_track,
    const std::string& audio_input_id,
    uint32_t buffer_time_ms)
{
    bool        success = false;
    std::string fail_reason;

    do
    {
        auto ctx = std::make_shared<CustomAudioInputContext>();
        ctx->custom_audio_input_id = audio_input_id;
        ctx->buffer_time_ms = buffer_time_ms;
        ctx->raw_buffer_time_ms = buffer_time_ms + 1000;

        {
            std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);

            auto found = custom_audio_input_map_.find(audio_input_id);
            if (found != custom_audio_input_map_.end())
            {
                fail_reason = "audio_input_id already existed!";
                break;
            }

            custom_audio_input_map_[audio_input_id] = ctx;
        }

        auto impl = std::make_unique<CustomAudioInputImpl>();
        impl->Init(audio_input_id);

        success = AddCustomAudioInput(audio_input_id, audio_track, impl.get());

        if (success)
        {
            ctx->impl = std::move(impl);
            AdjustMainAudioTrackDelay();
        }
        else
        {
            {
                std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);
                custom_audio_input_map_.erase(audio_input_id);
            }
            impl.reset();
        }

    } while (0);

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::CreateCaptureAudioInput] success=%d, audio_input_id=%s, track_id=%u, buffer_time_ms=%u, fail_reason=%s",
                              (int)success, audio_input_id.c_str(), audio_track, buffer_time_ms, fail_reason.c_str());

    return success;
}

bool AudioFrameProcessor::DestroyCaptureAudioInput(
    const std::string& audio_input_id, bool delay_delete)
{
    bool        success = false;
    std::string fail_reason;

    do
    {
        CustomAudioInputContextPtr ctx;

        {
            std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);

            auto found = custom_audio_input_map_.find(audio_input_id);
            if (found != custom_audio_input_map_.end())
            {
                ctx = found->second;
            }
            else
            {
                fail_reason = "audio_input_id not existed or deleted(1)";
                break;
            }
        }

        RemoveCustomAudioInputListenSource(audio_input_id);

        if (ctx)
        {
            if (!ctx->need_remove)
            {
                ctx->remove_tp = std::chrono::high_resolution_clock::now();
                ctx->need_remove = true;

                std::unique_lock<std::mutex> lock(delete_handle_queue_mutex_);

                DeleteHandleContext del_ctx;

                del_ctx.custom_audio_input_id = audio_input_id;
                del_ctx.target_delete_tp = ctx->remove_tp + std::chrono::milliseconds(delay_delete ? ctx->buffer_time_ms : 0);

                delete_handle_queue_.push(del_ctx);

                delete_handle_thread_cv_.notify_one();

                success = true;
            }
            else
            {
                fail_reason = "audio_input_id not existed or deleted(2)";
            }
        }

    } while (0);

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::CreateCaptureAudioInput] success=%d, audio_input_id=%s, delay_delete=%d, fail_reason=%s",
                              (int)success, audio_input_id.c_str(), (int)delay_delete, fail_reason.c_str());

    return success;
}

bool AudioFrameProcessor::CaptureAudioInputEnableAutoMute(
    const std::string& audio_input_id,
    bool               enable)
{
    return true;
}

bool AudioFrameProcessor::CaptureAudioInputSetReplaceAudio(
    const std::string& audio_input_id,
    const std::string& replace_audio_filename)
{

    bool        success = false;
    std::string fail_reason;

    do
    {
        auto ctx = GetCustomAudioInputContext(audio_input_id);
        if (!ctx)
        {
            fail_reason = "audio_input_id not found";
            break;
        }

        std::vector<DecodeAudioFrame> pcm_frame_list;
        bool                          ok = DecodeAudioAsPCM(replace_audio_filename, 48000, 1000, &pcm_frame_list);

        if (!ok)
        {
            fail_reason = "DecodeAudioAsPCM fail";
            break;
        }

        {
            std::unique_lock<std::mutex> lock(ctx->capture_audio_buffer_mutex);

            ctx->capture_audio_frame_count = pcm_frame_list.size();
            ctx->capture_audio_buffer_l.clear();
            ctx->capture_audio_buffer_r.clear();

            for (const auto& frame : pcm_frame_list)
            {
                ctx->capture_audio_buffer_l.append(frame.l_buffer);
                ctx->capture_audio_buffer_r.append(frame.r_buffer);
            }

            if (ctx->capture_audio_buffer_l.empty() || ctx->capture_audio_buffer_l.size() != ctx->capture_audio_buffer_r.size())
            {
                ctx->capture_audio_frame_count = 0;
                ctx->capture_audio_buffer_l.clear();
                ctx->capture_audio_buffer_r.clear();

                fail_reason = "wrong erase_audio_buffer size";
                break;
            }
        }

        success = true;

    } while (0);

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::CaptureAudioInputSetReplaceAudio] success=%d, audio_input_id=%s, audio_file=%s, fail_reason=%s",
                              (int)success, audio_input_id.c_str(), replace_audio_filename.c_str(), fail_reason.c_str());

    return success;
}

bool AudioFrameProcessor::CaptureAudioInputSetSource(
    const std::string& audio_input_id,
    const std::string& source_audio_input_id)
{
    bool        success = false;
    std::string fail_reason;
    do
    {
        auto ctx = GetCustomAudioInputContext(audio_input_id);

        if (!ctx)
        {
            fail_reason = "audio_input_id not found(1)";
            break;
        }

        if (ctx->need_remove)
        {
            fail_reason = "audio_input_id not found(2)";
            break;
        }

        {
            std::unique_lock<std::mutex> lock(custom_audio_input_listen_map_mutex_);
            custom_audio_input_listen_map_[source_audio_input_id] = audio_input_id;
        }

        success = true;

    } while (0);

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::CaptureAudioInputSetReplaceAudio] success=%d, audio_input_id=%s, source_audio_input_id=%s, fail_reason=%s",
                              (int)success, audio_input_id.c_str(), source_audio_input_id.c_str(), fail_reason.c_str());

    return success;
}

bool AudioFrameProcessor::CaptureAudioInputGetSource(
    const std::string& audio_input_id,
    std::string*       source_audio_id)
{
    auto ctx = GetCustomAudioInputContext(audio_input_id);

    if (!ctx)
        return false;

    if (ctx->need_remove)
        return false;

    bool found = false;
    {
        std::unique_lock<std::mutex> lock(custom_audio_input_listen_map_mutex_);

        for (auto it : custom_audio_input_listen_map_)
        {
            if (it.second == audio_input_id)
            {
                if (source_audio_id)
                    *source_audio_id = it.first;

                found = true;
                break;
            }
        }
    }

    return found;
}

bool AudioFrameProcessor::CaptureAudioInputRange(
    const std::string& audio_input_id,
    int64_t begin_pts_ms,
    int64_t end_pts_ms,
    bool* hit /*= nullptr*/,
    int64_t* buffer_begin_pts_ms /*= nullptr*/,
    int64_t* buffer_end_pts_ms /*= nullptr*/,
    bool enable_dump /*= false*/,
    const std::string& dump_tag /*= ""*/,
    const std::string& dump_filename /*= ""*/)
{

    bool    tmp_hit = false;
    bool    success = false;
    int64_t buffer_begin_ns = 0;
    int64_t buffer_end_ns = 0;

    do
    {
        auto ctx = GetCustomAudioInputContext(audio_input_id);

        if (!ctx)
            break;

        int64_t begin_pts_ns = begin_pts_ms * 1000000;
        int64_t end_pts_ns = end_pts_ms * 1000000;

        std::unique_lock<std::mutex> lock(ctx->audio_frame_list_mutex);

        for (auto& frame : ctx->audio_frame_list)
        {
            if ((frame->begin_timestamp_ns >= begin_pts_ns && frame->begin_timestamp_ns <= end_pts_ns) || (frame->timestamp_ns >= begin_pts_ns && frame->timestamp_ns <= end_pts_ns))
            {
                frame->capture_mark = true;
                tmp_hit = true;
            }
        }

        if (!ctx->audio_frame_list.empty())
        {
            buffer_begin_ns = ctx->audio_frame_list.front()->begin_timestamp_ns;
            buffer_end_ns = (*ctx->audio_frame_list.rbegin())->timestamp_ns;
        }

        if (enable_dump)
        {
            std::unique_lock<std::mutex> lock(ctx->raw_audio_frame_list_mutex);
            PostDumpFileTask(dump_tag, dump_filename, ctx->raw_audio_frame_list, tmp_hit);
        }

        success = true;

    } while (0);

    if (hit)
        *hit = tmp_hit;

    if (buffer_begin_pts_ms)
        *buffer_begin_pts_ms = buffer_begin_ns / 1000000;
    if (buffer_end_pts_ms)
        *buffer_end_pts_ms = buffer_end_ns / 1000000;

    return success;
}

void AudioFrameProcessor::SetMonoAudioPCMS16DataCallback(MonoAudioPCMS16DataCallback cb)
{
    std::unique_lock<std::mutex> lock(notify_listener_cb_mutex_);
    notify_listener_cb_ = cb;
}

bool AudioFrameProcessor::AudioInputStartListen(const std::string& audio_input_id, AudioInputFrameTypeEnum frame_type, const std::string& audio_capture_id /* = ""*/)
{
    std::unique_lock<std::mutex> listen_audio_input_list_lock(listen_audio_input_list_mutex_);

    ListenAudioInputContextPtr ctx = GetListenAudioInputContextNoLock(audio_input_id);

    std::string fail_reason;

    if (!ctx)
    {
        ctx = std::make_shared<ListenAudioInputContext>();
        ctx->audio_input_id = audio_input_id;
        listen_audio_input_list_[audio_input_id] = ctx;
    }

    bool success = true;

    switch (frame_type)
    {
    case AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw:
        ctx->listen_raw_frame = true;
        break;
    case AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Filtered:
        ctx->listen_filtered_frame = true;
        break;
    default:
        success = false;
        fail_reason = "invalid frame_type";
        break;
    }

    if (success)
    {
        ++ctx->ref_count;

        std::unique_lock<std::mutex> resampler_ctxs_lock(resampler_ctxs_mutex_);

        auto& resampler_ctxs = frame_type == AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw ? raw_resampler_ctxs_ : filtered_resampler_ctxs_;

        auto found = resampler_ctxs.find(audio_input_id);
        if (found == resampler_ctxs.end())
        {
            ResamplerContextPtr resampler_ctx = std::make_shared<ResamplerContext>();

            resampler_ctx->frame_type = frame_type;
            resampler_ctx->audio_input_id = audio_input_id;
            resampler_ctx->audio_capture_id = audio_capture_id;
            resampler_ctxs[audio_input_id] = resampler_ctx;
        }
    }
    else
    {
        CheckIfNeedRemoveListenAudioInputContextNoLock(ctx);
    }

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::AudioInputStartListen] success=%d, audio_input_id=%s, frame_type=%d, fail_reason=%s",
                              (int)success, audio_input_id.c_str(), (int)frame_type, fail_reason.c_str());

    return success;
}

bool AudioFrameProcessor::AudioInputStopListen(const std::string& audio_input_id)
{
    bool ok1 = AudioInputStopListen(audio_input_id, AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw);
    bool ok2 = AudioInputStopListen(audio_input_id, AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Filtered);

    bool success = ok1 || ok2;

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::AudioInputStopListen] success=%d, audio_input_id=%s",
                              (int)success, audio_input_id.c_str());

    return success;
}

bool AudioFrameProcessor::AudioInputStopListen(const std::string& audio_input_id, AudioInputFrameTypeEnum frame_type)
{
    bool        success = false;
    std::string fail_reason;

    do
    {
        std::unique_lock<std::mutex> lock(listen_audio_input_list_mutex_);

        ListenAudioInputContextPtr ctx = GetListenAudioInputContextNoLock(audio_input_id);

        if (!ctx)
        {
            fail_reason = "audio_input_id not found or already stopped";
            break;
        }

        if (ctx->listen_raw_frame && frame_type == AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw)
        {
            ctx->listen_raw_frame = false;
            --ctx->ref_count;
            success = true;
        }
        if (ctx->listen_filtered_frame && frame_type == AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Filtered)
        {
            ctx->listen_filtered_frame = false;
            --ctx->ref_count;
            success = true;
        }

        if (success)
        {
            CheckIfNeedRemoveListenAudioInputContextNoLock(ctx);

            std::unique_lock<std::mutex> resampler_ctxs_lock(resampler_ctxs_mutex_);

            auto& resampler_ctxs = frame_type == AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw ? raw_resampler_ctxs_ : filtered_resampler_ctxs_;

            auto found = resampler_ctxs.find(audio_input_id);
            if (found != resampler_ctxs.end())
            {
                ResamplerContextPtr resampler_ctx = found->second;
                resampler_ctxs.erase(found);
                resampler_ctx->need_delete = true;
            }
        }
        else
        {
            fail_reason = "already stop listen";
        }

    } while (0);

    LOG(INFO) << StringPrintf("[AudioFrameProcessor::AudioInputStopListen] success=%d, audio_input_id=%s, frame_type=%d, fail_reason=%s",
                              success, audio_input_id.c_str(), (int)frame_type, fail_reason.c_str());

    return success;
}

void AudioFrameProcessor::CheckIfNeedRemoveListenAudioInputContextNoLock(ListenAudioInputContextPtr ctx)
{
    if (ctx->ref_count <= 0)
    {
        listen_audio_input_list_.erase(ctx->audio_input_id);
    }
}

AudioFrameProcessor::ListenAudioInputContextPtr AudioFrameProcessor::GetListenAudioInputContextNoLock(const std::string& audio_input_id)
{
    auto found = listen_audio_input_list_.find(audio_input_id);
    if (found != listen_audio_input_list_.end())
    {
        return found->second;
    }
    return NULL;
}

bool AudioFrameProcessor::AudioInputGetListeningList(std::vector<std::string>* audio_id_list)
{
    std::unique_lock<std::mutex> lock(listen_audio_input_list_mutex_);

    if (!audio_id_list)
        return false;

    audio_id_list->clear();

    for (const auto it : listen_audio_input_list_)
    {
        auto& ctx = it.second;
        if (ctx->listen_raw_frame)
        {
            audio_id_list->push_back(ctx->audio_input_id);
        }
    }
    return true;
}

void AudioFrameProcessor::OnAudioInputRawFrame(const std::string& audio_input_id, const bool is_mute, const mediasdk::AudioSourceFrame& frame)
{
    auto recv_tp = std::chrono::high_resolution_clock::now();

    std::unique_lock<std::mutex> lock(listen_audio_input_list_mutex_);

    AudioFrameContextPtr audio_frame;

    auto listen_audio_input_ctx = GetListenAudioInputContextNoLock(audio_input_id);
    if (listen_audio_input_ctx)
    {
        if (listen_audio_input_ctx->listen_raw_frame)
        {
            if (!audio_frame)
                audio_frame = CreateAudioFrameContext(frame, recv_tp, is_mute);

            AsyncConvertToMonoAudio(audio_input_id, audio_frame, AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw);
        }
    }

    /*auto custom_audio_input_ctx = GetCustomAudioInputContextBySource(audio_input_id);
    if (custom_audio_input_ctx && !custom_audio_input_ctx->need_remove)
    {
        if (!audio_frame)
            audio_frame = CreateAudioFrameContext(frame, recv_tp);

        AddAudioFrame(custom_audio_input_ctx, audio_frame, AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw);
    }*/
}

void AudioFrameProcessor::OnAudioInputFilteredFrame(const std::string& audio_input_id, const bool is_mute, const mediasdk::AudioSourceFrame& frame)
{
    auto recv_tp = std::chrono::high_resolution_clock::now();

    std::unique_lock<std::mutex> lock(listen_audio_input_list_mutex_);

    AudioFrameContextPtr audio_frame;

    auto listen_audio_input_ctx = GetListenAudioInputContextNoLock(audio_input_id);
    if (listen_audio_input_ctx)
    {
        if (listen_audio_input_ctx->listen_filtered_frame)
        {
            if (!audio_frame)
                audio_frame = CreateAudioFrameContext(frame, recv_tp, is_mute);

            AsyncConvertToMonoAudio(audio_input_id, audio_frame, AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Filtered);
        }
    }

    auto custom_audio_input_ctx = GetCustomAudioInputContextBySource(audio_input_id);
    if (custom_audio_input_ctx && !custom_audio_input_ctx->need_remove)
    {
        if (!audio_frame)
            audio_frame = CreateAudioFrameContext(frame, recv_tp, is_mute);

        AddAudioFrame(custom_audio_input_ctx, audio_frame, AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Filtered);
    }
}

void AudioFrameProcessor::OnMixedAudioFrame(uint32_t track_id, const mediasdk::AudioSourceFrame& frame)
{
}

AudioFrameProcessor::CustomAudioInputContextPtr AudioFrameProcessor::GetCustomAudioInputContext(const std::string& audio_input_id)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);

    auto found = custom_audio_input_map_.find(audio_input_id);

    if (found != custom_audio_input_map_.end())
    {
        return found->second;
    }
    return NULL;
}

AudioFrameProcessor::CustomAudioInputContextPtr AudioFrameProcessor::GetCustomAudioInputContextBySource(const std::string& source_audio_input_id)
{
    std::string custom_audio_input_id;

    {
        std::unique_lock<std::mutex> lock(custom_audio_input_listen_map_mutex_);

        auto found = custom_audio_input_listen_map_.find(source_audio_input_id);
        if (found != custom_audio_input_listen_map_.end())
        {
            custom_audio_input_id = found->second;
        }
    }

    return GetCustomAudioInputContext(custom_audio_input_id);
}

AudioFrameProcessor::CustomAudioInputContextPtr AudioFrameProcessor::RemoveCustomAudioInputContext(const std::string& audio_input_id)
{
    CustomAudioInputContextPtr ctx;

    std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);

    auto found = custom_audio_input_map_.find(audio_input_id);

    if (found != custom_audio_input_map_.end())
    {
        ctx = found->second;
        custom_audio_input_map_.erase(found);
    }

    return ctx;
}

void AudioFrameProcessor::RemoveCustomAudioInputListenSource(const std::string& custom_audio_input_id)
{
    std::unique_lock<std::mutex> lock(custom_audio_input_listen_map_mutex_);

    for (auto it = custom_audio_input_listen_map_.begin();
         it != custom_audio_input_listen_map_.end();)
    {
        if (it->second != custom_audio_input_id)
        {
            ++it;
        }
        else
        {
            it = custom_audio_input_listen_map_.erase(it);
        }
    }
}

void AudioFrameProcessor::AddAudioFrame(CustomAudioInputContextPtr custom_audio_input_ctx, AudioFrameContextPtr audio_frame, AudioInputFrameTypeEnum frame_type)
{
    if (frame_type != AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Filtered)
        return;

    struct CurrentContext
    {
        std::mutex&                                     audio_frame_list_mutex;
        std::deque<AudioFrameContextPtr>&               audio_frame_list;
        uint32_t                                        buffer_time_ms;
        const char*                                     buffer_name;
        std::chrono::high_resolution_clock::time_point& audio_frame_list_overflow_tp;
    };

    CurrentContext cc_list[2] = {
        CurrentContext{custom_audio_input_ctx->audio_frame_list_mutex, custom_audio_input_ctx->audio_frame_list, custom_audio_input_ctx->buffer_time_ms, "audio_frame_list", custom_audio_input_ctx->audio_frame_list_overflow_tp},
        CurrentContext{custom_audio_input_ctx->raw_audio_frame_list_mutex, custom_audio_input_ctx->raw_audio_frame_list, custom_audio_input_ctx->raw_buffer_time_ms, "raw_audio_frame_list", custom_audio_input_ctx->raw_audio_frame_list_overflow_tp}};

    audio_frame->target_flush_tp =
        audio_frame->recv_tp + std::chrono::milliseconds(custom_audio_input_ctx->buffer_time_ms);

    audio_frame->target_keep_tp =
        audio_frame->recv_tp + std::chrono::milliseconds(custom_audio_input_ctx->raw_buffer_time_ms);

    for (int i = 0; i < 2; ++i)
    {
        CurrentContext& cc = cc_list[i];

        std::unique_lock<std::mutex> lock(cc.audio_frame_list_mutex);

        auto& audio_frame_list = cc.audio_frame_list;

        audio_frame_list.push_back(audio_frame);

        const uint32_t max_buffer_time = cc.buffer_time_ms + 1000;

        while (!audio_frame_list.empty())
        {
            auto    beg = audio_frame_list.front();
            int64_t buffered_time =
                std::chrono::duration_cast<std::chrono::milliseconds>(audio_frame->recv_tp - beg->recv_tp).count();

            if (buffered_time < max_buffer_time)
            {
                break;
            }
            else
            {
                if (audio_frame->recv_tp - cc.audio_frame_list_overflow_tp > std::chrono::seconds(10))
                {
                    LOG(INFO) << StringPrintf("[AudioFrameProcessor::AddAudioFrame] %s overflow! CustomAudioInput ID: %s, buffered_time=%lld ms, n_frame: %lld",
                                              cc.buffer_name, custom_audio_input_ctx->custom_audio_input_id.c_str(), buffered_time, (int64_t)audio_frame_list.size());
                    cc.audio_frame_list_overflow_tp = audio_frame->recv_tp;
                }

                audio_frame_list.pop_front();
            }
        }
    }

    {
        AudioHandleContext audio_handle_ctx;

        audio_handle_ctx.handle_tp = audio_frame->target_flush_tp;

        audio_handle_ctx.custom_audio_input_id = custom_audio_input_ctx->custom_audio_input_id;

        std::unique_lock<std::mutex> lock(audio_handle_queue_mutex_);

        audio_handle_queue_cv_.notify_all();

        audio_handle_queue_.push(audio_handle_ctx);

        if (audio_handle_queue_.size() > 65535)
        {
            audio_handle_queue_.pop();
        }
    }
}

bool AudioFrameProcessor::StartCustomAudioInputProcessThread()
{
    StopCustomAudioInputProcessThread();

    custom_audio_input_output_thread_stop_ = false;
    custom_audio_input_output_thread_ = std::thread(std::bind(&AudioFrameProcessor::CustomAudioInputProcessThread, this));

    return true;
}

void AudioFrameProcessor::StopCustomAudioInputProcessThread()
{
    custom_audio_input_output_thread_stop_ = true;

    audio_handle_queue_cv_.notify_all();
    if (custom_audio_input_output_thread_.joinable())
        custom_audio_input_output_thread_.join();

    custom_audio_input_output_thread_ = {};
}

void AudioFrameProcessor::CustomAudioInputProcessThread()
{
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);

    std::set<std::string> custom_audio_input_set;

    std::chrono::high_resolution_clock::time_point next_handle_tp =
        std::chrono::high_resolution_clock::now() = std::chrono::high_resolution_clock::now();

    std::chrono::high_resolution_clock::time_point delay_tp;
    int64_t                                        api_usetime_ns = 0;
    int64_t                                        flush_usetime_ns = 0;

    while (!custom_audio_input_output_thread_stop_)
    {
        auto now_tp = std::chrono::high_resolution_clock::now();
        auto handle_tp = now_tp + std::chrono::milliseconds(2);

        if (now_tp - next_handle_tp > std::chrono::milliseconds(10))
        {
            if (now_tp - delay_tp > std::chrono::seconds(10))
            {
                int64_t delay_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(now_tp - next_handle_tp).count();
                double  delay_ms = delay_ns / 1000000.0;
                double  api_usetime_ms = api_usetime_ns / 1000000.0;
                double  flush_usetime_ms = flush_usetime_ns / 1000000.0;
                delay_tp = now_tp;
                LOG(INFO) << StringPrintf("[AudioFrameProcessor::CustomAudioInputProcessThread] Delay=%lf ms, flush_usetime_ms=%lf ms, api_usetime=%lf ms", delay_ms, flush_usetime_ms, api_usetime_ms);
            }
        }

        custom_audio_input_set.clear();

        {
            std::unique_lock<std::mutex> lock(audio_handle_queue_mutex_);

            while (!custom_audio_input_output_thread_stop_ && audio_handle_queue_.empty())
            {
                audio_handle_queue_cv_.wait_for(lock, std::chrono::seconds(1));
            }

            while (!audio_handle_queue_.empty())
            {
                const auto& ctx = audio_handle_queue_.top();
                if (ctx.handle_tp <= handle_tp)
                {
                    custom_audio_input_set.insert(ctx.custom_audio_input_id);
                    audio_handle_queue_.pop();
                }
                else
                {
                    break;
                }
            }
        }

        next_handle_tp = now_tp + std::chrono::milliseconds(100);
        api_usetime_ns = 0;
        flush_usetime_ns = 0;

        auto tb = std::chrono::high_resolution_clock::now();

        for (const auto& custom_audio_input_id : custom_audio_input_set)
        {
            auto ctx = GetCustomAudioInputContext(custom_audio_input_id);
            if (ctx)
                FlushCustomAudioInput(ctx, handle_tp, next_handle_tp, api_usetime_ns);
        }

        auto te = std::chrono::high_resolution_clock::now();

        flush_usetime_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(te - tb).count();

        HighPrecisionSleep(next_handle_tp);
    }
}

void AudioFrameProcessor::HighPrecisionSleep(const std::chrono::high_resolution_clock::time_point& end_tp)
{
    using clock = std::chrono::high_resolution_clock;
    const auto start = clock::now();
    const auto end = end_tp;

    while (true)
    {
        const auto remaining = end - clock::now();

        if (remaining <= remaining.zero())
            break;

        if (remaining > std::chrono::milliseconds(1))
        {
            std::this_thread::sleep_for(remaining - std::chrono::milliseconds(1));
        }
        else
        {
            while (clock::now() < end)
            {
                std::this_thread::yield();
            }
            break;
        }
    }
}

void AudioFrameProcessor::FlushCustomAudioInput(
    CustomAudioInputContextPtr                            ctx,
    const std::chrono::high_resolution_clock::time_point& handle_tp,
    std::chrono::high_resolution_clock::time_point&       next_handle_tp,
    int64_t&                                              api_usetime_ns)
{
    std::array<char, 1024 * 256> stack_buffer;

    ctx->flush_audio_frame_list.clear();

    {
        std::unique_lock<std::mutex> lock(ctx->audio_frame_list_mutex);

        while (!ctx->audio_frame_list.empty())
        {
            auto audio_frame = ctx->audio_frame_list.front();

            bool need_flush = audio_frame->target_flush_tp <= handle_tp;

            if (need_flush)
            {
                ctx->flush_audio_frame_list.push_back(audio_frame);
                ctx->audio_frame_list.pop_front();
            }
            else
                break;
        }

        if (!ctx->audio_frame_list.empty())
        {
            if (ctx->audio_frame_list.front()->target_flush_tp < next_handle_tp)
            {
                next_handle_tp = ctx->audio_frame_list.front()->target_flush_tp;
            }
        }
    }

    {
        std::unique_lock<std::mutex> lock(ctx->raw_audio_frame_list_mutex);

        while (!ctx->raw_audio_frame_list.empty())
        {
            auto audio_frame = ctx->raw_audio_frame_list.front();

            bool need_keep = audio_frame->target_keep_tp > handle_tp;

            if (!need_keep)
                ctx->raw_audio_frame_list.pop_front();
            else
                break;
        }
    }

    if (ctx->flush_audio_frame_list.empty())
        return;

    if (!ctx->impl)
    {
        ctx->flush_audio_frame_list.clear();
        return;
    }

    for (auto audio_frame : ctx->flush_audio_frame_list)
    {
        mediasdk::AudioSourceFrame tmp{};
        AudioFrameContextBase      mod_frame{};

        if (audio_frame->capture_mark && !audio_frame->is_mute)
        {
            std::unique_lock<std::mutex> lock(ctx->capture_audio_buffer_mutex);

            if (!ctx->capture_audio_buffer_l.empty())
            {
                size_t channel_buffer_size = size_t(audio_frame->count) * size_t(audio_frame->block_size);

                size_t target_copy_size = channel_buffer_size;

                mod_frame = MakeAudioFrameRef(audio_frame.get(), stack_buffer.data(), stack_buffer.size());

                // TODO adjust volumn
                while (target_copy_size > 0)
                {
                    size_t start_pos = ctx->capture_audio_idx % ctx->capture_audio_buffer_l.size();
                    size_t max_len = ctx->capture_audio_buffer_l.size() - start_pos;
                    size_t copy_size = (std::min)(max_len, target_copy_size);

                    if (mod_frame.data[0])
                        memcpy(mod_frame.data[0], ctx->capture_audio_buffer_l.c_str() + start_pos, copy_size);
                    if (mod_frame.data[1])
                        memcpy(mod_frame.data[1], ctx->capture_audio_buffer_r.c_str() + start_pos, copy_size);

                    target_copy_size -= copy_size;

                    ctx->capture_audio_idx += copy_size;
                }

                ToAudioSourceFrame(&mod_frame, &tmp);
            }
            else
            {
                ToAudioSourceFrame(audio_frame.get(), &tmp);
                ctx->capture_audio_idx = 0;
            }
        }
        else
        {
            // for test
            // memset(audio_frame->buffer.data(), 0, audio_frame->buffer.size());

            ToAudioSourceFrame(audio_frame.get(), &tmp);
            ctx->capture_audio_idx = 0;
        }

        {
            std::unique_lock<std::mutex> impl_lock(ctx->impl_mutex);

            if (ctx->impl)
            {
                auto t1 = std::chrono::high_resolution_clock::now();
                ctx->impl->InputCustomAudioData(tmp);
                auto t2 = std::chrono::high_resolution_clock::now();
                api_usetime_ns += std::chrono::duration_cast<std::chrono::nanoseconds>(t2 - t1).count();
            }
            else
            {
                break;
            }
        }
    }

    if (!ctx->first_flush_audio_frame)
    {
        LOG(INFO) << StringPrintf("[AudioFrameProcessor::FlushCustomAudioInput] CustomAudioInput first flush, custom_audio_input_id:%s", ctx->custom_audio_input_id.c_str());
        ctx->first_flush_audio_frame = true;
    }

    ctx->flush_audio_frame_list.clear();
}

AudioFrameProcessor::AudioFrameContextPtr AudioFrameProcessor::CreateAudioFrameContext(
    const mediasdk::AudioSourceFrame&                     frame,
    const std::chrono::high_resolution_clock::time_point& recv_tp,
    const bool                                            is_mute)
{
    auto audio_frame = std::make_shared<AudioFrameContext>();

    audio_frame->sample_rate = frame.sample_rate;
    audio_frame->block_size = frame.block_size;
    audio_frame->count = frame.count;
    audio_frame->channel_count = frame.channel_count;
    audio_frame->timestamp_ns = frame.timestamp_ns;
    audio_frame->recv_tp = recv_tp;
    audio_frame->is_mute = is_mute;

    if (frame.sample_rate > 0)
    {
        audio_frame->begin_timestamp_ns = frame.timestamp_ns - (int64_t)frame.count * (int64_t)1000000000ll / (int64_t)frame.sample_rate;
    }
    else
    {
        audio_frame->begin_timestamp_ns = audio_frame->timestamp_ns;
    }

    size_t channel_buffer_size = size_t(frame.count) * size_t(frame.block_size);
    size_t channel_buffer_size_align = channel_buffer_size + (16 - channel_buffer_size % 16);
    size_t buffer_size = 16 + channel_buffer_size_align * frame.channel_count;
    audio_frame->buffer.resize(buffer_size);

    uint8_t* base_ptr = (uint8_t*)audio_frame->buffer.c_str();
    intptr_t start = reinterpret_cast<intptr_t>(base_ptr);
    int      n = start % 16;
    int      offset = n == 0 ? 0 : 16 - n;
    base_ptr += offset;

    for (int i = audio_frame->channel_count; i < mediasdk::AudioFramePlane::kMaxAudioPlanes; ++i)
    {
        audio_frame->data[i] = NULL;
    }

    for (int i = 0; i < audio_frame->channel_count; ++i)
    {
        uint8_t* ptr = base_ptr + i * channel_buffer_size_align;

        audio_frame->data[i] = ptr;

        if (frame.data && frame.data[i])
        {
            memcpy(audio_frame->data[i], frame.data[i], channel_buffer_size);
        }
    }

    return audio_frame;
}

void AudioFrameProcessor::ToAudioSourceFrame(const AudioFrameContextBase* in, mediasdk::AudioSourceFrame* out)
{
    for (int i = 0; i < mediasdk::AudioFramePlane::kMaxAudioPlanes; ++i)
    {
        out->data[i] = in->data[i];
    }

    out->sample_rate = in->sample_rate;
    out->block_size = in->block_size;
    out->count = in->count;
    out->channel_count = in->channel_count;
    out->timestamp_ns = in->timestamp_ns;
}

AudioFrameProcessor::AudioFrameContextBase AudioFrameProcessor::MakeAudioFrameRef(const AudioFrameContext* in, void* buffer, size_t len)
{
    AudioFrameContextBase frame = *in;
    memcpy(frame.data, in->data, sizeof(frame.data));

    uint8_t* base_ptr = (uint8_t*)buffer;
    intptr_t start = reinterpret_cast<intptr_t>(base_ptr);
    int      n = start % 16;
    int      offset = n == 0 ? 0 : 16 - n;
    base_ptr += offset;

    if (len - offset >= in->buffer.size())
    {
        memcpy(buffer, in->buffer.data(), in->buffer.size());

        for (int i = 0; i < _countof(frame.data); ++i)
        {
            intptr_t offset = (intptr_t)in->data[i] - (intptr_t)in->buffer.data();
            if (offset >= 0)
                frame.data[i] = base_ptr + offset;
            else
                frame.data[i] = nullptr;
        }
    }

    return frame;
}

void AudioFrameProcessor::AsyncConvertToMonoAudio(const std::string& audio_input_id, AudioFrameContextPtr audio_frame, AudioInputFrameTypeEnum frame_type)
{

    ResamplerContextPtr ctx;

    {
        std::unique_lock<std::mutex> lock(resampler_ctxs_mutex_);

        auto& resampler_ctxs = frame_type == AudioInputFrameTypeEnum::kAudioInputFrameTypeEnum_Raw ? raw_resampler_ctxs_ : filtered_resampler_ctxs_;
        auto  found = resampler_ctxs.find(audio_input_id);
        if (found != resampler_ctxs.end())
        {
            ctx = found->second;
        }
    }

    if (!ctx)
        return;

    {
        std::unique_lock<std::mutex> lock(ctx->frame_list_mutex);
        ctx->frame_list.push_back(audio_frame);
        if (ctx->frame_list.size() > 500)
            ctx->frame_list.pop_front();
    }

    {
        std::unique_lock<std::mutex> lock(audio_process_cv_mutex_);
        ++audio_process_task_count_;
        audio_process_cv_.notify_one();
    }
}

bool AudioFrameProcessor::StartAudioProcessThread()
{
    StopAudioProcessThread();

    audio_process_stop_ = false;
    audio_process_thread_ = std::thread(std::bind(&AudioFrameProcessor::AudioProcessThread, this));

    return true;
}

void AudioFrameProcessor::StopAudioProcessThread()
{
    if (audio_process_stop_)
        return;

    audio_process_stop_ = true;
    audio_process_cv_.notify_one();

    if (audio_process_thread_.joinable())
        audio_process_thread_.join();

    audio_process_thread_ = {};
}

void AudioFrameProcessor::AudioProcessThread()
{
    uint64_t handled_task_count = 0;

    std::vector<ResamplerContextPtr> ctx_list;

    while (!audio_process_stop_)
    {
        uint64_t current_task_count = 0;
        {
            std::unique_lock<std::mutex> lock(audio_process_cv_mutex_);
            audio_process_cv_.wait(lock, [&]() {
                return audio_process_stop_ || handled_task_count != audio_process_task_count_;
            });

            if (audio_process_stop_)
                break;

            current_task_count = audio_process_task_count_;
        }

        ctx_list.clear();

        {
            std::unique_lock<std::mutex> lock(resampler_ctxs_mutex_);

            for (auto it : raw_resampler_ctxs_)
            {
                auto&                        ctx = it.second;
                std::unique_lock<std::mutex> ctx_frame_list_lock(ctx->frame_list_mutex);
                if (!ctx->frame_list.empty())
                {
                    ctx_list.push_back(ctx);
                }
            }

            for (auto it : filtered_resampler_ctxs_)
            {
                auto&                        ctx = it.second;
                std::unique_lock<std::mutex> ctx_frame_list_lock(ctx->frame_list_mutex);
                if (!ctx->frame_list.empty())
                {
                    ctx_list.push_back(ctx);
                }
            }
        }

        if (ctx_list.empty())
        {
            handled_task_count = current_task_count;
        }
        else
        {
            ++handled_task_count;
        }

        for (auto ctx : ctx_list)
        {
            std::deque<AudioFrameContextPtr> frame_list;
            {
                std::unique_lock<std::mutex> ctx_frame_list_lock(ctx->frame_list_mutex);
                frame_list = std::move(ctx->frame_list);
            }

            // Todo delay reset

            for (const auto audio_frame : frame_list)
            {
                auto& resampler = ctx->resampler;

                // TODO check format
                bool src_fmt_change = false;

                if (src_fmt_change || !ctx->resampler_init)
                {
                    ctx->resampler.Init(audio_frame->sample_rate, AudioSampleFormatEnum::kAudioSampleFormat_FLOAT_PLANAR, AudioChannelLayoutEnum::kAudioChannelLayout_STEREO,
                                        16000, AudioSampleFormatEnum::kAudioSampleFormat_S16, AudioChannelLayoutEnum::kAudioChannelLayout_MONO);

                    ctx->resampler_init = true;
                }

                ++ctx->nb_input_frame;

                if (!ctx->log_first_input && ctx->nb_input_frame > 0)
                {
                    LOG(INFO) << StringPrintf("[AudioFrameProcessor::AudioProcessThread] Listener, recv first input frame, audio_input_id:%s", ctx->audio_input_id.c_str());
                    ctx->log_first_input = true;
                }

                bool convert_ok = resampler.Resample(audio_frame->data, audio_frame->count);

                if (convert_ok)
                {
                    ++ctx->nb_output_frame;

                    auto data = resampler.GetOutputData();
                    auto nb_sample_count = resampler.GetOutputCount();

                    if (!ctx->log_first_output && ctx->nb_output_frame > 0)
                    {
                        LOG(INFO) << StringPrintf("[AudioFrameProcessor::AudioProcessThread] Listener, output first resampled frame, audio_input_id:%s", ctx->audio_input_id.c_str());
                        ctx->log_first_output = true;
                    }

                    if (data && data[0])
                    {
                        int64_t  begin_timestamp_ns = audio_frame->timestamp_ns - nb_sample_count * (1000000000ll) / 16000ll;
                        int16_t* pcm_data = reinterpret_cast<int16_t*>(data[0]);
                        NotifyMonoAudioPCMS16Data(ctx->audio_input_id, ctx->audio_capture_id, ctx->frame_type, begin_timestamp_ns, pcm_data, nb_sample_count);
                    }
                }
            }
        }
    }
}

void AudioFrameProcessor::NotifyMonoAudioPCMS16Data(const std::string&      audio_input_id,
                                                    const std::string&      audio_capture_id,
                                                    AudioInputFrameTypeEnum frame_type,
                                                    int64_t                 timestamp_ns,
                                                    int16_t*                pcm_s16_data,
                                                    int32_t                 nb_sample_count)
{
    if (!pcm_s16_data || nb_sample_count <= 0)
        return;

    std::unique_lock<std::mutex> lock(notify_listener_cb_mutex_);

    if (notify_listener_cb_)
    {
        notify_listener_cb_(audio_input_id, audio_capture_id, frame_type, timestamp_ns, pcm_s16_data, nb_sample_count);
    }
}

void AudioFrameProcessor::AdjustMainAudioTrackDelay()
{
    int max_buffer_time_ms = 0;

    {
        std::unique_lock<std::mutex> lock(custom_audio_input_map_mutex_);

        for (auto it : custom_audio_input_map_)
        {
            auto buffer_time_ms = it.second->buffer_time_ms;
            if (buffer_time_ms > max_buffer_time_ms)
                max_buffer_time_ms = buffer_time_ms;
        }
    }

    if (max_buffer_time_ms != main_audio_track_delay_ms_)
    {
        main_audio_track_delay_ms_ = max_buffer_time_ms;

        int64_t delay_ms = main_audio_track_delay_ms_;

        if (delay_ms > 0)
            delay_ms += 200;

        LOG(INFO) << StringPrintf("[AudioFrameProcessor::AdjustMainAudioTrackDelay] delay_ms=%lld", delay_ms);

        SetMainAudioTrackDelay(delay_ms);
    }
}

bool AudioFrameProcessor::StartDeleteHandleThread()
{
    StopDeleteHandleThread();

    delete_handle_thread_stop_ = false;
    delete_handle_thread_ = std::thread(std::bind(&AudioFrameProcessor::DeleteHandleThread, this));

    return true;
}

void AudioFrameProcessor::StopDeleteHandleThread()
{
    delete_handle_thread_stop_ = true;
    delete_handle_thread_cv_.notify_one();
    if (delete_handle_thread_.joinable())
        delete_handle_thread_.join();
    delete_handle_thread_ = {};
}

void AudioFrameProcessor::DeleteHandleThread()
{
    std::vector<std::string> custom_audio_input_id_list;

    std::chrono::high_resolution_clock::time_point
        next_tp = std::chrono::high_resolution_clock::now();

    while (1)
    {
        {
            std::unique_lock<std::mutex> lock(delete_handle_queue_mutex_);

            if (delete_handle_thread_stop_ && delete_handle_queue_.empty())
                break;

            auto now_tp = std::chrono::high_resolution_clock::now();
            next_tp = now_tp + std::chrono::seconds(1);

            while (!delete_handle_queue_.empty())
            {
                auto& info = delete_handle_queue_.top();
                if (info.target_delete_tp >= now_tp)
                    break;

                custom_audio_input_id_list.push_back(info.custom_audio_input_id);
                delete_handle_queue_.pop();
            }

            if (!delete_handle_queue_.empty())
            {
                next_tp = delete_handle_queue_.top().target_delete_tp;
            }
        }

        for (const std::string& custom_audio_input_id : custom_audio_input_id_list)
        {
            auto ctx = RemoveCustomAudioInputContext(custom_audio_input_id);
            if (ctx->impl)
            {
                {
                    std::unique_lock<std::mutex> impl_lock(ctx->impl_mutex);
                    if (ctx->impl)
                    {
                        ctx->impl->SetDeleteOnDetach(true);
                        ctx->impl.release();
                    }
                }

                RemoveCustomAudioInput(custom_audio_input_id);
            }
        }
        custom_audio_input_id_list.clear();

        AdjustMainAudioTrackDelay();

        {
            std::unique_lock<std::mutex> lock(delete_handle_queue_mutex_);
            if (delete_handle_queue_.empty() ||
                (!delete_handle_queue_.empty() && delete_handle_queue_.top().target_delete_tp >= next_tp))
            {
                delete_handle_thread_cv_.wait_until(lock, next_tp);
            }
        }
    }
}

bool AudioFrameProcessor::StartDumpFileThread()
{
    StopDumpFileThread();

    dump_file_thread_stop_ = false;
    dump_file_thread_ = std::thread(&AudioFrameProcessor::DumpFileThread, this);

    return true;
}

void AudioFrameProcessor::StopDumpFileThread()
{
    if (dump_file_thread_stop_)
        return;

    {
        std::unique_lock<std::mutex> lock(dump_file_thread_mutex_);
        dump_file_thread_stop_ = true;
        dump_file_tasks_.clear();
        dump_file_thread_cv_.notify_all();
    }

    if (dump_file_thread_.joinable())
        dump_file_thread_.join();
    dump_file_thread_ = {};
}

void AudioFrameProcessor::DumpFileThread()
{

    dump_file_resampler_.Init(48000, AudioSampleFormatEnum::kAudioSampleFormat_FLOAT_PLANAR, AudioChannelLayoutEnum::kAudioChannelLayout_STEREO,
                              16000, AudioSampleFormatEnum::kAudioSampleFormat_S16, AudioChannelLayoutEnum::kAudioChannelLayout_MONO);

    std::deque<DumpFileTaskPtr> dump_file_tasks;

    while (!dump_file_thread_stop_)
    {
        {
            std::unique_lock<std::mutex> lock(dump_file_thread_mutex_);

            while (!dump_file_thread_stop_ && dump_file_tasks_.empty())
            {
                dump_file_thread_cv_.wait(lock);
            }

            if (dump_file_thread_stop_)
                break;

            dump_file_tasks = std::move(dump_file_tasks_);
        }

        for (const auto& task : dump_file_tasks)
        {
            ProcessDumpFileTask(task.get());
        }
        dump_file_tasks.clear();
    }

    dump_file_resampler_.Uninit();
}

bool AudioFrameProcessor::ProcessDumpFileTask(DumpFileTask* task)
{
    if (!task)
        return false;

    bool success = false;

    do
    {

        if (!task->audio_frame_list.empty())
        {
            v3::WAVWriter wav;

            bool ok = wav.Open(task->filename, 16000, 1, v3::WAVWriter::SampleDataTypeEnum::kSampleDataType_S16);
            if (!ok)
                break;

            for (const auto& frame : task->audio_frame_list)
            {
                bool ok = dump_file_resampler_.Resample(frame->data, frame->count);
                if (ok)
                {
                    auto data_ptr = dump_file_resampler_.GetOutputData();
                    auto nb_sample_count = dump_file_resampler_.GetOutputCount();

                    if (data_ptr && nb_sample_count > 0)
                    {
                        wav.WriteData(data_ptr, nb_sample_count, true);
                    }
                }
            }

            wav.Close();
        }

        success = true;

    } while (0);

    {
        std::unique_lock<std::mutex> lock(dump_file_cb_mutex);
        if (dump_file_cb_)
            dump_file_cb_(task->tag, task->filename, task->hit, success);
    }

    return success;
}

void AudioFrameProcessor::PostDumpFileTask(const std::string& tag, const std::string& filename, const std::deque<AudioFrameContextPtr>& frames, bool hit)
{
    DumpFileTaskPtr task = std::make_shared<DumpFileTask>();
    task->tag = tag;
    task->filename = filename;
    task->audio_frame_list = frames;
    task->hit = hit;

    {
        std::unique_lock<std::mutex> lock(dump_file_thread_mutex_);
        if (!dump_file_thread_stop_)
        {
            dump_file_tasks_.push_back(task);
            dump_file_thread_cv_.notify_all();
        }
    }
}

void AudioFrameProcessor::SetCaptureAudioInputRangeDumpFileCallback(CaptureAudioInputRangeDumpFileCallback cb)
{
    std::unique_lock<std::mutex> lock(dump_file_cb_mutex);
    dump_file_cb_ = cb;
}

} // namespace v3