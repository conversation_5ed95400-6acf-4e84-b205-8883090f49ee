#include "stdafx.h"
#include "audio.h"

namespace LS
{
Audio::RequestList Audio::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Audio::EnumAppAudio>());
    list.push_back(std::make_unique<Audio::EnumCaptureAudio>());
    list.push_back(std::make_unique<Audio::EnumRenderAudio>());
    list.push_back(std::make_unique<Audio::GetDefaultInput>());
    list.push_back(std::make_unique<Audio::GetDefaultOutput>());
    list.push_back(std::make_unique<Audio::SystemSupportAppAudio>());

    list.push_back(std::make_unique<Audio::CreateWASAudio>());
    list.push_back(std::make_unique<Audio::CreateAPPAudio>());
    list.push_back(std::make_unique<Audio::CreatePCMAudio>());
    list.push_back(std::make_unique<Audio::RemoveAudio>());

    list.push_back(std::make_unique<Audio::IsEmpty>());
    list.push_back(std::make_unique<Audio::UpdatePCM>());
    list.push_back(std::make_unique<Audio::SetRenderDeviceID>());
    list.push_back(std::make_unique<Audio::GetPerformance>());

    list.push_back(std::make_unique<Audio::AddFilter>());
    list.push_back(std::make_unique<Audio::RemoveFilter>());

    list.push_back(std::make_unique<Audio::SetAudioUniAttr>());
    list.push_back(std::make_unique<Audio::GetAudioUniAttr>());

    list.push_back(std::make_unique<Audio::SetANSOption>());
    list.push_back(std::make_unique<Audio::EnableAudioInputEchoDetection>());
    list.push_back(std::make_unique<Audio::EnableAudioInputNoiseDetection>());
    list.push_back(std::make_unique<Audio::SetAudioInputRenderDeviceID>());

    list.push_back(std::make_unique<Audio::StartAudioCapture>());
    list.push_back(std::make_unique<Audio::StopAudioCapture>());
    list.push_back(std::make_unique<Audio::UpdateCapturedAudioID>());
    list.push_back(std::make_unique<Audio::SetAudioReplacePTS>());
    return list;
}

void Audio::SetVISAudioInfo(AUDIO_SETTING& audioSetting, const ls_audio::AudioSettingParam& audio_setting)
{
    if (audio_setting.has_volume())
    {
        audioSetting.volume = audio_setting.volume();
    }
    if (audio_setting.has_balanceing())
    {
        audioSetting.balanceing = audio_setting.balanceing();
    }
    if (audio_setting.has_down_mix_mono())
    {
        audioSetting.downMixMono = audio_setting.down_mix_mono();
    }
    if (audio_setting.has_interval())
    {
        audioSetting.interval = audio_setting.interval();
    }
    if (audio_setting.has_monitor_type())
    {
        audioSetting.monitorType = static_cast<AUDIO_MONITOR_TYPE>(audio_setting.monitor_type());
    }
    if (audio_setting.has_mute())
    {
        audioSetting.mute = audio_setting.mute();
    }
    if (audio_setting.has_sync_offset())
    {
        audioSetting.syncOffset = audio_setting.sync_offset();
    }
}

bool Audio::EnumAppAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<DSHOW> devices{};
    controller->EnumAppAudio(&devices);
    for (const auto& device : devices)
    {
        auto ref = rsp.add_audio_devices();
        ref->set_id(device.id);
        ref->set_name(device.name);
        ref->set_exe(device.exe);
    }

    return true;
}

bool Audio::EnumCaptureAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<DSHOW> devices{};
    controller->EnumCaptureAudio(&devices);
    for (const auto& device : devices)
    {
        auto ref = rsp.add_audio_devices();
        ref->set_id(device.id);
        ref->set_name(device.name);
    }
    return true;
}

bool Audio::EnumRenderAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<DSHOW> devices{};
    controller->EnumRenderAudio(&devices);
    for (const auto& device : devices)
    {
        auto ref = rsp.add_audio_devices();
        ref->set_id(device.id);
        ref->set_name(device.name);
    }
    return true;
}

bool Audio::GetDefaultInput::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    DSHOW device{};
    controller->GetDefaultInput(&device);
    ls_base::DShow dshow{};
    dshow.set_id(device.id);
    dshow.set_name(device.name);
    rsp.mutable_audio_device()->CopyFrom(dshow);
    return true;
}

bool Audio::GetDefaultOutput::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    DSHOW device{};
    controller->GetDefaultOutput(&device);
    ls_base::DShow dshow{};
    dshow.set_id(device.id);
    dshow.set_name(device.name);
    rsp.mutable_audio_device()->CopyFrom(dshow);
    return true;
}

bool Audio::SystemSupportAppAudio::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	bool support = false;
	controller->SystemSupportAppAudio(&support);
	rsp.set_support(support);
	return true;
}

void SetAudioUniformInfo(AUDIO_INFO& audioInfo, const ls_audio::AudioUniAttr& attr, UINT64* cmd = NULL)
{
    if (attr.has_type())
    {
        audioInfo.type = (AUDIO_TYPE)attr.type();
    }
    if (attr.has_audio_track())
    {
        audioInfo.audioTrack = attr.audio_track();
        if (cmd)
            *cmd |= AUDIO_CONTROL_SET_AUDIO_TRACK;
    }
    if (attr.has_device())
    {
        audioInfo.device.id = attr.device().id();
        audioInfo.device.name = attr.device().name();
    }
    if (attr.has_setting_param())
    {
        const ls_audio::AudioSettingParam& param = attr.setting_param();
        AUDIO_SETTING&                     setting = audioInfo.audioSetting;
        if (param.has_balanceing())
        {
            setting.balanceing = param.balanceing();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_BALANCEING;
        }
        if (param.has_down_mix_mono())
        {
            setting.downMixMono = param.down_mix_mono();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_DOWN_MIX_MONO;
        }
        if (param.has_volume())
        {
            setting.volume = param.volume();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_VOLUME;
        }
        if (param.has_interval())
        {
            setting.interval = param.interval();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_INTERVAL;
        }
        if (param.has_monitor_type())
        {
            setting.monitorType = (AUDIO_MONITOR_TYPE)param.monitor_type();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_MONITOR_TYPE;
        }
        if (param.has_mute())
        {
            setting.mute = param.mute();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_MUTE;
        }
        if (param.has_sync_offset())
        {
            setting.syncOffset = param.sync_offset();
            if (cmd)
                *cmd |= AUDIO_CONTROL_SET_SYNC_OFFSET;
        }
    }
    if (attr.has_capture_param())
    {
        ls_audio::AudioCaptureParam param = attr.capture_param();
        AUDIO_CAPTURE               capture;
        if (param.has_sample_per_sec())
        {
            capture.samplePerSec = param.sample_per_sec();
        }
        if (param.has_bits_per_sec())
        {
            capture.bitsPerSec = param.bits_per_sec();
        }
        if (param.has_channels())
        {
            capture.channels = param.channels();
        }
        if (param.has_frames())
        {
            capture.frames = param.frames();
        }
        if (param.has_planes())
        {
            capture.planes = param.planes();
        }
        if (param.has_audio_format())
        {
            capture.audioFormat = param.audio_format();
        }
        if (param.has_channel_layout())
        {
            capture.channelLayout = param.channel_layout();
        }
        audioInfo.audioCapture = capture;
    }
	if (attr.has_enable_aec())
	{
		audioInfo.enableAec = attr.enable_aec();
		if (cmd)
			*cmd |= AUDIO_CONTROL_ENABLE_AEC;
	}
    if (attr.has_aec_ref_id())
    {
        audioInfo.aecRefID = attr.aec_ref_id();
        if (cmd)
            *cmd |= AUDIO_CONTROL_SET_AEC_REF_ID;
    }
    if (attr.has_agc_option())
    {
		audioInfo.agcOption = (AUDIO_AGC_OPTION)attr.agc_option();
		if (cmd)
			*cmd |= AUDIO_CONTROL_SET_AGC_OPTION;
    }
    if (attr.has_ans_option())
    {
		audioInfo.ansOption = (AUDIO_ANS_OPTION)attr.ans_option();
		if (cmd)
			*cmd |= AUDIO_CONTROL_SET_ANS_OPTION;
    }
}

bool Audio::CreateWASAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    WAS_AUDIO iWas{};
    iWas.type = (AUDIO_INPUT_TYPE)req.audio_input_type();
    iWas.isLyrax = req.is_lyrax();
    if (req.has_mic_input_level())
    {
        iWas.micInputLevel = req.mic_input_level();
    }
    iWas.sysEnhancementMode = (BYPASS_SYSTEM_ENHANCEMENT_MODE)req.sys_enhancement_mode();
    iWas.micBoostLevel = (MICROPHONE_BOOST_GAIN_PROPORTION)req.mic_boost_level();
    for (int i = 0; i < req.microphone_boost_partnames_size(); ++i)
    {
        iWas.microphoneBoostPartnames.push_back(req.microphone_boost_partnames(i));
    }

    AUDIO_INFO audioInfo{};
    SetAudioUniformInfo(audioInfo, req.audio_uni_attr());
    audioInfo.type = AUDIO_WAS;
    audioInfo.audio = iWas;

    UINT64 audioID = controller->AddAudio(&audioInfo);
    if (controller->FindAudioByID(audioID))
    {
        std::string audio_id = "";
        Util::NumToString(audioID, &audio_id);

        AUDIO_INFO oAudioInfo{};
        controller->GetAudioInfo(audioID, &oAudioInfo);
        WAS_AUDIO oWas = std::get<WAS_AUDIO>(oAudioInfo.audio);
        rsp.set_audio_id(audio_id);
    }
    else
    {
        LOG(ERROR) << "[Audio::CreateWASAudio] audio create failed, audio_id: " << audioID << " audio_type: " << audioInfo.type;
        return false;
    }

    return true;
}

bool Audio::CreateAPPAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    APP_AUDIO iApp{};
    iApp.excludePID = req.exclude_pid();

    AUDIO_INFO audioInfo{};
    SetAudioUniformInfo(audioInfo, req.audio_uni_attr());
    audioInfo.type = AUDIO_APP;
    audioInfo.audio = iApp;

    UINT64 audioID = controller->AddAudio(&audioInfo);
    if (controller->FindAudioByID(audioID))
    {
        std::string audio_id = "";
        Util::NumToString(audioID, &audio_id);
        rsp.set_audio_id(audio_id);
    }
    else
    {
        LOG(ERROR) << "[Audio::CreateAPPAudio] audio create failed, audio_id: " << audioID << " audio_type: " << audioInfo.type;
        return false;
    }

    return true;
}

bool Audio::CreatePCMAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    PCM_AUDIO iPcm{};
    iPcm.bufLeft = req.buffer().buffer_l();
    iPcm.bufRight = req.buffer().buffer_r();

    AUDIO_INFO audioInfo{};
    SetAudioUniformInfo(audioInfo, req.audio_uni_attr());
    audioInfo.type = AUDIO_PCM;
    audioInfo.audio = iPcm;
    audioInfo.audioSetting.monitorType = AUDIO_MONITOR_STREAM_AND_DEVICE;

    UINT64 audioID = controller->AddAudio(&audioInfo);
    if (controller->FindAudioByID(audioID))
    {
        std::string audio_id = "";
        Util::NumToString(audioID, &audio_id);
        rsp.set_audio_id(audio_id);
    }
    else
    {
        LOG(ERROR) << "[Audio::CreatePCMAudio] audio create failed, audio_id: " << audioID << " audio_type: " << audioInfo.type;
        return false;
    }

    return true;
}

bool Audio::RemoveAudio::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::RemoveAudio] audio not exist, audioID: " << audioID;
        return true;
    }

    controller->DeleteAudio(audioID);
    return true;
}

bool Audio::IsEmpty::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);

    if (!controller->FindAudioByID(audioID))
    {
        rsp.set_empty(true);
        return true;
    }

    rsp.set_empty(false);
    return true;
}

bool Audio::UpdatePCM::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::UpdatePCM] audio not exist, audioID: " << audioID;
        return false;
    }

    PCM_AUDIO iPcm{};
    iPcm.bufLeft = req.buffer().buffer_l();
    iPcm.bufRight = req.buffer().buffer_r();

    AUDIO_CAPTURE caputre{};
    if (req.capture_param().has_audio_format())
    {
        caputre.audioFormat = req.capture_param().audio_format();
    }
    if (req.capture_param().has_bits_per_sec())
    {
        caputre.bitsPerSec = req.capture_param().bits_per_sec();
    }
    if (req.capture_param().has_channel_layout())
    {
        caputre.channelLayout = req.capture_param().channel_layout();
    }
    if (req.capture_param().has_channels())
    {
        caputre.channels = req.capture_param().channels();
    }
    if (req.capture_param().has_frames())
    {
        caputre.frames = req.capture_param().frames();
    }
    if (req.capture_param().has_planes())
    {
        caputre.planes = req.capture_param().planes();
    }
    if (req.capture_param().has_sample_per_sec())
    {
        caputre.samplePerSec = req.capture_param().sample_per_sec();
    }

    AUDIO_INFO audioInfo{};
    controller->GetAudioInfo(audioID, &audioInfo);

    audioInfo.audio = iPcm;
    audioInfo.audioCapture = caputre;

    AUDIO_CONTROL_INFO audioControlInfo{};
    audioControlInfo.audioInfo = audioInfo;
    audioControlInfo.cmd = AUDIO_CONTROL_UPDATE_PCM;
    return controller->ControlAudio(audioID, &audioControlInfo);
}

bool Audio::SetRenderDeviceID::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->SetRenderDeviceID(req.device_id());
    return true;
}

bool Audio::GetPerformance::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::GetPerformance] audio not exist, audioID: " << audioID;
        return false;
    }

    AUDIO_PERFORMANCE_INFO info{};
    controller->GetAudioPerformance(req.audio_id(), &info);
    rsp.set_cost(info.cost);
    rsp.set_valid(info.valid);
    rsp.set_reset_times(info.resetTimes);
    rsp.set_offset(info.offset);

    return true;
}

bool Audio::AddFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::AddFilter] audio not exist, audioID: " << audioID;
        return false;
    }

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[Audio::AddFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    controller->BindFilter(audioID, filterID);
    return true;
}

bool Audio::RemoveFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::RemoveFilter] audio not exist, audioID: " << audioID;
        return false;
    }

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[Audio::RemoveFilter] filter not exist, filterID: " << filterID;
        return true;
    }

    controller->UnBindFilter(audioID, filterID);

    return true;
}

bool Audio::SetAudioUniAttr::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::SetAudioUniAttr] audio not exist, audioID: " << audioID;
        return false;
    }

    AUDIO_INFO audioInfo{};
    controller->GetAudioInfo(audioID, &audioInfo);

    UINT64 cmd = AUDIO_CONTROL_NONE;
    SetAudioUniformInfo(audioInfo, req.audio_uni_attr(), &cmd);

    AUDIO_CONTROL_INFO audioControlInfo{};
    audioControlInfo.audioInfo = audioInfo;
    audioControlInfo.cmd = static_cast<AUDIO_CONTROL_CMD>(cmd);
    if (!controller->ControlAudio(audioID, &audioControlInfo))
        LOG(ERROR) << "[Audio::SetAudioUniAttr] control audio param empty or set property error";

    return true;
}

bool Audio::GetAudioUniAttr::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::GetAudioUniAttr] audio not exist, audioID: " << audioID;
        return false;
    }

    AUDIO_INFO audioInfo{};
    controller->GetAudioInfo(audioID, &audioInfo);

    ls_audio::AudioUniAttr uni_attr{};
    uni_attr.set_type((ls_audio::AUDIO_TYPE)audioInfo.type);
    uni_attr.set_audio_track((ls_audio::AUDIO_TRACK)audioInfo.audioTrack);

    ls_base::DShow device{};
    device.set_id(audioInfo.device.id);
    device.set_name(audioInfo.device.name);
    uni_attr.mutable_device()->CopyFrom(device);

    ls_audio::AudioSettingParam setting_param{};
    setting_param.set_balanceing(audioInfo.audioSetting.balanceing);
    setting_param.set_down_mix_mono(audioInfo.audioSetting.downMixMono);
    setting_param.set_volume(audioInfo.audioSetting.volume);
    setting_param.set_interval(audioInfo.audioSetting.interval);
    setting_param.set_monitor_type((ls_audio::AUDIO_MONITOR_TYPE)audioInfo.audioSetting.monitorType);
    setting_param.set_mute(audioInfo.audioSetting.mute);
    setting_param.set_sync_offset(audioInfo.audioSetting.syncOffset);
    uni_attr.mutable_setting_param()->CopyFrom(setting_param);

    ls_audio::AudioCaptureParam capture_param{};
    capture_param.set_audio_format(audioInfo.audioCapture.audioFormat);
    capture_param.set_bits_per_sec(audioInfo.audioCapture.bitsPerSec);
    capture_param.set_channel_layout(audioInfo.audioCapture.channelLayout);
    capture_param.set_channels(audioInfo.audioCapture.channels);
    capture_param.set_frames(audioInfo.audioCapture.frames);
    capture_param.set_planes(audioInfo.audioCapture.planes);
    capture_param.set_sample_per_sec(audioInfo.audioCapture.samplePerSec);
    uni_attr.mutable_capture_param()->CopyFrom(capture_param);

    rsp.mutable_audio_uni_attr()->CopyFrom(uni_attr);

    return true;
}

bool Audio::SetANSOption::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    if (!controller->SetANSOption(static_cast<AUDIO_ANS_OPTION>(req.ans_option())))
    {
        LOG(ERROR) << "[Audio::SetANSOption] SetANSOption failed, ans_option: " << req.ans_option();
        return false;
    }

    return true;
}

bool Audio::EnableAudioInputEchoDetection::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::EnableAudioInputEchoDetection] audio not exist, audioID: " << audioID;
        return true;
    }

    return controller->EnableAudioInputEchoDetection(audioID, req.interval());
}

bool Audio::EnableAudioInputNoiseDetection::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::EnableAudioInputNoiseDetection] audio not exist, audioID: " << audioID;
        return true;
    }

    return controller->EnableAudioInputNoiseDetection(audioID, req.interval(), req.duration());
}

bool Audio::SetAudioInputRenderDeviceID::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);

    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::SetAudioInputRenderDeviceID] audio not exist, audioID: " << audioID;
        return true;
    }

    return controller->SetAudioInputRenderDeviceID(audioID, req.render_device_id());
}

bool Audio::StartAudioCapture::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (!controller->FindAudioByID(audioID))
    {
        LOG(ERROR) << "[Audio::StartAudioCapture] origin audio isn't exist, audioID: " << req.audio_id();
        return false;
    }

    AUDIO_INFO audioInfo{};
    controller->GetAudioInfo(audioID, &audioInfo);

    CAPTURE_AUDIO_INFO captureAudioInfo{};
    captureAudioInfo.originAudioID = audioID;
    captureAudioInfo.audioTrack = CAPTURE_AUDIO_TRACK_0;
    captureAudioInfo.audioSetting = audioInfo.audioSetting;

    if (req.has_replace_audio_path())
    {
        captureAudioInfo.replacePath = req.replace_audio_path();
    }

    if (req.has_delay_time_ms() && req.delay_time_ms() >= 0)
    {
        captureAudioInfo.bufferTimeMs = req.delay_time_ms();
    }

    if (req.has_enable_write())
    {
        captureAudioInfo.enableWrite = req.enable_write();
    }

    UINT64 captureAudioID = controller->CreateCaptureAudio(&captureAudioInfo);
    if (captureAudioID <= 0)
    {
        LOG(ERROR) << "[Audio::StartAudioCapture] CreateCaptureAudio failed, audioID: " << audioID << ", replaceAudioPath: " << req.replace_audio_path() << ", delay_time_ms: " << req.delay_time_ms();
        return false;
    }

    if (captureAudioInfo.enableWrite)
    {
        if (!controller->ChangeAudioTrack(audioID, CAPTURE_AUDIO_TRACK_0, CAPTURE_AUDIO_TRACK_5))
        {
            LOG(ERROR) << "[Audio::StartAudioCapture] ChangeAudioTrack failed, audioID: " << audioID << ", audioTrack: " << audioInfo.audioTrack;
            return false;
        }
    }

    std::string audio_capture_id = "";
    Util::NumToString(captureAudioID, &audio_capture_id);
    rsp.set_audio_capture_id(audio_capture_id);
    return true;
}

bool Audio::StopAudioCapture::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 captureAudioID = 0;
    Util::StringToNum(req.audio_capture_id(), &captureAudioID);

    CAPTURE_AUDIO_INFO captureAudioInfo{};
    controller->GetCaptureAudioInfo(captureAudioID, &captureAudioInfo);
    if (!controller->DeleteCaptureAudio(captureAudioID))
    {
        LOG(ERROR) << "[Audio::StopAudioCapture] DeleteCaptureAudio failed, captureAudioID: " << captureAudioID << ", audioTrack: " << captureAudioInfo.audioTrack << ", originAudioID: " << captureAudioInfo.originAudioID;
        return false;
    }

    if (captureAudioInfo.enableWrite)
    {
        if (!controller->ChangeAudioTrack(captureAudioInfo.originAudioID, CAPTURE_AUDIO_TRACK_5, CAPTURE_AUDIO_TRACK_0))
            LOG(ERROR) << "[Audio::StopAudioCapture] ChangeAudioTrack failed, originAudioID: " << captureAudioInfo.originAudioID;
    }

    return true;
}

bool Audio::UpdateCapturedAudioID::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 audioCaptureID = 0;
    Util::StringToNum(req.audio_capture_id(), &audioCaptureID);

    if (!controller->FindCaptureAudioByID(audioCaptureID))
    {
        LOG(ERROR) << "[Audio::UpdateCaptureAudioID] capture audio not exist, audioCaptureID: " << audioCaptureID;
        return false;
    }

    CAPTURE_AUDIO_INFO captureAudioInfo{};
    controller->GetCaptureAudioInfo(audioCaptureID, &captureAudioInfo);

    UINT64 audioID = 0;
    Util::StringToNum(req.audio_id(), &audioID);
    if (captureAudioInfo.originAudioID != audioID)
    {
        std::string old_origin_audio_id = "";
        Util::NumToString(captureAudioInfo.originAudioID, &old_origin_audio_id);
        if (captureAudioInfo.originAudioID > 0 && !controller->StopListenAudio(old_origin_audio_id))
        {
            LOG(ERROR) << "[Audio::UpdateCaptureAudioID] StopListenAudio failed, old originAudioID: " << captureAudioInfo.originAudioID;
            return false;
        }

        if (captureAudioInfo.enableWrite)
        {
            if (!controller->ChangeAudioTrack(captureAudioInfo.originAudioID, CAPTURE_AUDIO_TRACK_5, CAPTURE_AUDIO_TRACK_0))
                LOG(ERROR) << "[Audio::UpdateCaptureAudioID] ChangeAudioTrack failed, old originAudioID: " << captureAudioInfo.originAudioID;
        }

        if (audioID > 0)
        {
            if (!controller->StartListenAudio(req.audio_id(), req.audio_capture_id()))
            {
                LOG(ERROR) << "[Audio::UpdateCaptureAudioID] StartListenAudio failed, new audioID: " << audioID;
                return false;
            }

            if (captureAudioInfo.enableWrite)
            {
                if (!controller->ChangeAudioTrack(audioID, CAPTURE_AUDIO_TRACK_0, CAPTURE_AUDIO_TRACK_5))
                {
                    LOG(ERROR) << "[Audio::UpdateCapturedAudioID] ChangeAudioTrack failed, audioID: " << audioID;
                    return false;
                }

                if (!controller->CaptureAudioSetOriginAudio(audioCaptureID, audioID))
                {
                    LOG(ERROR) << "[Audio::UpdateCapturedAudioID] CaptureAudioSetOriginAudio failed, new audioID: " << audioID << ", audioCaptureID: " << audioCaptureID;
                    return false;
                }
            }
        }
    }

    return true;
}

bool Audio::SetAudioReplacePTS::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    bool  hit = false;
    INT64 bufferBeginPTSMs = 0;
    INT64 bufferEndPTSMs = 0;

    UINT64 audioCaptureID = 0;
    Util::StringToNum(req.audio_capture_id(), &audioCaptureID);

    CAPTURE_AUDIO_INFO captureAudioInfo{};
    controller->GetCaptureAudioInfo(audioCaptureID, &captureAudioInfo);

    if (captureAudioInfo.enableWrite)
    {
        if (!controller->CaptureAudioSetReplaceRange(audioCaptureID, req.begin_pts_ms(), req.end_pts_ms(), &hit, &bufferBeginPTSMs, &bufferEndPTSMs, req.enable_dump(), req.dump_tag(), req.dump_filename()))
        {
            LOG(ERROR) << "[Audio::SetAudioReplacePTS] CaptureAudioSetReplaceRange failed, audioCaptureID: " << audioCaptureID;
            return false;
        }
    }

    rsp.set_hit(hit);
    rsp.set_buffer_begin_pts_ms(bufferBeginPTSMs);
    rsp.set_buffer_end_pts_ms(bufferEndPTSMs);
    return true;
}
} // namespace MediaSDK