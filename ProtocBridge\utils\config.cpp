#include "stdafx.h"
#include "config.h"


namespace LS
{
Config::RequestList Config::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Config::Initialize>());
    list.push_back(std::make_unique<Config::PreUnInitialize>());

    list.push_back(std::make_unique<Config::IsForegroundFullScreen>());
    list.push_back(std::make_unique<Config::WindowOverlapped>());
    list.push_back(std::make_unique<Config::GetWindowOverlappedMonitors>());

    list.push_back(std::make_unique<Config::SetDisplayMasks>());

    list.push_back(std::make_unique<Config::EnablePreview>());
    list.push_back(std::make_unique<Config::EnableInteract>());

    list.push_back(std::make_unique<Config::CreateFullScreenDetector>());
    list.push_back(std::make_unique<Config::DestroyFullScreenDetector>());
    list.push_back(std::make_unique<Config::SetFullScreenDetectorIgnoreProcessList>());

    list.push_back(std::make_unique<Config::GetFontFamilies>());
    list.push_back(std::make_unique<Config::GetMediaFileInfo>());

    list.push_back(std::make_unique<Config::GetDeviceInfo>());
    list.push_back(std::make_unique<Config::GetPerformance>());
    list.push_back(std::make_unique<Config::GetCpuInfo>());
    list.push_back(std::make_unique<Config::GetMonitorInfos>());

    list.push_back(std::make_unique<Config::GetSysHAGS>());
    list.push_back(std::make_unique<Config::StartRenderProfiler>());
    list.push_back(std::make_unique<Config::StartCollectPerformanceMatrics>());
    list.push_back(std::make_unique<Config::SetTTNtpMS>());

    list.push_back(std::make_unique<Config::InitEffectPlatform>());
    list.push_back(std::make_unique<Config::UnInitEffectPlatform>());
    list.push_back(std::make_unique<Config::LoadEffectModels>());
    list.push_back(std::make_unique<Config::UpdateEffectConfig>());

    list.push_back(std::make_unique<Config::UpdateDynamicConfig>());
    list.push_back(std::make_unique<Config::CheckEncoderSession>());
    list.push_back(std::make_unique<Config::ResetParfait>());
    list.push_back(std::make_unique<Config::SetParfaitContextInfo>());
    list.push_back(std::make_unique<Config::StartColorPicker>());
    list.push_back(std::make_unique<Config::StartAiIPCServer>());
    list.push_back(std::make_unique<Config::StopAiIPCServer>());
    list.push_back(std::make_unique<Config::ReStartAiIPCServer>());
    return list;
}

bool Config::Initialize::doHandle(const In& req, Out& rsp)
{
    rsp.set_dx_error(0);
    rsp.set_pid(GetCurrentProcessId());

    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    INITIALIZE_INFO info{};
    info.bottomWnd = reinterpret_cast<HWND>(req.hwnd());
    info.hi = reinterpret_cast<HINSTANCE>(req.instance());
    info.fps = req.video_mix().fps();
    info.outputSize.cx = req.video_mix().output_size().x();
    info.outputSize.cy = req.video_mix().output_size().y();
    info.resDir = req.resource_directory().c_str();
    info.workDir = req.work_directory().c_str();
    info.storeDir = req.store_directory().c_str();
    info.version = req.version();
    if (req.has_json_config())
    {
        info.json = req.json_config().c_str();
    }
    if (req.has_hwnd_main())
    {
        info.topWindow = reinterpret_cast<HWND>(req.hwnd_main());
    }
    if (req.has_reporter())
    {
        info.uid = req.reporter().uid();
        info.did = req.reporter().did();
        info.host = req.reporter().host();
    }

    if (req.has_enable_transition())
    {
        info.enableTransition = req.enable_transition();
    }

    if (!controller->Initialize(&info))
    {
        return false;
    }
    
    return true;
}

bool Config::PreUnInitialize::doHandle(const In&, Out&)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

    controller->Quit();
    return true;
}

bool Config::IsForegroundFullScreen::doHandle(const In&, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    bool bFlag = false;
    controller->IsForegroundFullScreen(&bFlag);
	rsp.set_fullscreen(bFlag);
	return true;
}

bool Config::WindowOverlapped::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	bool overlapped = false;
	controller->WindowOverlapped((HWND)req.hwnd(), &overlapped);
	rsp.set_overlapped(overlapped);

	return true;
}

void Config::GetWindowOverlappedMonitors::asyncHandle(std::unique_ptr<AsyncMethodContext> context)
{
    std::thread t([context = std::move(context)]() {
        auto& req = context->requestMessage;
        auto& rsp = context->responseMessage;

        auto controller = PBBridge::GetInstance().GetController();
        if (!controller)
        {
            context->resolve(false);
            return;
        }

        std::vector<std::string> monitorDids{};
        std::string              main_monitor_did;
        std::vector<std::string> all_monitor_did_list;
        controller->GetWindowOverlappedMonitors((HWND)req.hwnd(), &monitorDids, &main_monitor_did, &all_monitor_did_list);

        for (auto did : monitorDids)
        {
            rsp.add_monitor_dids(did);
        }

        rsp.set_main_monitor_did(main_monitor_did);
        for (const auto& did : all_monitor_did_list)
        {
            *rsp.add_all_monitor_did_list() = did;
        }

        context->resolve(true);
    });
    t.detach();
}

bool Config::SetDisplayMasks::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->SetDisplayMasks(req.portrait_path(), req.landscape_path());
    return true;
}

bool Config::EnablePreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
    {
        rsp.set_success(false);
        return true;
    }

    controller->EnableAllPreview(req.enable());
    rsp.set_success(true);
    return true;
}

bool Config::EnableInteract::doHandle(const In& req, Out&)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->EnableInteract(req.enable());
    return true;
}

bool Config::CreateFullScreenDetector::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->CreateFullScreenDetector(req.detector_id());
    return true;
}

bool Config::DestroyFullScreenDetector::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->DestroyFullScreenDetector(req.detector_id());
    return true;
}

bool Config::SetFullScreenDetectorIgnoreProcessList::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<std::string> exeNames{};
    for (int i = 0; i < req.ignore_exe_name_list_size(); ++i)
    {
        exeNames.push_back(req.ignore_exe_name_list(i));
    }
    controller->SetFullScreenDetectorIgnoreProcessList(exeNames);

    return true;
}

bool Config::GetFontFamilies::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<std::string> fonts{};
    controller->GetFontFamilies(&fonts);

    for (const auto font : fonts)
    {
        auto ref = rsp.add_fonts();
        *ref = font;
    }

    return true;
}

bool Config::GetMediaFileInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    auto          path = req.file_path();
    MATERIAL_DESC desc{};
    controller->GetMediaFileInfo(path, &desc);

    ls_base::MaterialParam materialParam{};
    ls_base::SizeF         size{};
    size.set_x(desc.size.Width);
    size.set_y(desc.size.Height);
    materialParam.mutable_size()->CopyFrom(size);
    materialParam.set_angle(desc.angle);
    rsp.mutable_material_param()->CopyFrom(materialParam);

    return true;
}

bool Config::GetDeviceInfo::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	std::vector<VIDEO_ADAPTER_INFO> adapterInfos = controller->GetGPUInfo();
    for (int i = 0; i < adapterInfos.size(); ++i)
	{
        auto& info = adapterInfos[i];
		auto ref = rsp.add_devices();
		ref->set_adapter_model(info.adapterModel);
		ref->set_pci_id(info.pciId);
		ref->set_adapter_luid(info.adapterLuid);
		ref->set_gpu_driver_version(info.gpuDriverVersion);
		ref->set_dedicated_memory_size(info.dedicatedMemorySize);
		ref->set_shared_memory_size(info.sharedMemorySize);
		ref->set_gpu_3d_total(info.gpu3DTotal);
		ref->set_gpu_3d_usage(info.gpu3DUsage);
		ref->set_gpu_dedicated_memory_total(info.gpuDedicatedMemoryTotal);
		ref->set_gpu_dedicated_memory_usage(info.gpuDedicatedMemoryUsage);
        ref->set_is_encode_active(i == 0);
	}

	return true;
}

bool Config::GetPerformance::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

	float cpuUsage = .0f, cpuGlobalUtil = .0f, cpuGlobalUsage = .0f;
	controller->GetCPUUsage(&cpuUsage, &cpuGlobalUtil, &cpuGlobalUsage);

	float sdkMemorySize = .0f, memoryUsage = .0f, pageFault = .0f, commitMemoryUsage = .0f;
	UINT64 totalMemory = 0;
    controller->GetMemUsage(&sdkMemorySize, &memoryUsage, &totalMemory, &pageFault, &commitMemoryUsage);

    float activeFPS = .0f;
    controller->GetActiveFPS(&activeFPS);

    float notReadyFPS = .0f;
    controller->GetNotReadyFPS(&notReadyFPS);

	ls_config::PerformanceInfo info{};
    info.set_active_fps(activeFPS);
	info.set_cpu_usage(cpuUsage);
	info.set_cpu_global_utilization(cpuGlobalUtil);
	info.set_cpu_global_usage(cpuGlobalUsage);
	info.set_sdk_memory_usage(sdkMemorySize);
    info.set_commit_memory_usage(commitMemoryUsage);
	info.set_memory_usage(memoryUsage);
	info.set_total_memory(totalMemory);
    info.set_page_fault(pageFault);
    info.set_present_ready_fps(activeFPS - notReadyFPS);
	rsp.mutable_info()->CopyFrom(info);

	return true;
}

bool Config::GetCpuInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    auto cpuInfo = controller->GetCpuInfo();
    rsp.set_name(WStringToUTF8(cpuInfo.processorName));
    rsp.set_num(cpuInfo.processNum);
    rsp.set_clock_speed(cpuInfo.maxClockSpeed);
    rsp.set_core_num(cpuInfo.coreNum);
    rsp.set_family(cpuInfo.family);
    rsp.set_model(cpuInfo.model);
    rsp.set_stepping(cpuInfo.stepping);
    return true;
}

bool Config::GetMonitorInfos::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    auto monitorInfos = controller->GetMonitorsInfo();
    auto hMonitor = controller->GetCurrentMonitor();
    for (auto it : monitorInfos)
    {
        auto monitor = rsp.add_monitors();
        monitor->set_gpu_index(it.gpuIndex);
        monitor->set_left(it.left);
        monitor->set_top(it.top);
        monitor->set_right(it.right);
        monitor->set_bottom(it.bottom);
        monitor->set_refresh(it.refresh);
        monitor->set_phys_width(it.physWidth);
        monitor->set_phys_height(it.physHeight);
        if (it.monitor == hMonitor)
            monitor->set_is_current(true);
        else
            monitor->set_is_current(false);
        monitor->set_is_hdr(it.isHDR);
    }
    return true;
}

bool Config::GetSysHAGS::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT32 val = controller->GetHAGS();
    rsp.set_val(val);
    return true;
}

bool Config::StartRenderProfiler::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->StartRenderProfiler();
    return true;
}

bool Config::StartCollectPerformanceMatrics::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<PERFORMANCE_MATRICS> matrics{};
    for (int i = 0; i < req.param_size(); ++i)
    {
        PERFORMANCE_MATRICS matric{};
        matric.name = req.param(i).name();
        matric.threadshold = req.param(i).threadshold();
        matrics.push_back(matric);
    }
    controller->StartCollectPerformanceMatrics(matrics);
    return true;
}

bool Config::SetTTNtpMS::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->SetTTNtpMS(req.ntp_ms(), req.local_ms());
    return true;
}

bool Config::InitEffectPlatform::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    INIT_EFFECT_PLATFORM initEffectPlatform{};
    initEffectPlatform.accessKey = req.access_key();
    initEffectPlatform.appID = req.app_id();
    initEffectPlatform.appVersion = req.app_version();
    initEffectPlatform.builtInModelDir = req.built_in_model_dir();
    initEffectPlatform.channel = req.channel();
    initEffectPlatform.deviceID = req.device_id();
    initEffectPlatform.userID = req.user_id();
    initEffectPlatform.deviceType = req.device_type();
    initEffectPlatform.effectCacheDir = req.effect_cache_dir();
    initEffectPlatform.lokiHost = req.loki_host();
    initEffectPlatform.modelCacheDir = req.model_cache_dir();
    initEffectPlatform.modelStatus = req.model_status();
    initEffectPlatform.region = req.region();
    initEffectPlatform.ttlsHardwareLevel = req.ttls_hardware_level();
    initEffectPlatform.veCloudHost = req.ve_cloud_host();
    initEffectPlatform.timeOut = req.time_out();
    controller->InitEffectPlatform(initEffectPlatform);

    return true;
}

bool Config::UnInitEffectPlatform::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->UnInitEffectPlatform();
    return true;
}

bool Config::LoadEffectModels::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::vector<std::string> requirements{};
    for (int i = 0; i < req.requirement_size(); ++i)
    {
        requirements.push_back(req.requirement(i));
    }

    controller->LoadEffectModels(req.request_id(), req.model_name(), requirements);
    return true;
}

bool Config::UpdateEffectConfig::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->UpdateEffectConfig(req.user_id(), req.ttls_hardware_level());
    return true;
}

bool Config::UpdateDynamicConfig::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    std::string json_config = "";
    if (req.has_json_config())
    {
        json_config = req.json_config();
    }

    bool enable_transition = false;
    if (req.has_enable_transition())
    {
        enable_transition = req.enable_transition();
    }

    controller->UpdateDynamicConfig(json_config, enable_transition);
    return true;
}

bool Config::CheckEncoderSession::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

    INT32 result = 0;
	controller->CheckEncoderSession(req.encoder_name(), req.count(), &result);
    rsp.set_result(result);
	return true;
}

bool Config::ResetParfait::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    PARFAIT_PARAM param{};
	Util::StringToNum(req.aid().c_str(), &param.aid);
	param.dllPath = StringToWString(req.dll_root_path(), CP_UTF8);
	param.did = req.did();
	param.uid = req.uid();
	param.url = req.url();
	param.host = req.host();
	param.osversion = req.os_version();
	std::wstring            logDir(StringToWString(req.log_file(), CP_UTF8).c_str());
	std::wstring::size_type backslash = logDir.rfind(L'\\', logDir.size());
	if (backslash == std::wstring::npos)
	{
		backslash = logDir.rfind(L'/', logDir.size());
	}
	if (backslash != wstring::npos && backslash + 1 != wstring::npos)
	{
		param.prefix = WStringToString(logDir.substr(backslash + 1));
		wstring::size_type dotIndex = param.prefix.rfind(L'.', param.prefix.size());
		if (dotIndex != string::npos)
		{
			param.prefix.erase(dotIndex);
		}
        logDir.erase(backslash + 1);
	}
    logDir += L"parfaitlog\\meidasdk_server\\";
	param.rootPathName = WStringToUTF8(logDir);
	param.overseas = true;
    if (req.has_report_interval())
    {
        param.reportInterval = req.report_interval();
    }
    else
    {
        param.reportInterval = 30000;
    }

    bool reset = controller->ResetParfait(param);
    if (reset)
    {
        LOG(ERROR) << "[Config::ResetParfait] ResetParfait failed";
        return false;
    }
    return true;
}

bool Config::SetParfaitContextInfo::doHandle(const In& req, Out& rsp)
{
	auto controller = PBBridge::GetInstance().GetController();
	if (!controller)
		return false;

    controller->SetParfaitContextInfo(req.key(), req.value());
	return true;
}

bool Config::StartColorPicker::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    HWND hwnd = NULL;
    if (req.has_hwnd())
    {
        hwnd = (HWND)req.hwnd();
    }
    controller->StartColorPicker(hwnd);

    return true;
}

bool Config::StartAiIPCServer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->StartAiIpc();
    return true;
}

bool Config::StopAiIPCServer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;
        
    controller->StopAiIpc();
    return true;
}

bool Config::ReStartAiIPCServer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;
    controller->StopAiIpc();
    controller->StartAiIpc();

    return true;
}

} // namespace MediaSDK