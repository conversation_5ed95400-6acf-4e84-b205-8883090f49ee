#pragma once
#include "LSPublicHeader.h"
#include "mediasdk_v2_header/plugins/bytelink_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/fav_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/desktop_capture_visual_source_helper.h"
#include "librtc/rtc/bytertc_rts_defines.h"
#include "librtc/rtc/bytertc_video_defines.h"
#include "mediasdk_v2_header/mediasdk_defines_rtc.h"
#include "mediasdk_v2_header/mediasdk_defines_canvas.h"

AUDIO_DATA_FLOW MapAudioDataFlow2Native(EDataFlow in, bool* success);

AUDIO_DEVICE_ROLE MapAudioDeviceRole2Native(ERole in, bool* success);

AUDIO_DEVICE_STATE MapAudioDeviceState2Native(DWORD state, bool* success);

DEVICE_TRANSPORT_TYPE MapDeviceTransportType2Native(int in, bool* success);

CASTMATE_EVENT_TYPE MapMobileProjectorEvent2Native(const bytelink_visual_source::CastMateEvent in, bool* success);

CASTMATE_PROTOCOL_TYPE MapMobileProjectorProtocol2Native(const bytelink_visual_source::CastmateProtocolType in, bool* success);

FAV_EVENT_TYPE MapMediaSourceEvent2Native(const fav_visual_source::FAVEventType in, bool* success);

COLOR_RANGE MapColorRange2Native(mediasdk::VideoRange in, bool* success);

STREAM_TYPE MapStreamType2Native(const std::string& t, bool* success);

REMOTE_LEAVE_REASON MapRemoteLeaveReason2Native(bytertc::UserOfflineReason in, bool* success);

STREAM_MIXING_EVENT MapStreamMixingEvent2Native(int in, bool* success);

AUDIO_MONITOR_TYPE MapAudioDeviceMonitorType2Native(const int32_t in, bool* success);

VIDEO_PIXEL_FORMAT MapVideoFormat2Native(mediasdk::PixelFormat in, bool* success);

GRAFFITI_TYPE MapGraffitiType2Native(const std::string in);

CURSOR_HIT_POSITION MapCursorHitPosition2Native(mediasdk::HittestCursorPos in, bool* success);

SHORT_CUT_ACTION MapShortCutAction2Native(mediasdk::ShortcutAction in, bool* success);

VISUAL_CAPTURE_TYPE MapWindowCaptureMethod2Native(desktop_capture_visual_source::WindowCaptureMethod in, bool* success);

GAME_CAPTURE_EVENT_TYPE MapGameCaptureEventType2Native(mediasdk::GameCaptureEventType in, bool* success);

CAMERA_CONTROL_TYPE MapCameraControlType2Native(int in, bool* success);

int MapCameraControlType2SDK(CAMERA_CONTROL_TYPE in, bool* success);

VIDEO_PROCAMP_TYPE MapVideoProcAmpType2Native(int in, bool* success);

int MapVideoProcAmpType2SDK(VIDEO_PROCAMP_TYPE in, bool* success);

std::string MapEncoderName2Native(std::string in);

int32_t MapAudioDeviceMonitorType2SDK(const AUDIO_MONITOR_TYPE in, bool* success);

mediasdk::ColorSpace MapColorSpace2SDK(COLOR_SPACE in, bool* success);

mediasdk::VideoRange MapColorRange2SDK(COLOR_RANGE in, bool* success);

mediasdk::PixelFormat MapVideoFormat2SDK(VIDEO_PIXEL_FORMAT in, bool* success);

bytelink_visual_source::CastMateOptionType MapCastMateOptionType2SDK(const CASTMATE_OPTION_TYPE in, bool* success);

bytelink_visual_source::CastmateProtocolType MapCastmateProtocolType2SDK(const CASTMATE_PROTOCOL_TYPE in, bool* success);

bytertc::MediaStreamType MapRTCControllerMediaStreamType2SDK(MEDIA_STREAM_TYPE type, bool* success);

mediasdk::VirtualCameraObjectFitMode MapObjectFitMode2SDK(OBJECT_FIT_MODE mode, bool* success);

mediasdk::TransitionType MapTransitionType2SDK(TRANSITION_TYPE type, bool* success);

mediasdk::TransitionProgressFunctionType MapTransitionProgressFunctionType2SDK(TRANSITION_PROGRESS_FUNCTION_TYPE type, bool* success);

mediasdk::TransitionDirection MapTransitionDirection2SDK(TRANSITION_DIRECTION direction, bool* success);

mediasdk::TransitionMoveType MapTransitionMoveType2SDK(TRANSITION_MOVE_TYPE type, bool* success);
mediasdk::CanvasSelectionBorderRenderType MapBorderRenderType2SDK(CANVAS_SELECTION_BORDER_TYPE type, bool* success);


int MapANSOption2SDK(const AUDIO_ANS_OPTION in, bool* success);

std::string MapStreamType2SDK(const STREAM_TYPE& t);

std::string MapGraffitiType2SDK(const GRAFFITI_TYPE in);

std::string MapAudioAACProfile2SDK(const PROFILE_AAC in, bool* success);

std::string MapEncoderName2SDK(std::string in);

// Not Enum Map
mediasdk::MSTransform MapTransform2SDK(TRANSFORM transform);

TRANSFORM MapTransform2Native(mediasdk::MSTransform transform);