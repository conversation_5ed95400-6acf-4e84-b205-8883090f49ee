﻿#include "ModeSceneMgr.h"
#include "pathutil.h"
#include "stringutil.h"
#include <unordered_map>
#include "AudioMgr.h"

#define MAKELSID(type,instance) (((type)<<32)|(instance))

const char* LAYER_LAYOUT_NAME[] = {
    "LAYOUT_NONE",
    "LAYOUT_CENTERED",
	"LAYOUT_COVER_RATIO_FILL",
	"LAYOUT_CONTAIN_RATIO_FILL",
	"LAYOUT_TILE_FILL",
};

const char* LAYER_FIXED_EDGE_NAME[] = {
    "FIXED_EDGE_NONE",
	"FIXED_EDGE_WIDTH",
	"FIXED_EDGE_HEIGHT",
	"FIXED_EDGE_LONGEST",
	"FIXED_EDGE_SHORTEST",
};

ModeSceneMgr g_modeSceneMgr;

ModeSceneMgr* ModeSceneMgr::GetInstance()
{
	return &g_modeSceneMgr;
}

ModeSceneMgr::ModeSceneMgr()
{
	m_modeScene[0] = new Mode(LIVE_MODE_LANDSCAPE);
	m_modeScene[1] = new Mode(LIVE_MODE_PORTRAIT);
	m_modeScene[2] = new Mode(LIVE_MODE_DBCANVAS);

	m_modeInfos[LIVE_MODE_LANDSCAPE].id = LIVE_MODE_LANDSCAPE;
	m_modeInfos[LIVE_MODE_PORTRAIT].id = LIVE_MODE_PORTRAIT;
	m_modeInfos[LIVE_MODE_DBCANVAS].id = LIVE_MODE_DBCANVAS;
}

ModeSceneMgr::~ModeSceneMgr(){}

UINT64 ModeSceneMgr::CreateScene(SCENE_INFO* sceneInfo, const UINT64* sceneID, const LIVE_MODE* mode)
{
	Scene* scene = 0;
	if (mode)
	{
		if (*mode == LIVE_MODE_LANDSCAPE)
			scene = new SceneLandscape;
		else if (*mode == LIVE_MODE_PORTRAIT)
			scene = new ScenePortrait;
		else if (*mode == LIVE_MODE_DBCANVAS)
			scene = new SceneDBCanvas;
	}
	if (!scene)
		scene = new Scene;

	if (sceneInfo)
		scene->SetSceneInfo(sceneInfo);
	UINT64 id = sceneID ? *sceneID : AllocSceneID();
	scene->SetSceneID(id);
	scene->InitializeCanvas();

	BindObject(id, scene, OBJECT_SCENE);
	return id;
}

void ModeSceneMgr::BindModeScene(LIVE_MODE mode, UINT64 sceneID)
{
	Mode* pMode = GetModeByID(mode);
	if (!pMode)
		return;
	Scene* pScene = GetSceneByID(sceneID);
	if (!pScene)
		return;
	pMode->AddScene(pScene);
}

void ModeSceneMgr::DestroyScene(UINT64 sceneID)
{
	Scene* pScene = GetSceneByID(sceneID);
	if (pScene)
	{
		SCENE_INFO_EX sceneInfo = {};
		pScene->GetSceneInfo(&sceneInfo);
		Mode* pMode = GetModeByID(sceneInfo.mode);
		if (pMode)
			pMode->RemoveScene(pScene);

		UnBindObject(sceneID, OBJECT_SCENE);
		delete pScene;
	}
}

UINT32 ModeSceneMgr::AddPreview(PREVIEW_INFO* info, const UINT32* id)
{
    Preview* pView = new Preview;
    PREVIEW_INFO& tmpInfo = *info;
    tmpInfo.id = id ? *id : AllocPreviewID();
    pView->SetPreviewInfo(&tmpInfo);
    BindObject(tmpInfo.id, pView, OBJECT_PREVIEW);
    LOG(INFO) << "[ModeSceneMgr::AddPreview] videoModel: " << info->videoModel << ", previewID: " << tmpInfo.id;
    return tmpInfo.id;
}

void ModeSceneMgr::RemovePreview(UINT32 previewID)
{
    LOG(INFO) << "[ModeSceneMgr::RemovePreview] previewID: " << previewID;
	if (Preview* pView = GetPreviewByID(previewID))
	{
        UnBindObject(previewID, OBJECT_PREVIEW);
        delete pView;
	}
}

void ModeSceneMgr::DestroyPreview(UINT32 previewID)
{
    LOG(INFO) << "[ModeSceneMgr::DestroyPreview] previewID: " << previewID;
	if (Preview* pView = GetPreviewByID(previewID))
	{
		if (!pView->Destroy())
		{
            LOG(ERROR) << "[ModeSceneMgr::DestroyPreview] Destroy Preview failed, previewID: " << previewID;
            return;
		}

		UnBindObject(previewID, OBJECT_PREVIEW);
        delete pView;
	}
}

void ModeSceneMgr::GetPreviewInfoByID(UINT32 previewID, PREVIEW_INFO* info)
{
	if (Preview* pView = GetPreviewByID(previewID))
	{
        pView->GetPreviewInfo(info);
	}
}

UINT64 ModeSceneMgr::AddCanvas(CANVAS_INFO* info, const UINT64* id)
{
	Canvas* canvas = new Canvas;
	CANVAS_INFO& tmpInfo = *info;
	tmpInfo.id = id ? *id : AllocCanvasID();
	canvas->SetCanvasInfo(&tmpInfo);
	BindObject(tmpInfo.id, canvas, OBJECT_CANVAS);
    LOG(INFO) << "[ModeSceneMgr::AddCanvas] canvasID: " << tmpInfo.id;
	return tmpInfo.id;
}

bool ModeSceneMgr::DestroyCanvas(UINT64 canvasID)
{
    LOG(INFO) << "[ModeSceneMgr::DestroyCanvas] canvasID: " << canvasID;
	if (Canvas* pCanvas = GetCanvasByID(canvasID))
	{
        CANVAS_INFO_EX canvasInfo;
        pCanvas->GetCanvasInfo(&canvasInfo);

		bool success = false;
		if (Scene* pScene = GetSceneByID(canvasInfo.sceneID))
		{
            success = pScene->DestroyCanvas(pCanvas);
		}
		else
		{
            success = pCanvas->Destroy();
		}

		// Cleanup Preview CanvasIDs Array
		if (Preview* pView = GetPreviewByID(canvasInfo.previewID))
		{
            if (!pView->UnbindCanvas(canvasID))
                LOG(ERROR) << "[ModeSceneMgr::DestroyCanvas] Unbind Canvas failed, canvasID: " << canvasID << ", previewID: " << canvasInfo.previewID;
		}

		if (success)
		{
            UnBindObject(canvasID, OBJECT_CANVAS);
            delete pCanvas;
		}

		return success;
	}

	return false;
}

bool ModeSceneMgr::BindPreviewCanvas(UINT32 previewID, UINT64 canvasID)
{
    Preview* pView = GetPreviewByID(previewID);
    if (!pView)
        return false;

    return pView->BindCanvas(canvasID);
}

void ModeSceneMgr::GetCanvasInfoByID(UINT64 canvasID, CANVAS_INFO_EX* infoEx)
{
    Canvas* pCanvas = GetCanvasByID(canvasID);
	if (pCanvas)
	{
        pCanvas->GetCanvasInfo(infoEx);
	}
}

UINT64 ModeSceneMgr::AddLayer(LAYER_INFO* layerInfo, const UINT64* layerID)
{
	Layer* pLayer = new Layer;
	LAYER_INFO& info = *layerInfo;
	info.id = layerID ? *layerID : AllocLayerID();
	info.canvasID = 0;

	pLayer->SetLayerInfo(&info);
	BindObject(info.id, pLayer, OBJECT_LAYER);
	return info.id;
}

bool ModeSceneMgr::BindCanvasLayer(UINT64 canvasID, UINT64 layerID)
{
	Canvas* pCanvas = GetCanvasByID(canvasID);
	if (!pCanvas)
		return false;
	Layer* pLayer = GetLayerByID(layerID);
	if (!pLayer)
		return false;

	return pCanvas->CreateLayer(pLayer, true);
}

void ModeSceneMgr::DestroyLayer(UINT64 layerID)
{
	Layer* pLayer = GetLayerByID(layerID);
	if (pLayer)
	{
		//1,Check remove from parent
		LAYER_INFO layerInfo = {};
		pLayer->GetLayerInfo(&layerInfo);
		Canvas* pCanvas = GetCanvasByID(layerInfo.canvasID);
		if (pCanvas)
			pCanvas->DestroyLayer(pLayer);
		//2,Unbind from object pool
		UnBindObject(layerInfo.id, OBJECT_LAYER);
		//3,Release
		delete pLayer;
	}
}

void ModeSceneMgr::HandleLayerSizeChange(UINT64 layerID, float width, float height)
{
    Layer* pLayer = GetLayerByID(layerID);
	if (pLayer)
	{
        LAYER_INFO layerInfo{};
        pLayer->GetLayerInfo(&layerInfo);
        if (width > EPS && height > EPS)
        {
            VisualReadyEvent event{};
            std::string      layer_id = "";
            Util::NumToString(layerID, &layer_id);
            event.visualID = layer_id;
            event.size.Width = width;
            event.size.Height = height;
            eventbus::EventBus::PostEvent(event);

            pLayer->ReCalLayerTransform(width, height);
        }
	}
}

bool ModeSceneMgr::SelectMode(LIVE_MODE mode)
{
	if (mode >= LIVE_MODE_MAX)
		return false;
	if (mode == m_curMode)
		return true;
	
	if (m_curMode < LIVE_MODE_MAX)
		m_modeScene[m_curMode]->Select(false);
	m_curMode = mode;
	m_modeScene[mode]->Select(true);
	return true;
}

Mode* ModeSceneMgr::GetCurrentMode()
{
	return GetModeByID(m_curMode);
}

Scene* ModeSceneMgr::GetCurrentScene()
{
	Mode* pMode = GetCurrentMode();
	if (pMode)
		return pMode->GetCurrentScene();
	return 0;
}

bool ModeSceneMgr::IsCurrentScene(UINT64 sceneID)
{
	Scene* pScene = GetSceneByID(sceneID);
	if (pScene && pScene == GetCurrentScene())
		return true;
	return false;
}

UINT64 ModeSceneMgr::AllocSceneID()
{
	while (1)
	{
		UINT sceneCounter = ::InterlockedIncrement(&m_sceneCounter);
		if (sceneCounter == 0 || sceneCounter == 0xffffffff)
			sceneCounter = ::InterlockedIncrement(&m_sceneCounter);
		UINT64 sceneID = MAKELSID((UINT64)OBJECTTYPE_SCENE, sceneCounter);
		std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_SCENE].find(sceneID);
		if (it == m_bindObjects[OBJECT_SCENE].end())
			return sceneID;
	}
	return -1;
}

UINT64 ModeSceneMgr::AllocCanvasID()
{
	while (1)
	{
		USHORT canvasCounter = ::InterlockedIncrement(&m_canvasCounter);
		if (canvasCounter == 0 || canvasCounter == 0xffffffff)
			canvasCounter = ::InterlockedIncrement(&m_canvasCounter);
		UINT64 canvasID = MAKELSID((UINT64)OBJECTTYPE_CANVAS, canvasCounter);
		std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_CANVAS].find(canvasID);
		if (it == m_bindObjects[OBJECT_CANVAS].end())
			return canvasID;
	}
	return -1;
}

UINT64 ModeSceneMgr::AllocLayerID()
{
	while (1)
	{
		UINT layerCounter = ::InterlockedIncrement(&m_layerCounter);
		if (layerCounter == 0 || layerCounter == 0xffffffff)
			layerCounter = ::InterlockedIncrement(&m_layerCounter);
		UINT64 layerID = MAKELSID((UINT64)OBJECTTYPE_LAYER, layerCounter);
		std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_LAYER].find(layerID);
		if (it == m_bindObjects[OBJECT_LAYER].end())
			return layerID;
	}
	return 0;
}

UINT64 ModeSceneMgr::AllocPreviewID()
{
	while (1)
	{
		UINT previewCounter = ::InterlockedIncrement(&m_previewCounter);
		if (previewCounter == 0 || previewCounter == 0xffffffff)
			previewCounter = ::InterlockedIncrement(&m_previewCounter);
		UINT64 previewID = MAKELSID((UINT64)OBJECTTYPE_PREVIEW, previewCounter);
		std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECTTYPE_PREVIEW].find(previewID);
		if (it == m_bindObjects[OBJECTTYPE_PREVIEW].end())
			return previewID;
	}
	return -1;
}

UINT64 ModeSceneMgr::AllocFrameID()
{
    while (1)
    {
        UINT frameCounter = ::InterlockedIncrement(&m_frameCounter);
        if (frameCounter == 0 || frameCounter == 0xffffffff)
            frameCounter = ::InterlockedIncrement(&m_frameCounter);
        UINT64                            frameID = MAKELSID((UINT64)OBJECTTYPE_FRAME, frameCounter);
        std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECTTYPE_FRAME].find(frameID);
        if (it == m_bindObjects[OBJECTTYPE_FRAME].end())
            return frameID;
    }
    return -1;
}

void ModeSceneMgr::BindObject(UINT64 id, void* object, ENUMOBJECT bo)
{
	m_bindObjects[bo].insert(std::pair<UINT64, void*>(id, object));
}

void ModeSceneMgr::UnBindObject(UINT64 id, ENUMOBJECT bo)
{
	m_bindObjects[bo].erase(id);
}

Mode* ModeSceneMgr::GetModeByID(UINT64 id)
{
	if (id >= LIVE_MODE_MAX)
		return 0;
	return m_modeScene[id];
}

Scene* ModeSceneMgr::GetSceneByID(UINT64 id)
{
	std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_SCENE].find(id);
	if (it != m_bindObjects[OBJECT_SCENE].end())
		return (Scene*)it->second;
	return 0;
}

Preview* ModeSceneMgr::GetPreviewByID(UINT64 id)
{
    std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_PREVIEW].find(id);
    if (it != m_bindObjects[OBJECT_PREVIEW].end())
        return (Preview*)it->second;
    return 0;
}

Canvas* ModeSceneMgr::GetCanvasByID(UINT64 id)
{
	std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_CANVAS].find(id);
	if (it != m_bindObjects[OBJECT_CANVAS].end())
		return (Canvas*)it->second;
	return 0;
}

Layer* ModeSceneMgr::GetLayerByID(UINT64 id)
{
	std::map<UINT64, void*>::iterator it = m_bindObjects[OBJECT_LAYER].find(id);
	if (it != m_bindObjects[OBJECT_LAYER].end())
		return (Layer*)it->second;
	return 0;
}

void ModeSceneMgr::GetLayerInfoByID(UINT64 id, LAYER_INFO* info)
{
	Layer* pLayer = GetLayerByID(id);
	if (pLayer)
	{
		pLayer->GetLayerInfo(info);
	}
}

bool ModeSceneMgr::CheckLayerExist(UINT64 id)
{
	Layer* pLayer = GetLayerByID(id);
	if (pLayer)
	{
		LAYER_INFO layerInfo{};
		pLayer->GetLayerInfo(&layerInfo);
		if (layerInfo.isCreated)
			return true;
	}
	return false;
}

Preview* ModeSceneMgr::GetPreviewByVideoModel(UINT32 videoModel, UINT32* previewID /* = nullptr */)
{
	for (const auto it : m_bindObjects[OBJECT_PREVIEW])
	{
		if (Preview* pView = (Preview*)it.second)
		{
            PREVIEW_INFO previewInfo;
            pView->GetPreviewInfo(&previewInfo);
            if (previewInfo.videoModel == videoModel)
            {
                if (previewID)
                    *previewID = previewInfo.id;

                return pView;
            }
		}
	}
	return nullptr;
}

UINT32 ModeSceneMgr::GetVideoModelByPreviewID(UINT64 previewID)
{
	if (Preview* pView = GetPreviewByID(previewID))
	{
        PREVIEW_INFO previewInfo;
        pView->GetPreviewInfo(&previewInfo);
        previewInfo.videoModel;
	}
	return UINT32_MAX;
}

UINT32 ModeSceneMgr::GetPreviewIDByCanvasID(UINT64 canvasID)
{
	if (Canvas* pCanvas = GetCanvasByID(canvasID))
	{
        CANVAS_INFO_EX canvasInfo;
        pCanvas->GetCanvasInfo(&canvasInfo);
        return canvasInfo.previewID;
	}
    return 0;
}

UINT64 ModeSceneMgr::GetCurCanvasIDByPreviewID(UINT32 previewID)
{
	if (Preview* pView = GetPreviewByID(previewID))
	{
        PREVIEW_INFO previewInfo;
        pView->GetPreviewInfo(&previewInfo);
        return previewInfo.curCanvasID;
	}
    return 0;
}

UINT32 ModeSceneMgr::GetVideoModelByOldCanvasIdx(UINT32 canvasIdx, LIVE_MODE mode /*= LIVE_MODE_MAX*/)
{
    Mode* pMode = mode == LIVE_MODE_MAX ? GetCurrentMode() : GetModeByID(static_cast<UINT64>(mode));
	if (pMode)
	{
		return pMode->GetVideoModel(canvasIdx);
	}
	return UINT32_MAX;
}

UINT32 ModeSceneMgr::GetOldCanvasIdxByModel(UINT32 videoModel)
{
	Mode* pMode = GetCurrentMode();
	if (pMode)
	{
		MODE_INFO_EX modeInfoEx{};
		pMode->GetModeInfo(&modeInfoEx);
		if (modeInfoEx.id == LIVE_MODE_LANDSCAPE || modeInfoEx.id == LIVE_MODE_PORTRAIT)
		{
			return 0;
		}
		else if (modeInfoEx.id == LIVE_MODE_DBCANVAS)
		{
			return videoModel - 2;
		}
	}
	return UINT32_MAX;
}

UINT32 ModeSceneMgr::GetOldCanvasIdxByPreviewID(UINT32 previewID)
{
    PREVIEW_INFO previewInfo;
    ModeSceneMgr::GetInstance()->GetPreviewInfoByID(previewID, &previewInfo);
	if (!previewInfo.canvasIDs.empty())
	{
        CANVAS_INFO_EX canvasInfoEx;
        GetCanvasInfoByID(previewInfo.canvasIDs[0], &canvasInfoEx);
        return canvasInfoEx.oldCanvasIdx;
	}
    return UINT32_MAX;
}

void ModeSceneMgr::OnBrowserSourceUpdate(LAYER_INFO& layerInfo, SOURCE_INFO* sourceInfo)
{
    Canvas* canvas = GetCanvasByID(layerInfo.canvasID);
    if (canvas)
        canvas->SyncLayerSource(layerInfo, sourceInfo);
}
