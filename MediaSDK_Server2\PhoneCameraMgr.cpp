﻿#include "PhoneCameraMgr.h"
#include "LSPublicHeader.h"

void CastmateLogCallback(int level, const void* priv, const char* log)
{
    switch (level)
    {
    case 0:  // LOG_ERROR
        LOG(ERROR) << "[castmate]" << log;
        break;
    case 1: // LOG_WARNING
        LOG(WARNING) << "[castmate]" << log;
        break;
    case 2: // LOG_INFO
        LOG(INFO) << "[castmate]" << log;
        break;
    }
}

static CASTMATE_PROTOCOL_TYPE MapToCastmateProtocolType(CastMateProtocolType type)
{
    CASTMATE_PROTOCOL_TYPE ret = NOT_REACHED;
    switch (type)
    {
    case TYPE_WIRELESS_ANDROID:
        ret = WIRELESS_ANDROID;
        break;
    case TYPE_WIRELESS_IOS:
        ret = WIRELESS_IOS;
        break;
    case TYPE_WIRED_ANDROID:
        ret = WIRED_ANDROID;
        break;
    case TYPE_WIRED_IOS:
        ret = WIRED_IOS;
        break;
    case TYPE_WIRELESS_CAMERA:
        ret = WIRELESS_CAMERA;
        break;
    case TYPE_WIRED_ANDROID_CAMERA:
        ret = WIRED_ANDROID_CAMERA;
        break;
    case TYPE_WIRED_IOS_CAMERA:
        ret = WIRED_IOS_CAMERA;
        break;
    case TYPE_NOT_REACHED:
        ret = NOT_REACHED;
        break;
    default:
        break;

    }
    return ret;
}

PhoneCameraMgr::PhoneCameraMgr()
{
    callbacks_.cls = this;
    callbacks_.Auth_Handler = PhoneCameraMgr::Auth_Handler_s;
    callbacks_.Audio_Open = PhoneCameraMgr::Audio_Open_s;
    callbacks_.Audio_Process = PhoneCameraMgr::Audio_Process_s;
    callbacks_.Audio_Close = PhoneCameraMgr::Audio_Close_s;
    callbacks_.Video_Open = PhoneCameraMgr::Video_Open_s;
    callbacks_.Video_Process = PhoneCameraMgr::Video_Process_s;
    callbacks_.Video_Close = PhoneCameraMgr::Video_Close_s;
    callbacks_.OnConnect = PhoneCameraMgr::OnConnect_s;
    callbacks_.OnDisconnect = PhoneCameraMgr::OnDisconnect_s;
    callbacks_.OnError = PhoneCameraMgr::OnError_s;
    callbacks_.OnEvent = PhoneCameraMgr::OnEvent_s;
    callbacks_.OnMirrorPort = PhoneCameraMgr::OnMirrorPort_s;
    callbacks_.Video_Output = PhoneCameraMgr::Video_Output_s;
    callbacks_.OnRecvMetaData = PhoneCameraMgr::OnRecvMetaData_s;

    listener_.cls = this;
    listener_.OnBrowseError = PhoneCameraMgr::OnBrowseError_s;
    listener_.OnBrowseEvent = PhoneCameraMgr::OnBrowseEvent_s;
    listener_.OnInviteError = PhoneCameraMgr::OnInviteError_s;
    listener_.OnInviteResult = PhoneCameraMgr::OnInviteResult_s;
    

    CastMateMirror_SetLogCallback(CastmateLogCallback, nullptr);
}

PhoneCameraMgr::~PhoneCameraMgr()
{
    CastMateMirror_UnBindSdk();
}

void PhoneCameraMgr::SetUidDid(string uid, string did)
{
    uid_ = uid;
    did_ = did;
}

void* PhoneCameraMgr::GetCurrentHanle()
{
    void* handle = nullptr;
    if (connectingType == TYPE_WIRELESS_CAMERA)
    {
        handle = wirelessHandle_;
    }
    else if (connectingType == TYPE_WIRED_ANDROID_CAMERA)
    {
        handle = wiredAndroidHandle_;
    }
    else if (connectingType == TYPE_WIRED_IOS_CAMERA)
    {
        handle = wiredIosHandle_;
    }
    return handle;
}

bool PhoneCameraMgr::PhoneCameraStart(PHONECAMERA_PROTOCOL_TYPE type, string abConfig)
{
    TinyAutoLock lock(mutex_);
    LOG(INFO) << "[PhoneCamera]PhoneCameraStart type = " << type << " serviceState =" << serviceState << " serviceSubState=" << serviceSubState;
    if (serviceState != SERVICESTATE_STOPED)
    {
        return FALSE;
    }
    
    serviceSubState = SERVICESUBSTATE_NOCONNECT;
    CastMateMirror_SetOption(CASTMATE_OPTION_SET_AB_CONFIG, abConfig.c_str());
    CastMateMirror_BindSdk(kAppID, kAppSecret, did_.c_str(), uid_.c_str(), &callbacks_);
    CastMateProtocolType ptype;
    string               streamParams = "{\"fps\":30,\"width\":1080,\"height\":1920,\"bitrate\":10240,\"audioSource\":0,\"micScale\":0,\"serverMode\":1,\"landscape\":0}";
    if (type == PHONECAMERA_PROTOCOL_TYPE_WIRELESS)
    {
        ptype = TYPE_WIRELESS_CAMERA;
        
        wirelessHandle_ = CastMateMirror_StartService_New(ptype, "LiveStudioPhoneCamera", 0, streamParams.c_str(), &callbacks_);
        if (wirelessHandle_ == nullptr)
        {
            LOG(INFO) << "[PhoneCamera]PhoneCameraStart failed";
            return FALSE;
        }
    }
    else if (type == PHONECAMERA_PROTOCOL_TYPE_WIRED)
    {
        ptype = TYPE_WIRED_ANDROID_CAMERA;
        wiredAndroidHandle_ = CastMateMirror_StartService_New(ptype, "LiveStudioPhoneCamera", 0, streamParams.c_str(), &callbacks_);

        ptype = TYPE_WIRED_IOS_CAMERA;
        wiredIosHandle_ = CastMateMirror_StartService_New(ptype, "LiveStudioPhoneCamera", 0, streamParams.c_str(), &callbacks_);

        if (wiredAndroidHandle_ == nullptr || wiredIosHandle_ == nullptr)
        {
            LOG(INFO) << "[PhoneCamera]PhoneCameraStart failed";
            return FALSE;
        }

        CastMateMirror_SetInviteListener(&listener_);

        PhoneCameraOnStartResultEvent event;
        event.errCode = 0;
        eventbus::EventBus::PostEvent(event);
    }
    serviceState = SERVICESTATE_RUNNING;
    LOG(INFO) << "[PhoneCamera]PhoneCameraStart wiredAndroidHandle_ = " << wiredAndroidHandle_ << " wiredIosHandle_ =" << wiredIosHandle_ << " wirelessHandle_=" << wirelessHandle_;
    return TRUE;
}

bool PhoneCameraMgr::PhoneCameraStartAll(string abConfig)
{
    TinyAutoLock lock(mutex_);
    LOG(INFO) << "[PhoneCamera]PhoneCameraStartAll serviceState =" << serviceState << " serviceSubState=" << serviceSubState;
    if (serviceState != SERVICESTATE_STOPED)
    {
        return FALSE;
    }

    serviceSubState = SERVICESUBSTATE_NOCONNECT;
    CastMateMirror_BindSdk(kAppID, kAppSecret, did_.c_str(), uid_.c_str(), &callbacks_);
    CastMateProtocolType ptype;
    string               streamParams = "{\"fps\":30,\"width\":1080,\"height\":1920,\"bitrate\":10240,\"audioSource\":0,\"micScale\":0,\"serverMode\":1,\"landscape\":0}";

    ptype = TYPE_WIRELESS_CAMERA;

    wirelessHandle_ = CastMateMirror_StartService_New(ptype, "LiveStudioPhoneCamera", 0, streamParams.c_str(), &callbacks_);
    if (wirelessHandle_ == nullptr)
    {
        LOG(INFO) << "[PhoneCamera]PhoneCameraStartAll failed";
        return FALSE;
    }

    ptype = TYPE_WIRED_ANDROID_CAMERA;
    wiredAndroidHandle_ = CastMateMirror_StartService_New(ptype, "LiveStudioPhoneCamera", 0, streamParams.c_str(), &callbacks_);

    ptype = TYPE_WIRED_IOS_CAMERA;
    wiredIosHandle_ = CastMateMirror_StartService_New(ptype, "LiveStudioPhoneCamera", 0, streamParams.c_str(), &callbacks_);

    if (wiredAndroidHandle_ == nullptr || wiredIosHandle_ == nullptr)
    {
        LOG(INFO) << "[PhoneCamera]PhoneCameraStartAll failed";
        return FALSE;
    }

    CastMateMirror_SetInviteListener(&listener_);

    PhoneCameraOnStartResultEvent event;
    event.errCode = 0;
    eventbus::EventBus::PostEvent(event);
    serviceState = SERVICESTATE_RUNNING;
    LOG(INFO) << "[PhoneCamera]PhoneCameraStartAll wiredAndroidHandle_ = " << wiredAndroidHandle_ << " wiredIosHandle_ =" << wiredIosHandle_ << " wirelessHandle_=" << wirelessHandle_;
    return TRUE;
}

bool PhoneCameraMgr::PhoneCameraClose()
{

    LOG(INFO) << "[PhoneCamera]PhoneCameraClose serviceState = " << serviceState << " serviceSubState = " << serviceSubState << " wiredAndroidHandle_ = " << wiredAndroidHandle_ << " wiredIosHandle_ = " << wiredIosHandle_ << " wirelessHandle_ = " << wirelessHandle_;;
    TinyAutoLock lock(mutex_);
    if (serviceState == SERVICESTATE_RUNNING)
    {
        serviceState = SERVICESTATE_STOPING;
        if (wirelessHandle_ != nullptr)
        {
            LOG(INFO) << "[PhoneCamera]PhoneCameraCloseService wirelessHandle_ = " << wirelessHandle_;
            CastMateMirror_StopService(wirelessHandle_);
        }

        if (wiredAndroidHandle_ != nullptr)
        {
            LOG(INFO) << "[PhoneCamera]PhoneCameraCloseService wiredAndroidHandle_ = " << wiredAndroidHandle_;
            CastMateMirror_StopService(wiredAndroidHandle_);
        }

        if (wiredIosHandle_ != nullptr)
        {
            LOG(INFO) << "[PhoneCamera]PhoneCameraCloseService wiredIosHandle_ = " << wiredIosHandle_;
            CastMateMirror_StopService(wiredIosHandle_);
        }
    }

    PhoneCameraOnClosed();
    return TRUE;
}

bool PhoneCameraMgr::PhoneCameraOnClosed()
{
    LOG(INFO) << "[PhoneCamera]PhoneCameraOnClosed";
    
    serviceState = SERVICESTATE_STOPED;
    serviceSubState = SERVICESUBSTATE_NOCONNECT;
    wirelessHandle_ = nullptr;
    wiredAndroidHandle_ = nullptr;
    wiredIosHandle_ = nullptr;
    devidePlatform.clear();
    PhoneCameraOnClosedEvent event;
    eventbus::EventBus::PostEvent(event);
    LOG(INFO) << "[PhoneCamera]PhoneCameraOnClosed wiredAndroidHandle_ = " << wiredAndroidHandle_ << " wiredIosHandle_ =" << wiredIosHandle_ << " wirelessHandle_=" << wirelessHandle_;
    return true;
}

bool PhoneCameraMgr::PhoneCameraStartBrowser()
{
    if (wiredAndroidHandle_ == nullptr || wiredIosHandle_ == nullptr)
    {
        LOG(INFO) << "[PhoneCamera]PhoneCameraStartBrowser handle null";
        return false;
    }

    bool ret = CastMateMirror_StartBrowse();
    LOG(INFO) << "[PhoneCamera]PhoneCameraStartBrowser ret = " << ret;
    return ret;
}

bool PhoneCameraMgr::PhoneCameraStopBrowser()
{
    bool ret = CastMateMirror_StopBrowse();
    LOG(INFO) << "[PhoneCamera]PhoneCameraStopBrowser ret = " << ret;
    return ret;
}

bool PhoneCameraMgr::PhoneCameraInviteMirror(string deviceKey)
{
    void* handle = nullptr;
    auto  itor = devidePlatform.find(deviceKey);
    if (itor == devidePlatform.end())
    {
        LOG(ERROR) << "[PhoneCamera]PhoneCameraInviteMirror find error deviceKey = " << deviceKey;
        return false;
    }
    if (itor->second == PHONECAMERA_DEVICE_TYPE_WIRED_ANDROID)
    {
        handle = wiredAndroidHandle_;
    }
    else if (itor->second == PHONECAMERA_DEVICE_TYPE_WIRED_IOS)
    {
        handle = wiredIosHandle_;
    }

    if (handle == nullptr)
    {
        LOG(ERROR) << "[PhoneCamera]PhoneCameraInviteMirror handle error deviceKey = " << deviceKey;
        return false;
    }
    
    bool  ret = CastMateMirror_InviteMirror(handle, deviceKey.c_str(), nullptr);
    LOG(INFO) << "[PhoneCamera]PhoneCameraInviteMirror deviceKey = " << deviceKey << " ret = " << ret;
    return ret;
}

bool PhoneCameraMgr::PhoneCameraStopMirror(string deviceKey)
{
    if (deviceKey != connectingDeviceKey)
    {
        LOG(ERROR) << "[PhoneCamera]PhoneCameraStopMirror not found deviceKey = " << deviceKey;
        return false;
    }
    void* handle = GetCurrentHanle();
    bool  ret = CastMateMirror_StopMirror(handle, deviceKey.c_str());
    LOG(INFO) << "[PhoneCamera]PhoneCameraStopMirror handle << " << handle << " deviceKey = " << deviceKey << " ret = " << ret;
    return ret;
}

bool PhoneCameraMgr::PhoneCameraSendMsg(string deviceKey, string msg)
{
    if (deviceKey != connectingDeviceKey)
    {
        LOG(ERROR) << "[PhoneCamera]PhoneCameraSendMsg not found deviceKey = " << deviceKey;
        return false;
    }
    void* handle = GetCurrentHanle();
    bool ret = CastMateMirror_SendMetaData(handle, deviceKey.c_str(), msg.c_str());
    LOG(INFO) << "[PhoneCamera]PhoneCameraSendMsg deviceKey = " << deviceKey << " msg = " << msg << " ret = " << ret;
    return ret;
}

void PhoneCameraMgr::Auth_Handler(CastMateAuthResult result)
{
    LOG(INFO) << "[PhoneCamera]Auth_Handler result = " << result;
}

void PhoneCameraMgr::Audio_Open(void* handle, int bits, int channels, int samplerate,
                int samples, CastMateCodecType type)
{
}

void PhoneCameraMgr::Audio_Process(void* handle, const void* buffer, int buflen, double timestamp, uint32_t seqnum, frame_stat_info_t frame_stat)
{
}

void PhoneCameraMgr::Audio_Close(void* handle)
{
}

void PhoneCameraMgr::Video_Open(void* handle, const void* buffer, int buffersize, CastMateCodecType type, double timestamp)
{
    LOG(INFO) << "[PhoneCamera]Video_Open";
    serviceSubState = SERVICESUBSTATE_PROCESS;
    PhoneCameraOnCameraReadyResultEvent event;
    event.errCode = 0;
    eventbus::EventBus::PostEvent(event);

    CreateCastMateEvent pevent;
    pevent.protocolType = MapToCastmateProtocolType(CastMateMirror_GetProtocolType(handle));
    pevent.eventType = CASTMATE_EVENT_FIRSTFRAME;
    pevent.eventCode = 0;
    pevent.msg = "";
    eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::Video_Process(void* handle, const void* buffer, int buffersize, CastMateCodecType type, int angle, double timestamp, frame_stat_info_t frame_stat)
{
}

void PhoneCameraMgr::Video_Close(void* handle)
{
    CreateCastMateEvent pevent;
    pevent.protocolType = MapToCastmateProtocolType(CastMateMirror_GetProtocolType(handle));
    pevent.eventType = CASTMATE_EVENT_CLOSE;
    pevent.eventCode = 0;
    pevent.msg = "";
    eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::OnConnect(void* handle, const char* deviceKey, const char* name)
{
    LOG(INFO) << "[PhoneCamera]OnConnect deviceKey=" << deviceKey << " name = " << name;
    serviceSubState = SERVICESUBSTATE_CONNECTING;
    connectingDeviceKey = deviceKey;
    PhoneCameraOnConnectEvent event;
    connectingType = CastMateMirror_GetProtocolType(handle);
    if (connectingType == TYPE_WIRELESS_CAMERA)
    {
        event.deviceType = PHONECAMERA_DEVICE_TYPE_WIRELESS;
    }
    else if (connectingType == TYPE_WIRED_ANDROID_CAMERA)
    {
        event.deviceType = PHONECAMERA_DEVICE_TYPE_WIRED_ANDROID;
        platformType_ = PHONEPLATFORMTYPE_ANDROID;
    }
    else if (connectingType == TYPE_WIRED_IOS_CAMERA)
    {
        event.deviceType = PHONECAMERA_DEVICE_TYPE_WIRED_IOS;
        platformType_ = PHONEPLATFORMTYPE_IOS;
    }

    event.errCode = 0;
    event.deviceKey = deviceKey;
    event.name = name;
    eventbus::EventBus::PostEvent(event);

    CreateCastMateEvent pevent;
    pevent.protocolType = MapToCastmateProtocolType(CastMateMirror_GetProtocolType(handle));
    pevent.eventType = CASTMATE_EVENT_CONNECTED;
    pevent.eventCode = 0;
    pevent.msg = "";
    eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::OnDisconnect(void* handle, const char* deviceKey, int code, const char* msg)
{
    LOG(INFO) << "[PhoneCamera]OnDisconnect deviceKey=" << deviceKey << " code = " << code << " msg=" << msg
              << " serviceState =" << serviceState << " serviceSubState = " << serviceSubState;
    serviceSubState = SERVICESUBSTATE_DISCONNECT;
    if (serviceState == SERVICESTATE_STOPING)
    {
        connectingDeviceKey = "";
    }
    else if (serviceState == SERVICESTATE_RUNNING)
    {
        PhoneCameraOnDisconnectEvent event;
        event.deviceKey = deviceKey;
        event.errCode = code;
       
        eventbus::EventBus::PostEvent(event);
    }

    CreateCastMateEvent pevent;
    pevent.protocolType = MapToCastmateProtocolType(CastMateMirror_GetProtocolType(handle));
    pevent.eventType = CASTMATE_EVENT_DISCONNECTED;
    pevent.eventCode = 0;
    pevent.msg = "";
    eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::OnError(void* handle, int32_t error_num)
{
    LOG(ERROR) << "[PhoneCamera]OnError error_num=0x" << std::hex << error_num;
    PhoneCameraOnErrorFoundEvent event;
    event.errCode = error_num;
    eventbus::EventBus::PostEvent(event);

    CreateCastMateEvent pevent;
    pevent.protocolType = MapToCastmateProtocolType(CastMateMirror_GetProtocolType(handle));
    pevent.eventType = CASTMATE_EVENT_ONERROR;
    pevent.eventCode = error_num;
    pevent.msg = "";
    eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::OnEvent(void* handle, int32_t event_code, const char* event_msg)
{
    LOG(ERROR) << "[PhoneCamera]OnEvent event_code="  << event_code << " event_msg=" << event_msg;
    CreateCastMateEvent pevent;
    pevent.protocolType = MapToCastmateProtocolType(CastMateMirror_GetProtocolType(handle));
    pevent.eventType = CASTMATE_EVENT_CASTMATE_EVENT;
    pevent.eventCode = event_code;
    pevent.msg = event_msg;
    eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::OnMirrorPort(void* handle, CastMateProtocolType type, uint16_t port, const char* all_local_ip)
{
    LOG(INFO) << "[PhoneCamera]OnMirrorPort type=" << type << " port=" << port << " all_local_ip=" << all_local_ip;
    PhoneCameraOnStartResultEvent event;
    event.errCode = 0;
    event.ipList = all_local_ip;
    event.port = port;
    eventbus::EventBus::PostEvent(event);
}

void PhoneCameraMgr::Video_Output(void* handle, const struct CastMateVideoFrame* frame)
{

}

void PhoneCameraMgr::OnRecvMetaData(void* handle, const char* src, const char* msg)
{
    LOG(INFO) << "[PhoneCamera]OnRecvMetaData src=" << src << " msg=" << msg;
    PhoneCameraOnRecvMetaDataFoundEvent event;
    event.src = src;
    event.msg = msg;
    eventbus::EventBus::PostEvent(event);
}

void PhoneCameraMgr::OnBrowseEvent(void* handle, MDNS_BROWSE_EVENT event, source_device_info_t* sourceDevice)
{
   if (sourceDevice != nullptr)
   {
       LOG(INFO) << "[PhoneCamera]OnBrowseEvent event=" << event << " deviceType=" << sourceDevice->deviceType << " deviceKey =" << sourceDevice->deviceKey << " name=" << sourceDevice->hostName;
   }
   else
   {
       LOG(INFO) << "[PhoneCamera]OnBrowseEvent event=" << event;
       return;
   }
    
   PhoneCameraOnDeiviceFoundEvent pevent;
   pevent.errCode = 0;
   if (event == BROWSE_FOUND)
   {
        pevent.opt = PHONECAMERA_DEVICE_FOUND_OPT_ADD;
   }
   else if (event == BROWSE_LOST)
   {
        pevent.opt = PHONECAMERA_DEVICE_FOUND_OPT_DEL;
   }
   if (sourceDevice->deviceType == WIRELESS_SOURCE)
   {
        pevent.deviceType = PHONECAMERA_DEVICE_TYPE_WIRELESS;
   }
   else if (sourceDevice->deviceType == WIRED_ANDROID_SOURCE)
   {
        pevent.deviceType = PHONECAMERA_DEVICE_TYPE_WIRED_ANDROID;
   }
   else if (sourceDevice->deviceType == WIRED_IOS_SOURCE)
   {
        pevent.deviceType = PHONECAMERA_DEVICE_TYPE_WIRED_IOS;
   }
   
   if (devidePlatform.find(sourceDevice->deviceKey) == devidePlatform.end())
   {
        devidePlatform[sourceDevice->deviceKey] = pevent.deviceType;
   }

   pevent.deviceKey = sourceDevice->deviceKey;
   pevent.name = sourceDevice->hostName;
   eventbus::EventBus::PostEvent(pevent);
}

void PhoneCameraMgr::OnBrowseError(void* handle, int errorCode, const char* errorMessage)
{
    LOG(ERROR) << "[PhoneCamera]OnBrowseError errorCode=" << errorCode << " errorMessage=" << *errorMessage;
    PhoneCameraOnDeiviceFoundEvent event;
    event.errCode = errorCode;
    eventbus::EventBus::PostEvent(event);
}

void PhoneCameraMgr::OnInviteResult(void* handle, bool result)
{
    LOG(INFO) << "[PhoneCamera]OnInviteResult result=" << result;
    if (result == false)
    {
        PhoneCameraOnConnectEvent event;
        event.errCode = 0x12345678;
        eventbus::EventBus::PostEvent(event);
    }
}

void PhoneCameraMgr::OnInviteError(void* handle, int errorCode, const char* errorMessage)
{
    LOG(ERROR) << "[PhoneCamera]OnInviteError errorCode=" << errorCode << " errorMessage=" << *errorMessage;
    PhoneCameraOnConnectEvent event;
    event.errCode = errorCode;
    eventbus::EventBus::PostEvent(event);
}

void PhoneCameraMgr::Auth_Handler_s(void* cls, CastMateAuthResult result)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Auth_Handler(result);
    }
}

void PhoneCameraMgr::Audio_Open_s(void* handle, void* cls, int bits, int channels, int samplerate,
                               int samples, CastMateCodecType type)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Audio_Open(handle, bits, channels, samplerate, samples, type);
    }
}

void PhoneCameraMgr::Audio_Process_s(void* handle, void* cls, const void* buffer, int buflen, double timestamp, uint32_t seqnum, frame_stat_info_t frame_stat)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Audio_Process(handle, buffer, buflen, timestamp, seqnum, frame_stat);
    }
}

void PhoneCameraMgr::Audio_Close_s(void* handle, void* cls)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Audio_Close(handle);
    }
}

void PhoneCameraMgr::Video_Open_s(void* handle, void* cls, const void* buffer, int buffersize, CastMateCodecType type, double timestamp)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Video_Open(handle, buffer, buffersize, type, timestamp);
    }
}

void PhoneCameraMgr::Video_Process_s(void* handle, void* cls, const void* buffer, int buffersize, CastMateCodecType type, int angle, double timestamp, frame_stat_info_t frame_stat)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Video_Process(handle, buffer, buffersize, type, angle, timestamp, frame_stat);
    }
}

void PhoneCameraMgr::Video_Close_s(void* handle, void* cls)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Video_Close(handle);
    }
}

void PhoneCameraMgr::OnConnect_s(void* handle, void* cls, const char* userId, const char* name)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnConnect(handle, userId, name);
    }
}

void PhoneCameraMgr::OnDisconnect_s(void* handle, void* cls, const char* userId, int code, const char* msg)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnDisconnect(handle, userId, code, msg);
    }
}

void PhoneCameraMgr::OnError_s(void* handle, void* cls, int32_t error_num)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnError(handle, error_num);
    }
}

void PhoneCameraMgr::OnEvent_s(void* handle, void* cls, int32_t event_code, const char* event_msg)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnEvent(handle, event_code, event_msg);
    }
}

void PhoneCameraMgr::OnMirrorPort_s(void* handle, void* cls, CastMateProtocolType type, uint16_t port, const char* all_local_ip)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnMirrorPort(handle, type, port, all_local_ip);
    }
}

void PhoneCameraMgr::Video_Output_s(void* handle, void* cls, const struct CastMateVideoFrame* frame)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->Video_Output(handle, frame);
    }
}

void PhoneCameraMgr::OnRecvMetaData_s(void* handle, void* cls, const char* src, const char* msg)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnRecvMetaData(handle, src, msg);
    }
}

void PhoneCameraMgr::OnBrowseEvent_s(void* handle, void* cls, MDNS_BROWSE_EVENT event, source_device_info_t* sourceDevice)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnBrowseEvent(handle, event, sourceDevice);
    }
}

void PhoneCameraMgr::OnBrowseError_s(void* handle, void* cls, int errorCode, const char* errorMessage)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnBrowseError(handle, errorCode, errorMessage);
    }
}

void PhoneCameraMgr::OnInviteResult_s(void* handle, void* cls, bool result)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnInviteResult(handle, result);
    }
}

void PhoneCameraMgr::OnInviteError_s(void* handle, void* cls, int errorCode, const char* errorMessage)
{
    if (cls)
    {
        ((PhoneCameraMgr*)cls)->OnInviteError(handle, errorCode, errorMessage);
    }
}



