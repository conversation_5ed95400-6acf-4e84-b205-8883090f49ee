#pragma once

#include "LSPublicHeader.h"
#include "Canvas.h"

class Preview
{
public:
    Preview();
    ~Preview();

    bool Create();
    bool Destroy();

    void SetPreviewInfo(const PREVIEW_INFO* previewInfo);
    void GetPreviewInfo(PREVIEW_INFO* previewInfo);
    void UpdatePreviewLayout(const PREVIEW_INFO& previewInfo);

    bool BindCanvas(UINT64 canvasID);
    bool UnbindCanvas(UINT64 canvasID);
    void SetCurCanvasID(UINT64 canvasID);
    void SetCanvasIDs(const std::vector<UINT64>& canvasIDs);

protected:
    PREVIEW_INFO         m_previewInfo{};
};