﻿#include "Source.h"
#include "stringutil.h"
#include "LSPublicHeader.h"
#include "MediaSDKControllerV2Impl.h"
#include "Layer.h"
#include "ModeSceneMgr.h"
#include "AudioMgr.h"
#include <variant>

extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;

Source::Source(){}

Source::Source(SOURCE_INFO sourceInfo)
{
	m_sourceInfo = sourceInfo;
}

Source::~Source() {}

void Source::SetSourceInfo(const SOURCE_INFO* info)
{
	m_sourceInfo = *info;
}

void Source::GetSourceInfo(SOURCE_INFO* sourceInfo, SOURCE_INFO_CMD cmd /*= SOURCE_INFO_NONE*/)
{
    *sourceInfo = m_sourceInfo;

    if (cmd != SOURCE_INFO_NONE)
    {
        std::string source_id = "";
        Util::NumToString(sourceInfo->id, &source_id);

        bool success = false;
        if (cmd & SOURCE_INFO_FPS)
        {
            float fps = .0f;
            success = g_sdkController->GetSourceFPS(source_id, &fps);
            if (success)
            {
                sourceInfo->fps = fps;
            }
            else
            {
                LOG(ERROR) << "[Source::GetSourceInfo] GetSourceFPS failed";
            }
        }

        switch (sourceInfo->type)
        {
        case VISUAL_CAMERA:
        {
            CAMERA_SOURCE camera = std::get<CAMERA_SOURCE>(sourceInfo->source);
            if (cmd & SOURCE_INFO_CAMERA_CONTROL)
            {
                std::vector<CAMERA_CONTROL> properties{};
                success = g_sdkController->CameraSourceGetControlProperty(source_id, reinterpret_cast<uint64_t>(camera.hwnd), &properties);
                if (success)
                {
                    camera.controls.clear();
                    std::stringstream ss;
                    for (const auto& property : properties)
                    {
                        camera.controls.push_back(property);
                        ss << property.toString() << ",";
                    }
                    LOG(INFO) << "[Source::GetSourceInfo] CameraSourceGetControlProperty source_id: " << source_id << ", controls: " << ss.str();
                }
                else
                {
                    LOG(ERROR) << "[Source::GetSourceInfo] CameraSourceGetControlProperty failed";
                }
            }
            if (cmd & SOURCE_INFO_CAP_PROC_AMP)
            {
                std::vector<VIDEO_PROC_AMP> properties{};
                success = g_sdkController->CameraSourceGetAmpProperty(source_id, reinterpret_cast<uint64_t>(camera.hwnd), &properties);
                if (success)
                {
                    camera.procAmps.clear();
                    std::stringstream ss;
                    for (const auto& property : properties)
                    {
                        camera.procAmps.push_back(property);
                        ss << property.toString() << ",";
                    }
                    LOG(INFO) << "[Source::GetSourceInfo] CameraSourceGetAmpProperty source_id: " << source_id << ", procAmps: " << ss.str();
                }
                else
                {
                    LOG(ERROR) << "[Source::GetSourceInfo] CameraSourceGetAmpProperty failed";
                }
            }
            if (cmd & SOURCE_INFO_FILTER_STATE)
            {
                CAMERA_FILTER_STATE state = CAMERA_FILTER_STATE::CAMERA_FILTER_STATE_STOPPED;
                bool camera_is_ready = false;
                CAMERA_CREATE_STEP_STATE step_state{};
                success = g_sdkController->CameraSourceGetState(source_id, &camera_is_ready, &state, &step_state);
                if (success)
                {
                    camera.filterState = state;
                }
                else
                {
                    LOG(ERROR) << "[Source::GetSourceInfo] CameraSourceGetState failed";
                }
                LOG(INFO) << "[Source::GetSourceInfo] CameraSourceGetState source_id: " << source_id << ", state: " << camera.filterState << " step_state.step: " << static_cast<uint32_t>(step_state.step) << " step_state.error: " << step_state.error;
            }
            sourceInfo->source = camera;
        }
        break;
        case VISUAL_FAV:
        {
            FAV_SOURCE source = std::get<FAV_SOURCE>(sourceInfo->source);
            if (cmd & SOURCE_INFO_PAUSED)
            {
                success = g_sdkController->MediaSourceIsPaused(source_id, &source.paused);
                if (!success)
                    LOG(ERROR) << "[Source::GetSourceInfo] MediaSourceIsPaused failed";
                LOG(INFO) << "[Source::GetSourceInfo] MediaSourceIsPaused source_id: " << source_id << ", paused: " << source.paused;
            }
            if (cmd & SOURCE_INFO_MEDIA_SEEKABLE)
            {
                success = g_sdkController->MediaSourceIsSeekable(source_id, &source.materialDesc.seekable);
                if (!success)
                    LOG(ERROR) << "[Source::GetSourceInfo] MediaSourceIsSeekable failed";
                LOG(INFO) << "[Source::GetSourceInfo] MediaSourceIsSeekable source_id: " << source_id << ", seekable: " << source.materialDesc.seekable;
            }
            if (cmd & SOURCE_INFO_MEDIA_DURATION)
            {
                success = g_sdkController->MediaSourceGetDuration(source_id, &source.materialDesc.duration);
                if (!success)
                    LOG(ERROR) << "[Source::GetSourceInfo] MediaSourceGetDuration failed";
                LOG(INFO) << "[Source::GetSourceInfo] MediaSourceGetDuration source_id: " << source_id << ", duration: " << source.materialDesc.duration;
            }
            sourceInfo->source = source;
        }
        break;
        case VISUAL_GRAFFITI:
        {
            GRAFFITI_SOURCE source = std::get<GRAFFITI_SOURCE>(sourceInfo->source);
            if (cmd & SOURCE_INFO_HAS_RESTORES)
            {
                success = g_sdkController->GraffitiSourceHasRestores(source_id, &source.hasRestores);
                if (!success)
                    LOG(ERROR) << "[Source::GetSourceInfo] GraffitiSourceHasRestores failed";
                LOG(INFO) << "[Source::GetSourceInfo] GraffitiSourceHasRestores source_id: " << source_id << ", hasRestores: " << source.hasRestores;
            }
            if (cmd & SOURCE_INFO_HAS_TRACKERS)
            {
                success = g_sdkController->GraffitiSourceHasTracks(source_id, &source.hasTrackers);
                if (!success)
                    LOG(ERROR) << "[Source::GetSourceInfo] GraffitiSourceHasTracks failed";
                LOG(INFO) << "[Source::GetSourceInfo] GraffitiSourceHasTracks source_id: " << source_id << ", hasTrackers: " << source.hasTrackers;
            }
            if (cmd & SOURCE_INFO_EDITABLE)
            {
                success = g_sdkController->GraffitiSourceGetEditState(source_id, &source.editable);
                if (!success)
                    LOG(ERROR) << "[Source::GetSourceInfo] GraffitiSourceGetEditState failed";
                LOG(INFO) << "[Source::GetSourceInfo] GraffitiSourceGetEditState source_id: " << source_id << ", editable: " << source.editable;
            }
            sourceInfo->source = source;
        }
        break;
        default:
            break;
        }
    }
}

void Source::ControlSource(SOURCE_INFO& sourceInfo, SOURCE_CONTROL_CMD cmd)
{
    std::string source_id = "";
    Util::NumToString(sourceInfo.id, &source_id);

    bool success = false;
    switch (sourceInfo.type)
    {
    case VISUAL_WINDOW:
    {
        const auto& window = std::get<WINDOW_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_CHANGE_WINDOW)
        {
            if (window.windowDesc.hwnd)
            {
                LOG(INFO) << "Source::ControlSource] WindowSourceSelectWindow source_id: " << source_id << ", hwnd: " << window.windowDesc.hwnd;
                success = g_sdkController->WindowSourceSelectWindow(source_id, reinterpret_cast<int64_t>(window.windowDesc.hwnd));
                if (!success)
                    LOG(ERROR) << "Source::ControlSource] WindowSourceSelectWindow failed";
            }

            if (!success && !window.windowDesc.szClass.empty())
            {
                LOG(INFO) << "Source::ControlSource] WindowSourceSelectWindow source_id: " << source_id << ", szClass: " << window.windowDesc.szClass;
                success = g_sdkController->WindowSourceSelectWindow(source_id, window.windowDesc.szClass);
                if (!success)
                    LOG(ERROR) << "Source::ControlSource] WindowSourceSelectWindow failed";
            }
        }
        if (cmd & SOURCE_CONTROL_SET_CURSOR)
        {
            LOG(INFO) << "Source::ControlSource] WindowSourceShowCursor source_id: " << source_id << ", hasCursor: " << window.windowDesc.hasCursor;
            success = g_sdkController->WindowSourceShowCursor(source_id, window.windowDesc.hasCursor);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] WindowSourceShowCursor failed";
        }
        break;
    }
    case VISUAL_GAME:
    {
        const auto& game = std::get<GAME_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_SET_CURSOR)
        {
            LOG(INFO) << "Source::ControlSource] GameSourceShowCursor source_id: " << source_id << ", hasCursor: " << game.windowDesc.hasCursor;
            success = g_sdkController->GameSourceShowCursor(source_id, game.windowDesc.hasCursor);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GameSourceShowCursor failed";
        }
        if (cmd & SOURCE_CONTROL_CHANGE_WINDOW)
        {
            LOG(INFO) << "Source::ControlSource] GameSourceSelectTarget source_id: " << source_id << ", game info: " << game.toString();
            success = g_sdkController->GameSourceSelectTarget(source_id, game);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] SelectGameSource failed";
        }
        if (cmd & SOURCE_CONTROL_SET_FOCUS_CPU)
        {
            LOG(INFO) << "Source::ControlSource] GameSourceSetShareDataMode source_id: " << source_id << ", enable_cpu: " << game.enableCpu;
            success = g_sdkController->GameSourceSetShareDataMode(source_id, game.enableCpu);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GameSourceSetShareDataMode failed";
        }
        if (cmd & SOURCE_CONTROL_SET_LIMIT_RATE)
        {
            LOG(INFO) << "Source::ControlSource] GameSourceLimitCaptureFPS source_id: " << source_id << ", enableLimit: " << game.enableLimit << ", fps: " << sourceInfo.fps;
            success = g_sdkController->GameSourceLimitCaptureFPS(source_id, game.enableLimit, sourceInfo.fps);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GameSourceLimitCaptureFPS failed";
        }
        break;
    }
    case VISUAL_MONITOR:
    {
        const auto& monitor = std::get<MONITOR_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_SET_CURSOR)
        {
            LOG(INFO) << "Source::ControlSource] MonitorSourceShowCursor source_id: " << source_id << ", hasCursor: " << monitor.hasCursor;
            success = g_sdkController->MonitorSourceShowCursor(source_id, monitor.hasCursor);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MonitorSourceShowCursor failed";
        }
        if (cmd & SOURCE_CONTROL_CHANGE_WINDOW)
        {
            LOG(INFO) << "Source::ControlSource] MonitorSourceSetMonitor layer_id: " << source_id << ", monitorDid: " << monitor.monitorDesc.did;
            success = g_sdkController->MonitorSourceSetMonitor(source_id, monitor.monitorDesc.did);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MonitorSourceSetMonitor failed";
        }
        break;
    }
    case VISUAL_FAV:
    {
        const auto& fav = std::get<FAV_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_PAUSE)
        {
            LOG(INFO) << "Source::ControlSource] MediaSourcePause source_id: " << source_id;
            success = g_sdkController->MediaSourcePause(source_id);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MediaSourcePause failed";
        }
        if (cmd & SOURCE_CONTROL_RESUME)
        {
            LOG(INFO) << "Source::ControlSource] MediaSourceResume source_id: " << source_id;
            success = g_sdkController->MediaSourceResume(source_id);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MediaSourceResume failed";
        }
        if (cmd & SOURCE_CONTROL_SEEK)
        {
            LOG(INFO) << "Source::ControlSource] MediaSourceSeek source_id: " << source_id << ", seekTime: " << fav.seekTime << ", absolute: " << fav.absolute;
            success = g_sdkController->MediaSourceSeek(source_id, fav.seekTime, fav.absolute);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MediaSourceSeek failed";
        }
        if (cmd & SOURCE_CONTROL_LOOP)
        {
            LOG(INFO) << "Source::ControlSource] MediaSourceEnableLoop source_id: " << source_id << ", loop: " << fav.materialDesc.loop;
            success = g_sdkController->MediaSourceEnableLoop(source_id, fav.materialDesc.loop);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MediaSourceEnableLoop failed";
        }
        break;
    }
    case VISUAL_CAMERA:
    {
        const auto& camera = std::get<CAMERA_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_SET_VIDEO_PROCAMP)
        {
            if (!camera.procAmps.empty())
            {
                LOG(INFO) << "Source::ControlSource] CameraSourceSetAmpProperty source_id: " << source_id << ", hwnd: " << camera.hwnd << ", procAmps[0] type: " << camera.procAmps[0].type << ", procAmp[0]: " << camera.procAmps[0].toString();
                success = g_sdkController->CameraSourceSetAmpProperty(source_id, reinterpret_cast<int64_t>(camera.hwnd), camera.procAmps[0].type, camera.procAmps[0].attr.value, camera.procAmps[0].attr.flag);
                if (!success)
                    LOG(ERROR) << "Source::ControlSource] CameraSourceSetAmpProperty failed";
            }
        }
        if (cmd & SOURCE_CONTROL_SET_CAMERA_CONTROL)
        {
            if (!camera.controls.empty())
            {
                LOG(INFO) << "Source::ControlSource] CameraSourceSetControlProperty source_id: " << source_id << ", hwnd: " << camera.hwnd << ", control[0] type: " << camera.controls[0].type << ", controls[0]: " << camera.controls[0].toString();
                success = g_sdkController->CameraSourceSetControlProperty(source_id, reinterpret_cast<int64_t>(camera.hwnd), camera.controls[0].type, camera.controls[0].attr.value, camera.controls[0].attr.flag);
                if (!success)
                    LOG(ERROR) << "Source::ControlSource] CameraSourceSetControlProperty failed";
            }
        }
        if (cmd & SOURCE_CONTROL_SET_DETECT_COLOR_RANGE)
        {
            CAMERA_SOURCE camera = std::get<CAMERA_SOURCE>(sourceInfo.source);
            LOG(INFO) << "Source::ControlSource] CameraSourceSetDetectColorRangeParam source_id: " << source_id << ", interval: " << camera.detectColorRange.interval << ", limitedCnt: " << camera.detectColorRange.limitedCnt;
            success = g_sdkController->CameraSourceSetDetectColorRangeParam(source_id, camera.detectColorRange.interval, camera.detectColorRange.limitedCnt);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] CameraSourceSetDetectColorRangeParam failed";
        }
        if (cmd & SOURCE_CONTROL_STOP_DETECT_COLOR_RANGE)
        {
            LOG(INFO) << "Source::ControlSource] CameraSourceStopDetectColorRange source_id: " << source_id;
            success = g_sdkController->CameraSourceStopDetectColorRange(source_id);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] CameraSourceStopDetectColorRange failed";
        }
        if (cmd & SOURCE_CONTROL_SET_CAPTURE_MAX_RATE)
        {
            LOG(INFO) << "Source::ControlSource] CameraSourceSetMaxCaptureFps layer_id: " << source_id << ", maxRate: " << camera.maxRate;
            success = g_sdkController->CameraSourceSetMaxCaptureFps(source_id, camera.maxRate);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] CameraSourceSetMaxCaptureFps failed";
        }
        break;
    }
    case VISUAL_BYTELINK:
    {
        const auto& bytelink = std::get<BYTELINK_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_SET_OPTION)
        {
            std::stringstream            ss;
            std::vector<BYTELINK_OPTION> options{};
            for (const auto& bytelinkOption : bytelink.bytelinkOptions)
            {
                options.push_back(bytelinkOption);
                ss << bytelinkOption.config << ",";
            }
            LOG(INFO) << "Source::ControlSource] MobileProjectorSourceSetOption source_id: " << source_id << ", options: " << ss.str();
            success = g_sdkController->MobileProjectorSourceSetOption(options);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] MobileProjectorSourceSetOption failed";
        }
        break;
    }
    case VISUAL_GRAFFITI:
    {
        const auto& graffiti = std::get<GRAFFITI_SOURCE>(sourceInfo.source);
        if (cmd & SOURCE_CONTROL_SET_EDIT_STATE)
        {
            LOG(INFO) << "Source::ControlSource] GraffitiSourceSetEditState source_id: " << source_id << ", editable: " << graffiti.editable;
            success = g_sdkController->GraffitiSourceSetEditState(source_id, graffiti.editable);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GraffitiSourceSetEditState failed";
        }
        if (cmd & SOURCE_CONTROL_RESUME)
        {
            LOG(INFO) << "Source::ControlSource] GraffitiSourceRecover source_id: " << source_id;
            success = g_sdkController->GraffitiSourceRecover(source_id);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GraffitiSourceRecover failed";
        }
        if (cmd & SOURCE_CONTROL_SET_TYPE)
        {
            LOG(INFO) << "Source::ControlSource] GraffitiSourceSetType source_id: " << source_id << ", type: " << graffiti.type;
            success = g_sdkController->GraffitiSourceSetType(source_id, graffiti.type);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GraffitiSourceSetType failed";
        }
        if (cmd & SOURCE_CONTROL_SET_PEN)
        {
            LOG(INFO) << "Source::ControlSource] GraffitiSourceSetPen source_id: " << source_id << ", rgba: " << graffiti.colorRGBA.toString();
            success = g_sdkController->GraffitiSourceSetPen(source_id, graffiti.colorRGBA.R, graffiti.colorRGBA.G, graffiti.colorRGBA.B, (float)graffiti.colorRGBA.A, graffiti.penWidth);
            if (!success)
                LOG(ERROR) << "Source::ControlSource] GraffitiSourceSetPen failed";
        }
        break;
    }
    default:
        break;
    }

    SetSourceInfo(&sourceInfo);
}

bool Source::ReopenSource(SOURCE_INFO& sourceInfo)
{
    std::string source_id = "";
    Util::NumToString(sourceInfo.id, &source_id);

    bool success = false;
    if (sourceInfo.type == VISUAL_CAMERA)
    {
        const auto& camera = std::get<CAMERA_SOURCE>(sourceInfo.source);
        LOG(INFO) << "[Source::ReopenSource] ReopenCameraSourceWithTransform source_id: " << source_id << ", camera info: " << camera.toString();
        success = g_sdkController->ReopenCameraSource(source_id, camera, &sourceInfo.result);
        if (!success)
            LOG(ERROR) << "[Source::ReopenSource] ReopenCameraSourceWithTransform failed";
    }
    else if (sourceInfo.type == VISUAL_ANALOG)
    {
        const auto& analog = std::get<ANALOG_SOURCE>(sourceInfo.source);
        LOG(INFO) << "[Source::ReopenSource] ReopenAnalogSourceWithTransform source_id: " << source_id << ", analog info: " << analog.toString();
        success = g_sdkController->ReopenAnalogSource(source_id, analog, &sourceInfo.result);
        if (!success)
            LOG(ERROR) << "Source::ControlSource] ReopenAnalogSourceWithTransform failed";
    }
    
    if (sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
    {
        const auto& virtual_camera = std::get<VIRTUAL_CAMERA_SOURCE>(sourceInfo.source);
        LOG(INFO) << "[Source::ReopenSource] VirtualCameraSourceReopen source_id: " << source_id << ", virtual camera info: " << virtual_camera.toString();

        success = g_sdkController->VirtualCameraSourceReopen(virtual_camera.refID);
        if (!success)
        {
            LOG(ERROR) << "[Source::ReopenSource] VirtualCameraSourceReopen failed!";
            return false;
        }

        for (const auto& layerID : sourceInfo.layerIDs)
        {
            LAYER_INFO layerInfo;
            ModeSceneMgr::GetInstance()->GetLayerInfoByID(layerID, &layerInfo);

            success = g_sdkController->SetVirtualCameraFlipV(layerInfo.transform.vFlip);
            if (!success)
                LOG(ERROR) << "[Source::ReopenSource] LayerSetVerticalFlip failed!";

            success = g_sdkController->SetVirtualCameraFlipH(layerInfo.transform.hFlip);
            if (!success)
                LOG(ERROR) << "[Source::ReopenSource] LayerSetHorizontalFlip failed!";

            success = g_sdkController->SetVirtualCameraRotate(layerInfo.transform.angle);
            if (!success)
                LOG(ERROR) << "[Source::ReopenSource] LayerSetHorizontalFlip failed!";
        }
    }
    
    return success;
}

bool Source::CreateSource()
{
    std::string source_id = "";
    Util::NumToString(m_sourceInfo.id, &source_id);

    VISUAL_TYPE           source_type = m_sourceInfo.type;
    bool                  success = false;
    if (source_type == VISUAL_GAME)
    {
        success = CreateGameSource(source_id);
    }
    else if (source_type == VISUAL_CAMERA)
    {
        success = CreateCameraSource(source_id);
    }
    else if (source_type == VISUAL_WINDOW)
    {
        success = CreateWindowSource(source_id);
    }
    else if (source_type == VISUAL_FAV)
    {
        success = CreateMediaSource(source_id);
    }
    else if (source_type == VISUAL_IMAGE)
    {
        success = CreateImageSource(source_id);
    }
    else if (source_type == VISUAL_MONITOR)
    {
        success = CreateMonitorSource(source_id);
    }
    else if (source_type == VISUAL_BROWSER)
    {
        success = CreateBrowserSource(source_id);
    }
    else if (source_type == VISUAL_ANALOG)
    {
        success = CreateAnalogSource(source_id);
    }
    else if (source_type == VISUAL_BYTELINK)
    {
        success = CreateBytelinkSource(source_id);
    }
    else if (source_type == VISUAL_GRAFFITI)
    {
        success = CreateGraffitiSource(source_id);
    }
    else if (source_type == VISUAL_RTC)
    {
        success = CreateRTCSource(source_id);
    }

    if (!success && !IsSyncSource()) DestroySource();
    return success;
}

bool Source::DestroySource()
{
    std::string source_id = "";
    Util::NumToString(m_sourceInfo.id, &source_id);
    bool success = g_sdkController->VisualDestroy(source_id);
    if (!success)
        LOG(WARNING) << "[Source::DestorySource] VisualDestory failed";
    return success;
}

bool Source::CreateImageSource(const std::string& source_id)
{
    IMAGE_SOURCE image = std::get<IMAGE_SOURCE>(m_sourceInfo.source);
    bool         success = g_sdkController->CreateImageSource(source_id, image, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateImageSource] CreateImageSource failed";
    LOG(INFO) << "[Source::CreateImageSource] source_id: " << source_id << ", image info: " << image.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateGameSource(const std::string& source_id)
{
    GAME_SOURCE game = std::get<GAME_SOURCE>(m_sourceInfo.source);
    bool        success = g_sdkController->CreateGameSource(source_id, game, &m_sourceInfo.result);
    if (!success)
    {
        LOG(WARNING) << "[Source::CreateGameSource] CreateGameSource failed";
    }

    LOG(INFO) << "[Source::CreateGameSource] source_id: " << source_id << ", game info: " << game.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateWindowSource(const std::string& source_id)
{
    WINDOW_SOURCE       window = std::get<WINDOW_SOURCE>(m_sourceInfo.source);
    VISUAL_CAPTURE_TYPE cap_type = CAPTURE_TYPE_NONE;
    bool                success = g_sdkController->CreateWindowSource(source_id, window, &cap_type, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateWindowSource] CreateWindowSource failed";

	for (const auto& layerID : m_sourceInfo.layerIDs)
	{
		std::string layer_id = "";
		Util::NumToString(layerID, &layer_id);
		VisualCaptureTypeChangeEvent cap_event{};
		cap_event.layerID = layer_id;
		cap_event.type = cap_type;
		cap_event.capPID = window.windowDesc.dwPID;
		eventbus::EventBus::PostEvent(cap_event);
		LOG(INFO) << "[VisualCaptureTypeChangeEvent] visualID: " << source_id << ", layerID: " << layerID << ", cap_type: " << cap_event.type << ", cap_pid: " << cap_event.capPID;
	}
    LOG(INFO) << "[Source::CreateWindowSource] source_id: " << source_id << ", window info: " << window.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateMonitorSource(const std::string& source_id)
{
    MONITOR_SOURCE      monitor = std::get<MONITOR_SOURCE>(m_sourceInfo.source);
    VISUAL_CAPTURE_TYPE cap_type = CAPTURE_TYPE_NONE;
    bool                success = g_sdkController->CreateMonitorSource(source_id, monitor, &cap_type, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateMonitorSource] CreateMonitorSource failed";

	for (const auto& layerID : m_sourceInfo.layerIDs)
	{
		std::string layer_id = "";
		Util::NumToString(layerID, &layer_id);
		VisualCaptureTypeChangeEvent cap_event{};
		cap_event.layerID = layer_id;
		cap_event.type = cap_type;
		cap_event.capPID = 0;
		eventbus::EventBus::PostEvent(cap_event);
		LOG(INFO) << "[VisualCaptureTypeChangeEvent] visualID: " << source_id << ", layerID: " << layerID << ", cap_type: " << cap_event.type << ", cap_pid: " << cap_event.capPID;
	}
    LOG(INFO) << "[Source::CreateMonitorSource] source_id: " << source_id << ", monitor info: " << monitor.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateMediaSource(const std::string& source_id)
{
    FAV_SOURCE fav = std::get<FAV_SOURCE>(m_sourceInfo.source);
    bool       success = g_sdkController->CreateMediaSource(source_id, fav, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateMediaSource] CreateMediaSource failed";
	LOG(INFO) << "[Source::CreateMediaSource] source_id: " << source_id << ", fav info: " << fav.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
	return success;
}

bool Source::CreateBrowserSource(const std::string& source_id)
{
    BROWSER_SOURCE browser = std::get<BROWSER_SOURCE>(m_sourceInfo.source);
    bool           success = g_sdkController->CreateExtrenalBrowserSource(source_id, browser, &m_sourceInfo.result);
    if (!success)
        LOG(WARNING) << "[Source::CreateBrowserSource] CreateExtrenalBrowserSource failed";
    LOG(INFO) << "[Source::CreateBrowserSource] source_id: " << source_id << ", browser info: " << browser.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateCameraSource(const std::string& source_id)
{
    CAMERA_SOURCE& camera = std::get<CAMERA_SOURCE>(m_sourceInfo.source);
    bool           success = false;
    bool           is_pending = false;

    for (int i = 0; i < MAX_CAMERA_RETRY_COUNT; ++i)
    {
        if (i > 0)
        {
            g_sdkController->VisualDestroy(source_id);
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            LOG(WARNING) << "[Source::CreateCameraSource] Retrying camera creation (" << i << "/" << (MAX_CAMERA_RETRY_COUNT - 1) << ")...";
        }

        bool operation_success = g_sdkController->CreateCameraSource(source_id, camera, &m_sourceInfo.result);
        const bool camera_is_pending = m_sourceInfo.result.createStepState.step == CAMERA_STEP_STATE::CAMERA_STEP_STATE_CONTEXT && m_sourceInfo.result.createStepState.error == E_PENDING;
        const bool camera_is_ready = m_sourceInfo.result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL;
        if (camera_is_pending)
        {
            LOG(WARNING) << "[Source::CreateCameraSource] Camera creation pending (attempt " << (MAX_CAMERA_RETRY_COUNT + 1) << ")";
            is_pending = true;
            break;
        }

        if (camera_is_ready && operation_success)
        {
            camera.state.reason = m_sourceInfo.result.reason;
            camera.state.errorCode = m_sourceInfo.result.errorCode;
            success = true;
            break;
        }
    }

    if (is_pending)
    {
        LOG(WARNING) << "[Source::CreateCameraSource] Camera creation pending after all attempts";
        return false;
    }

    LOG(INFO) << "[Source::CreateCameraSource] source_id: " << source_id << ", camera info: " << camera.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateAnalogSource(const std::string& source_id)
{
    ANALOG_SOURCE analog = std::get<ANALOG_SOURCE>(m_sourceInfo.source);
    bool          success = g_sdkController->CreateCaptureCardSource(source_id, analog, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateAnalogSource] CreateCaptureCardSource failed";
    LOG(INFO) << "[Source::CreateAnalogSource] source_id: " << source_id << ", analog info: " << analog.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateBytelinkSource(const std::string& source_id)
{
    BYTELINK_SOURCE bytelink = std::get<BYTELINK_SOURCE>(m_sourceInfo.source);
    bool            success = g_sdkController->CreateMobileProjectorSource(source_id, bytelink, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateBytelinkSource] CreateMobileProjectorSource failed";
    LOG(INFO) << "[Source::CreateBytelinkSource] source_id: " << source_id << ", bytelink info: " << bytelink.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateGraffitiSource(const std::string& source_id)
{
    GRAFFITI_SOURCE graffiti = std::get<GRAFFITI_SOURCE>(m_sourceInfo.source);
    bool            success = g_sdkController->CreateGraffitiSource(source_id, graffiti, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateGraffitiSource] CreateGraffitiSource failed";
    LOG(INFO) << "[Source::CreateGraffitiSource] source_id: " << source_id << ", graffiti info: " << graffiti.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::CreateRTCSource(const std::string& source_id)
{
    RTC_SOURCE rtc = std::get<RTC_SOURCE>(m_sourceInfo.source);
    bool       success = g_sdkController->CreateRTCSource(source_id, rtc, &m_sourceInfo.result);
    if (!success)
        LOG(ERROR) << "[Source::CreateRTCSource] CreateRTCSource failed!";
    LOG(INFO) << "[Source::CreateRTCSource] source_id: " << source_id << ", rtc info: " << rtc.toString() << ", result error_code: " << m_sourceInfo.result.errorCode << ", reason: " << static_cast<uint32_t>(m_sourceInfo.result.reason);
    return success;
}

bool Source::IsSyncSource()
{
    if (m_sourceInfo.type == VISUAL_VIRTUAL_CAMERA)
        return true;

    if (m_sourceInfo.type == VISUAL_BROWSER)
    {
        BROWSER_SOURCE browser = std::get<BROWSER_SOURCE>(m_sourceInfo.source);
        if (browser.token.empty() && browser.captureDLL.empty() && browser.deviceID == 0)
            return true;
    }

    return false;
}