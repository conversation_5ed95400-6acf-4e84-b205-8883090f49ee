#pragma once

#include <d3d11.h>
#include <wrl/client.h>
#include <stdint.h>
#include <thread>
#include <mutex>
#include <atomic>
#include <condition_variable>
#include "FrameGrab.h"
#include "SharedMemoryRingBuffer.h"
#include "ColorCorrectionFilter.h"

namespace v3
{
using namespace Microsoft::WRL;

class VideoFrameProcessor
{
public:
    bool GrabFrame(
        const std::string&  media_object_id,
        const std::string&  frame_id,
        ClipResizeOrderEnum clip_resize_order,
        FitModeEnum         fit_mode,
        int32_t             target_width,
        int32_t             target_height,
        int32_t             clip_x,
        int32_t             clip_y,
        int32_t             clip_z,
        int32_t             clip_w);

    void SetGrabFrameResultCallback(const GrabFrameResultCallback& cb);

    ColorCorrectionFilter* GetColorCorrectionFilter();

public:
    VideoFrameProcessor(std::condition_variable& tick_cv);
    ~VideoFrameProcessor();

    bool Init(ID3D11Device* sdk_dev, ID3D11DeviceContext* sdk_ctx, ID3D11Device* dev, ID3D11DeviceContext* ctx);
    void Uninit();

    using RenderTask = std::function<void(ID3D11Device* dev, ID3D11DeviceContext* ctx)>;
    void SubmitRenderTask(const RenderTask& task);

    using LoopRenderTask = std::function<void(ID3D11Device* dev, ID3D11DeviceContext* ctx, bool& need_continue)>;
    void SubmitLoopRenderTask(const LoopRenderTask& task);

    void OnHookVideoAPILayerRenderLoopBegin(int64_t                                                render_count,
                                            const std::map<uint32_t, std::vector<std::string>>&    canvas_map,
                                            const std::map<std::string, std::vector<std::string>>& canvas_item_map,
                                            const std::map<std::string, std::string>&              canvas_item_map2);
    void OnHookVideoAPILayerRenderLoopEnd();
    void OnCanvasItemOriginalTexture(const std::string& canvas_item_id, ID3D11Texture2D* texture);
    void OnCanvasItemFilteredTexture(const std::string& canvas_item_id, ID3D11Texture2D* texture);
    void OnPreviewTextureCallback(const int64_t sink_id, ID3D11Texture2D* texture);

    void OnWorkerThreadLoopTick(uint64_t tick_count);
    void OnRenderTaskTick();

private:
    bool OnGrabFrameSentCB(int64_t task_id, const std::string& frame_id, int64_t shared_handle, int64_t frame_width, int64_t frame_height);

private:
    std::thread             grab_frame_process_thread_;
    std::mutex              grab_frame_process_thread_mutex_;
    std::condition_variable grab_frame_process_thread_cv_;
    std::atomic_bool        grab_frame_process_thread_stop_ = true;

    void StartGrabFrameProcessThread();
    void StopGrabFrameProcessThread();
    void GrabFrameProcessThread();

private:
    std::condition_variable& tick_cv_;

    ID3D11Device*        sdk_dev_ = nullptr;
    ID3D11DeviceContext* sdk_ctx_ = nullptr;
    ID3D11Device*        dev_ = nullptr;
    ID3D11DeviceContext* ctx_ = nullptr;
    bool                 ready_ = false;

    FrameGrab         frame_grab_;
    DXGI_ADAPTER_DESC adapter_desc_{};

    ColorCorrectionFilter color_correction_filter_;

    SharedMemoryRingBuffer send_buffer_;
    SharedMemoryRingBuffer ack_buffer_;

    std::mutex              render_tasks_mutex_;
    std::vector<RenderTask> render_tasks_;
    std::vector<RenderTask> tmp_render_tasks_;

    using LoopRenderTaskPtr = std::shared_ptr<LoopRenderTask>;

    std::mutex                     loop_render_tasks_mutex_;
    std::vector<LoopRenderTaskPtr> loop_render_tasks_;
    std::vector<LoopRenderTaskPtr> tmp_loop_render_tasks_;
};
} // namespace v3