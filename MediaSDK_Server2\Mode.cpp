﻿#include "Mode.h"
#include "ModeSceneMgr.h"
#include "MediaMgr.h"
#include "MediaSDKControllerV2Impl.h"

extern media_mgr::MediaMgr* g_mediaMgr;

Mode::Mode(LIVE_MODE mode)
{
	m_modeInfo.id = mode;
}

Mode::~Mode() {}

void Mode::Select(bool in)
{
	if (in)
	{
		if (m_modeInfo.id != LIVE_MODE_DBCANVAS)
		{
            PREVIEW_INFO previewInfo;
            previewInfo.videoModel = GetVideoModel(0);
            if (!ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(previewInfo.videoModel))
			{
                ModeSceneMgr::GetInstance()->AddPreview(&previewInfo, nullptr);
			}
		}
		else
		{
			for (int i = 0; i < 2; i++)
			{
                PREVIEW_INFO previewInfo;
                previewInfo.videoModel = GetVideoModel(i);
				if (!ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(previewInfo.videoModel))
				{
                    ModeSceneMgr::GetInstance()->AddPreview(&previewInfo, nullptr);
				}
			}
		}
	}
	else
	{
		if (m_modeInfo.id != LIVE_MODE_DBCANVAS)
		{
			g_mediaMgr->DestroyVideoModel(GetVideoModel(0));
            UINT32 previewID = 0;
            PREVIEW_INFO previewInfo;
			if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(GetVideoModel(0), &previewID))
			{
				pView->GetPreviewInfo(&previewInfo);
                g_mediaMgr->DestroyPreview(previewID);
			}

			for (const auto& canvasID : previewInfo.canvasIDs)
			{
				if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
				{
                    pCanvas->RePreLoadCanvas(false);
				}
			}
		}
		else
		{
			for (int i = 0; i < 2; i++)
			{
				g_mediaMgr->DestroyVideoModel(GetVideoModel(i));
                UINT32 previewID = 0;
                PREVIEW_INFO previewInfo;
				if (Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(GetVideoModel(i), &previewID))
				{
                    pView->GetPreviewInfo(&previewInfo);
                    g_mediaMgr->DestroyPreview(previewID);
				}

				for (const auto& canvasID : previewInfo.canvasIDs)
                {
                    if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
                    {
                        pCanvas->RePreLoadCanvas(false);
                    }
                }
			}
		}
	}
}

UINT64 Mode::AddSceneWithInfo(SCENE_INFO* sceneInfo, const UINT64* id /*= NULL*/)
{
	UINT64 sceneID = ModeSceneMgr::GetInstance()->CreateScene(sceneInfo, id, (const LIVE_MODE*)&m_modeInfo.id);
	Scene* scene = ModeSceneMgr::GetInstance()->GetSceneByID(sceneID);
	AddScene(scene);
	return sceneID;
}

void Mode::AddScene(Scene* pScene)
{
	if (!pScene)
		return;
	pScene->SetModeID(m_modeInfo.id);
	m_scenes.push_back(pScene);
}

void Mode::RemoveScene(Scene* pScene)
{
	if (!pScene)
		return;

	if (pScene == m_curScene)
    {
        m_curScene->Select(false, SWITCH_SCENE_PRELOAD);
        m_curScene = 0;
    }
	else
	{
		pScene->Select(false, SWITCH_SCENE_PRELOAD);
	}

	auto it = std::find(m_scenes.begin(), m_scenes.end(), pScene);
	if (it != m_scenes.end())
		m_scenes.erase(it);
}

void Mode::SelectScene(UINT64 scene, bool reloadWhenSwitchModel)
{
	Scene* pScene = ModeSceneMgr::GetInstance()->GetSceneByID(scene);
	if (!pScene)
		return;

	if (this != ModeSceneMgr::GetInstance()->GetCurrentMode())
	{
		m_curScene = pScene;
		return;
	}

	if (reloadWhenSwitchModel && m_isExisted)
	{
		pScene->Select(true, SWITCH_SCENE_RELOAD);
	}
	else
	{
		if (m_curScene)
			m_curScene->Select(false, SWITCH_SCENE_NORMAL);
		pScene->Select(true, SWITCH_SCENE_NORMAL);
	}
	m_curScene = pScene;
	m_isExisted = true;
}

Scene* Mode::GetCurrentScene()
{
	return m_curScene;
}

void Mode::GetModeInfo(MODE_INFO_EX* info, bool child /*= false*/)
{
	info->id = m_modeInfo.id;
	if (child)
	{
		for (int i = 0; i < m_scenes.size(); i++)
		{
			SCENE_INFO_EX sceneInfo = {};
			m_scenes[i]->GetSceneInfo(&sceneInfo, true);
			info->scenes.push_back(sceneInfo);
			if (m_scenes[i] == m_curScene)
			{
				info->currentSceneID = sceneInfo.id;
			}
		}
	}
}

UINT32 Mode::GetVideoModel(int index)
{
	if (m_modeInfo.id == LIVE_MODE_LANDSCAPE)
	{
		return 0;
	}
	else if (m_modeInfo.id == LIVE_MODE_PORTRAIT)
	{
		return 1;
	}
	else if (m_modeInfo.id == LIVE_MODE_DBCANVAS && (index == 0 || index == 1))
	{
		return 2 + index;
	}

	return UINT32_MAX;
}