﻿#include "SourceMgr.h"
#include "AudioMgr.h"
#include "FilterMgr.h"
#include "ModeSceneMgr.h"
#include "stringutil.h"
#include "MediaSDKControllerV2Impl.h"
#include "event_bus.h"

#include <chrono>

extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;

#define MAKETRACKID(type) (((type) & 0x7f) << 56)
#define MAKEAUDIOID(source) ((source) & 0xffffffffffffffff)

extern CIEF::ICIEF* g_cief;
SourceMgr           g_sourceMgr;

SourceMgr* SourceMgr::GetInstance()
{
    return &g_sourceMgr;
}

SourceMgr::SourceMgr()
{
    // Initialize default configuration
    m_retryConfig.maxRetryCount = 6;
    m_retryConfig.totalTimeoutMs = 30000;
    m_retryConfig.timeout_1 = std::chrono::seconds(5);
    m_retryConfig.timeout_n = std::chrono::seconds(10);
    m_retryConfig.retryInterval = std::chrono::seconds(30);
}

SourceMgr::~SourceMgr()
{
}

void SourceMgr::Init()
{
    g_cief->GetThreadMgr()->AddTaskToThreadPool(new LS::MemberTask<SourceMgr>(this, &SourceMgr::StartTimer, 0));
    m_processCheck.Init();
}

void SourceMgr::Uninit()
{
    m_processCheck.Uninit();
}

UINT64 SourceMgr::AllocSourceID(UINT64 type)
{
    while (1)
    {
        USHORT sourceCounter = ::InterlockedIncrement16((SHORT*)&m_sourceCounter);
        if (sourceCounter == 0)
            sourceCounter = ::InterlockedIncrement16((SHORT*)&m_sourceCounter);

        UINT64                              sourceID = MAKETRACKID(type) | MAKEAUDIOID((UINT64)sourceCounter);
        std::map<UINT64, Source*>::iterator it = m_sources.find(sourceID);
        if (it == m_sources.end())
        {
            return sourceID;
        }
    }

    return -1;
}

Source* SourceMgr::GetSourceByID(UINT64 id)
{
    std::map<UINT64, Source*>::iterator it = m_sources.find(id);
    if (it != m_sources.end())
    {
        return it->second;
    }

    return NULL;
}

bool SourceMgr::FindSourceByID(UINT64 id)
{
    Source* pSource = GetSourceByID(id);
    if (pSource)
        return true;
    return false;
}

bool SourceMgr::CheckSourceExist(UINT64 id)
{
    Source* pSource = GetSourceByID(id);
    if (pSource)
    {
        SOURCE_INFO sourceInfo{};
        pSource->GetSourceInfo(&sourceInfo);
        if (sourceInfo.isCreated)
            return true;
    }

    return false;
}

void SourceMgr::GetSourceInfoByID(UINT64 id, SOURCE_INFO* info)
{
    Source* pSource = GetSourceByID(id);
    if (pSource)
    {
        pSource->GetSourceInfo(info);
    }
}

void SourceMgr::SetSourceInfoByID(UINT64 id, const SOURCE_INFO& info)
{
    Source* pSource = GetSourceByID(id);
    if (pSource)
    {
        pSource->SetSourceInfo(&info);
    }
}

UINT64 SourceMgr::CreateSource(SOURCE_INFO* info, const UINT64* id /*= NULL*/)
{
    if (info->type == VISUAL_CAMERA)
    {
        return CreateCameraSource(info, id);
    }
    else if (info->type == VISUAL_GAME)
    {
        return CreateGameSource(info, id);
    }
    else if (info->type == VISUAL_IMAGE && id != NULL && GetCompositeRealType(*id) == VISUAL_CAMERA)
    {
		auto& iter = m_compositeMetas.find(*id);
		if (iter != m_compositeMetas.end())
		{
			CAMERA_SOURCE camera = std::get<CAMERA_SOURCE>(iter->second.source);
			info->source = camera;
			DestroySourceObject(*id);
			return CreateCameraSource(info, id);
		}
    }

    return CreateNativeSource(info, id);
}

bool SourceMgr::ReopenSource(UINT64 sourceID, SOURCE_INFO* info)
{
    Source* pSource = GetSourceByID(sourceID);
    if (!pSource)
        return false;

    bool is_composite_camera = false;
    bool success = false;
    if (GetCompositeRealType(sourceID) == VISUAL_CAMERA)
    {
        CAMERA_SOURCE camera = std::get<CAMERA_SOURCE>(info->source);
        success = ReopenCameraSource(sourceID, camera);
        is_composite_camera = true;
    }
    else
    {
        success = pSource->ReopenSource(*info);
    }
    
    if (!success)
    {
        LOG(ERROR) << "[SourceMgr::ReopenSource] ReopenSource failed, sourceID: " << info->id;
        return false;
    }

    if (info->type == VISUAL_ANALOG || is_composite_camera)
    {
        for (const auto& layerID : info->layerIDs)
        {
            LAYER_INFO  layerInfo;
            ModeSceneMgr::GetInstance()->GetLayerInfoByID(layerID, &layerInfo);

            SOURCE_INFO sourceInfo;
            GetSourceInfoByID(layerInfo.sourceID, &sourceInfo);
            bool        isFallback = (sourceInfo.type == VISUAL_IMAGE ? true : false);

            std::string layerIDStr = "";
            Util::NumToString(layerID, &layerIDStr);
            OnLayerFallback(layerIDStr, info->result.reason == CREATE_SOURCE_FAILED_REASON::CREATE_NOT_FAIL, info->result.errorCode, info->type, info->result.reason, isFallback);
        }
    }

    return true;
}

UINT64 SourceMgr::CreateNativeSource(SOURCE_INFO* info, const UINT64* id /*= NULL*/)
{
    SOURCE_INFO& sourceInfo = *info;
    sourceInfo.id = id ? *id : AllocSourceID(info->type);

    Source* pSource = NULL;
    bool    useExist = false;
    if (FindSourceByID(sourceInfo.id))
    {
        pSource = GetSourceByID(sourceInfo.id);
        useExist = true;
    }
    else
    {
        pSource = new Source(sourceInfo);
    }

    if (pSource)
    {
        pSource->SetSourceInfo(&sourceInfo);
        bool isVISAudio = sourceInfo.type == VISUAL_FAV || sourceInfo.type == VISUAL_ANALOG || sourceInfo.type == VISUAL_CAMERA || sourceInfo.type == VISUAL_BYTELINK || sourceInfo.type == VISUAL_RTC;
        if (isVISAudio)
        {
            AudioMgr::GetInstance()->CreateVISAudio(&sourceInfo);
        }

        bool success = pSource->CreateSource();
        bool isSyncSource = pSource->IsSyncSource();
        if (success || isSyncSource)
        {
            if (isVISAudio)
            {
                if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(sourceInfo.id))
                    pAudio->UpdateVISAudio(sourceInfo.type);
            }

            if (!useExist)
                m_sources.insert(std::pair<UINT64, Source*>(sourceInfo.id, pSource));

            pSource->GetSourceInfo(&sourceInfo);
            sourceInfo.isCreated = success;
            pSource->SetSourceInfo(&sourceInfo);
            return sourceInfo.id;
        }
        else
        {
            LOG(ERROR) << "[SourceMgr::CreateSource] source create failed, sourceID: " << sourceInfo.id << ", sourceType: " << sourceInfo.type;
            m_sources.erase(sourceInfo.id);
            if (pSource)
                delete pSource;
        }
    }

    return 0;
}

void SourceMgr::DestroySource(UINT64 id)
{
    std::map<UINT64, Source*>::iterator it = m_sources.find(id);
    if (it != m_sources.end())
    {
        m_sources.at(id)->DestroySource();
        delete it->second;
        m_sources.erase(it);
    }

    std::unordered_map<UINT64, COMPOSITE_SOURCE_META>::iterator cit = m_compositeMetas.find(id);
    if (cit != m_compositeMetas.end())
    {
        m_compositeMetas.erase(cit);
    }
}

void SourceMgr::DestroySource(Source* source)
{
    for (auto it = m_sources.begin(); it != m_sources.end();)
    {
        if (it->second == source)
        {
            it = m_sources.erase(it);
            source->DestroySource();
            delete source;
        }
        else
        {
            ++it;
        }
    }
}

void SourceMgr::DestroySourceObject(UINT64 id)
{
	std::map<UINT64, Source*>::iterator it = m_sources.find(id);
	if (it != m_sources.end())
	{
		delete it->second;
		m_sources.erase(it);
	}
}

void SourceMgr::SetSourceNotRealCreated(UINT64 id)
{
    Source* pSource = GetSourceByID(id);
    if (pSource)
    {
        SOURCE_INFO sourceInfo{};
        pSource->GetSourceInfo(&sourceInfo);
        sourceInfo.isCreated = false;
        pSource->SetSourceInfo(&sourceInfo);
    }
}

UINT64 SourceMgr::CreateGameSource(SOURCE_INFO* info, const UINT64* id /*= NULL*/)
{
    GAME_SOURCE           game = std::get<GAME_SOURCE>(info->source);
    COMPOSITE_SOURCE_META meta;
    meta.primaryType = VISUAL_GAME;
    meta.source = game;

    WINDOW_SOURCE window{};
    window.windowDesc = game.windowDesc;
    window.implVer = game.implVer;

    SOURCE_INFO windowInfo = *info;
    windowInfo.type = VISUAL_WINDOW;
    windowInfo.source = window;
    UINT64 fallbackID = CreateNativeSource(&windowInfo);

    SOURCE_INFO gameInfo = *info;
    gameInfo.type = VISUAL_GAME;
    gameInfo.source = game;
    UINT64 gameSourceID = CreateNativeSource(&gameInfo);
    if (gameSourceID > 0)
    {
        m_processCheck.AddGameCrashDetect(gameSourceID, game.windowDesc.hwnd, game.windowDesc.szEXE);
    }

    if (fallbackID == 0)
    {
        LOG(ERROR) << "[SourceMgr::CreateGameSource] Create fallback window source failed";

        meta.primarySourceID = gameSourceID;
        meta.fallbackSourceID = 0;
        meta.isFallback = false;
        m_compositeMetas[gameSourceID] = meta;

        *info = gameInfo;
        return gameSourceID;
    }
    else
    {
        meta.primarySourceID = gameSourceID;
        meta.fallbackSourceID = fallbackID;
        meta.isFallback = true;
        m_compositeMetas[gameSourceID] = meta;

        *info = windowInfo;
        return fallbackID;
    }

    return 0;
}

UINT64 SourceMgr::CreateCameraSource(SOURCE_INFO* info, const UINT64* id /*= NULL*/)
{
    CAMERA_SOURCE         camera = std::get<CAMERA_SOURCE>(info->source);
    COMPOSITE_SOURCE_META meta;
    meta.primaryType = VISUAL_CAMERA;
    meta.source = camera;

    SOURCE_INFO sourceInfo = *info;
    sourceInfo.type = VISUAL_CAMERA;
    sourceInfo.source = camera;
    UINT64 sourceID = CreateNativeSource(&sourceInfo, id);

    if (sourceID > 0)
    {
        meta.primarySourceID = sourceID;
        m_compositeMetas[sourceID] = meta;
        *info = sourceInfo;
        return sourceID;
    }

    SOURCE_INFO   fallbackInfo = *info;
    MATERIAL_DESC imageDesc{};
    g_sdkController->GetMediaFileInfo(camera.placeholder, &imageDesc);
    IMAGE_SOURCE image{};
    image.materialDesc = imageDesc;
    fallbackInfo.source = image;
    fallbackInfo.type = VISUAL_IMAGE;
    UINT64 fallbackID = CreateNativeSource(&fallbackInfo, id);

    if (fallbackID > 0)
    {
        meta.fallbackSourceID = fallbackID;
        meta.isFallback = true;
        m_compositeMetas[fallbackID] = meta;
        *info = fallbackInfo;
        return fallbackID;
    }

    return 0;
}

bool SourceMgr::ReopenCameraSource(UINT64 sourceID, const CAMERA_SOURCE& newCamera)
{
    auto it = m_compositeMetas.find(sourceID);
    if (it == m_compositeMetas.end() || it->second.primaryType != VISUAL_CAMERA)
        return false;

    COMPOSITE_SOURCE_META& meta = it->second;
    const bool             isCurPrimary = (meta.primarySourceID == sourceID);
    const bool             isCurFallback = (meta.fallbackSourceID == sourceID);

    if (isCurPrimary)
    {
        Source* pSource = GetSourceByID(sourceID);
        if (!pSource)
            return false;

        SOURCE_INFO sourceInfo{};
        GetSourceInfoByID(sourceID, &sourceInfo);
        sourceInfo.source = newCamera;
        pSource->SetSourceInfo(&sourceInfo);

        if (pSource->ReopenSource(sourceInfo))
        {
            meta.source = newCamera;
            return true;
        }

        return ActivateFallback(meta, sourceID);
    }

    if (isCurFallback)
    {
        SOURCE_INFO newPrimaryInfo{};
        newPrimaryInfo.type = VISUAL_CAMERA;
        newPrimaryInfo.source = newCamera;

        if (UINT64 newPrimaryID = CreateNativeSource(&newPrimaryInfo))
        {
            TransferLayerBindingsWithTransform(sourceID, newPrimaryID);

            meta.primarySourceID = newPrimaryID;
            meta.fallbackSourceID = 0;
            meta.isFallback = false;
            meta.source = newCamera;

            m_compositeMetas[newPrimaryID] = meta;
            m_compositeMetas.erase(sourceID);

            DestroySource(sourceID);
            return true;
        }

        LOG(WARNING) << "[SourceMgr::ReopenCameraSource] New camera create failed, remain fall back";
        return true;
    }
    return false;
}

bool SourceMgr::ActivateFallback(COMPOSITE_SOURCE_META& meta, UINT64 failedSourceID)
{
    const CAMERA_SOURCE& camera = std::get<CAMERA_SOURCE>(meta.source);

    MATERIAL_DESC imageDesc{};
    g_sdkController->GetMediaFileInfo(camera.placeholder, &imageDesc);
    IMAGE_SOURCE image{imageDesc};

    SOURCE_INFO fallbackInfo{};
    fallbackInfo.type = VISUAL_IMAGE;
    fallbackInfo.source = image;

    if (UINT64 fallbackID = CreateNativeSource(&fallbackInfo))
    {
        TransferLayerBindingsWithTransform(failedSourceID, fallbackID);

        meta.fallbackSourceID = fallbackID;
        meta.isFallback = true;
        m_compositeMetas[fallbackID] = meta;
        m_compositeMetas.erase(failedSourceID);

        DestroySource(failedSourceID);
        return true;
    }
    return false;
}

void SourceMgr::TransferLayerBindingsWithTransform(UINT64 oldSourceID, UINT64 newSourceID)
{
    const std::vector<UINT64> layerIDs = GetLayersBySourceID(oldSourceID);
    for (UINT64 layerID : layerIDs)
    {
        Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
        if (!pLayer)
            continue;

        UpdateLayerBinding(layerID, newSourceID);

        SOURCE_INFO oldSourceInfo;
        GetSourceInfoByID(oldSourceID, &oldSourceInfo);

        SOURCE_INFO newSourceInfo;
        GetSourceInfoByID(newSourceID, &newSourceInfo);

        std::string layerIDStr = "";
        Util::NumToString(layerID, &layerIDStr);
        if (oldSourceInfo.type == VISUAL_CAMERA && newSourceInfo.type == VISUAL_IMAGE)
        {
            g_sdkController->MediaFilterDestroy(layerIDStr, "filter", FILTER_VISUAL, "");
        }
        else if (oldSourceInfo.type == VISUAL_IMAGE && newSourceInfo.type == VISUAL_CAMERA)
        {
            LAYER_INFO layerInfo;
            ModeSceneMgr::GetInstance()->GetLayerInfoByID(layerID, &layerInfo);
            g_sdkController->CreateEffectVisualFilter(layerIDStr, layerInfo);
        }
    }
}

std::vector<UINT64> SourceMgr::GetLayersBySourceID(UINT64 sourceID)
{
    SOURCE_INFO sourceInfo{};
    GetSourceInfoByID(sourceID, &sourceInfo);
    return sourceInfo.layerIDs;
}

void SourceMgr::UpdateLayerBinding(UINT64 layerID, UINT64 newSourceID)
{
    Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID);
    if (!pLayer)
        return;

    LAYER_INFO layerInfo{};
    pLayer->GetLayerInfo(&layerInfo);

    std::string layer_id = "";
    Util::NumToString(layerID, &layer_id);

    std::string old_source_id = "";
    Util::NumToString(layerInfo.sourceID, &old_source_id);

    Source* pOldSource = GetSourceByID(layerInfo.sourceID);
    SOURCE_INFO oldSourceInfo{};
    if (pOldSource)
    {
        pOldSource->GetSourceInfo(&oldSourceInfo);
        const auto& iter = std::find(oldSourceInfo.layerIDs.begin(), oldSourceInfo.layerIDs.end(), layerID);
        if (iter != oldSourceInfo.layerIDs.end())
        {
            oldSourceInfo.layerIDs.erase(iter);
            pOldSource->SetSourceInfo(&oldSourceInfo);
        }
    }

    SOURCE_INFO newSourceInfo{};
    Source*     pNewSource = GetSourceByID(newSourceID);
    if (pNewSource)
    {
        pNewSource->GetSourceInfo(&newSourceInfo);
        newSourceInfo.layerIDs.push_back(layerID);
        pNewSource->SetSourceInfo(&newSourceInfo);
    }

    layerInfo.sourceID = newSourceID;
    pLayer->SetLayerInfo(&layerInfo);

    if (newSourceInfo.size.Width > EPS && newSourceInfo.size.Height > EPS && (std::abs(oldSourceInfo.size.Width - newSourceInfo.size.Width) > EPS || std::abs(oldSourceInfo.size.Height && newSourceInfo.size.Height) > EPS))
    {
        pLayer->ReCalLayerTransform(newSourceInfo.size.Width, newSourceInfo.size.Height);
    }
    
    if (layerInfo.isCreated)
    {
        LOG(INFO) << "[SourceMgr::UpdateLayerBinding] layerID: " << layerID << ", new_source_id: " << newSourceID;

        std::string source_id = "";
        Util::NumToString(newSourceID, &source_id);

        LOG(INFO) << "[SourceMgr::UpdateLayerBinding] SetVisualDestroyedWhenAllReferenceRemoved old_source_id: " << old_source_id;
        if (!g_sdkController->SetVisualDestroyedWhenAllReferenceRemoved(old_source_id, false))
            LOG(ERROR) << "[SourceMgr::UpdateLayerBinding] SetVisualDestroyedWhenAllReferenceRemoved failed, layer_id: " << layer_id << ", old_source_id: " << old_source_id;
        
        LOG(INFO) << "[SourceMgr::UpdateLayerBinding] UpdateLayerBindSourceWithTransform layer_id: " << layer_id << ", source_id: " << source_id;
        pLayer->GetLayerInfo(&layerInfo);
        if (!g_sdkController->UpdateLayerBindSourceWithTransform(layer_id, source_id, layerInfo.transform))
            LOG(ERROR) << "[SourceMgr::UpdateLayerBinding] UpdateLayerBindSourceWithTransform failed, layer_id: " << layer_id << ", source_id: " << source_id;
    }
}

UINT64 SourceMgr::IsMatchedSource(SOURCE_INFO& sourceInfo)
{
    for (const auto& sourcePair : m_sources)
    {
        if (!sourcePair.second)
            continue;

        SOURCE_INFO mSourceInfo{};
        sourcePair.second->GetSourceInfo(&mSourceInfo);

        if (sourceInfo.id != mSourceInfo.id && CheckSourceExist(mSourceInfo.id))
        {
            if (mSourceInfo.type == VISUAL_CAMERA && (sourceInfo.type == VISUAL_CAMERA || sourceInfo.type == VISUAL_IMAGE))
            {
                CAMERA_SOURCE mCamera = std::get<CAMERA_SOURCE>(mSourceInfo.source);
                CAMERA_SOURCE camera;
                if (sourceInfo.type == VISUAL_CAMERA)
                {
                    camera = std::get<CAMERA_SOURCE>(sourceInfo.source);
                }
                else if (sourceInfo.type == VISUAL_IMAGE && m_compositeMetas[sourceInfo.id].primaryType == VISUAL_CAMERA)
                {
                    camera = std::get<CAMERA_SOURCE>(m_compositeMetas[sourceInfo.id].source); 
                }

                if ((camera.dshow.id == mCamera.dshow.id) && (camera.dshow.name == mCamera.dshow.name))
                {
                    sourceInfo.id = mSourceInfo.id;
                    return true;
                }
            }
            else if (mSourceInfo.type == VISUAL_BYTELINK && sourceInfo.type == VISUAL_BYTELINK)
            {
                BYTELINK_SOURCE bytelink = std::get<BYTELINK_SOURCE>(sourceInfo.source);
                BYTELINK_SOURCE mBytelink = std::get<BYTELINK_SOURCE>(mSourceInfo.source);
                if ((bytelink.name == mBytelink.name) && (bytelink.protocolType == mBytelink.protocolType))
                {
                    sourceInfo.id = mSourceInfo.id;
                    return true;
                }
            }
        }
    }

    return false;
}

std::unordered_map<UINT64, COMPOSITE_SOURCE_META> SourceMgr::GetCompositeMetas()
{
    return m_compositeMetas;
}

void SourceMgr::SetCompositeMetas(const std::unordered_map<UINT64, COMPOSITE_SOURCE_META>& metas)
{
    m_compositeMetas = metas;
}

VISUAL_TYPE SourceMgr::GetCompositeRealType(UINT64 sourceID)
{
    SOURCE_INFO sourceInfo{};
    GetSourceInfoByID(sourceID, &sourceInfo);
    VISUAL_TYPE type = sourceInfo.type;

    auto& compositeMetas = SourceMgr::GetInstance()->GetCompositeMetas();
    auto& iter = compositeMetas.find(sourceID);
    if (iter != compositeMetas.end())
    {
        type = iter->second.primaryType;
    }

    return type;
}

void SourceMgr::OnLayerFallback(const std::string& visual_id, bool success, uint32_t error_code, VISUAL_TYPE type, CREATE_SOURCE_FAILED_REASON failed_reason, bool fallbackToPlaceHolder)
{
    FallbackVisualEvent event{};
    event.visualID = visual_id;
    event.success = success;
    event.errCode = static_cast<INT32>(error_code);
    event.type = type;
    event.fallbackToPlaceHolder = fallbackToPlaceHolder;
    event.reason = failed_reason;
    eventbus::EventBus::PostEvent(event);
}

void SourceMgr::SetGameRetryConfig(const GameRetryConfig& config)
{
    std::lock_guard<std::mutex> lock(m_retryMutex);
    m_retryConfig = config;
    LOG(INFO) << "Game retry config updated: maxRetryCount=" << config.maxRetryCount
              << ", totalTimeoutMs=" << config.totalTimeoutMs
              << ", timeout_1=" << std::chrono::duration_cast<std::chrono::milliseconds>(config.timeout_1).count() << "ms"
              << ", timeout_n=" << std::chrono::duration_cast<std::chrono::milliseconds>(config.timeout_n).count() << "ms"
              << ", retryInterval=" << std::chrono::duration_cast<std::chrono::milliseconds>(config.retryInterval).count() << "ms";
}

GameRetryConfig SourceMgr::GetGameRetryConfig() const
{
    return m_retryConfig;
}

bool SourceMgr::CheckProcessExists(DWORD processId) const
{
    if (processId == 0)
        return false;
    
    HANDLE hProcess = OpenProcess(PROCESS_QUERY_LIMITED_INFORMATION, FALSE, processId);
    if (hProcess == NULL)
        return false;
    
    DWORD exitCode = 0;
    bool result = GetExitCodeProcess(hProcess, &exitCode) && exitCode == STILL_ACTIVE;
    CloseHandle(hProcess);
    return result;
}

bool SourceMgr::CheckWindowExists(HWND hwnd) const
{
    if (hwnd == NULL)
        return false;
    
    return IsWindow(hwnd) && IsWindowVisible(hwnd);
}

bool SourceMgr::FindProcessByExeName(const std::string& exeName, DWORD* outProcessId, HWND* outHwnd) const
{
    if (exeName.empty())
        return false;
    
    uint32_t pid = 0;
    bool result = ProcessCheck::FindGameProcessID(NULL, exeName, &pid);
    if (result && outProcessId)
    {
        *outProcessId = static_cast<DWORD>(pid);
    }
    return result;
}

void SourceMgr::NotifyGameProcessChanged(UINT64 sourceID, bool exists, DWORD processID, HWND hwnd)
{
    std::lock_guard<std::mutex> lock(m_retryMutex);
    auto it = m_gameRetryContexts.find(sourceID);
    if (it == m_gameRetryContexts.end())
        return;
    
    GameRetryContext& ctx = it->second;
    bool wasExisting = ctx.processCaptureExists;
    ctx.processCaptureExists = exists;
    
    if (exists)
    {
        ctx.processID = processID;
        ctx.hwnd = hwnd;
        
        if (!wasExisting)
        {
            LOG(INFO) << "[SourceMgr::NotifyGameProcessChanged] Game process reappeared for sourceID: " << sourceID << ", processID: " << processID << ", exeName: " << ctx.exeName;
            // If process reappears while in waiting state, trigger immediate retry
            if (ctx.state == GAME_CAPTURE_STATE::GAME_CAPTURE_WAIT)
            {
                ctx.state = GAME_CAPTURE_STATE::GAME_CAPTURE_START;
                ctx.lastStateChangeTime = std::chrono::steady_clock::now();
                LOG(INFO) << "[SourceMgr::NotifyGameProcessChanged] Detected game process restart for sourceID: " << sourceID << ", triggering immediate retry";
            }
        }
    }
    else 
    {
        if (wasExisting)
        {
            LOG(INFO) << "[SourceMgr::NotifyGameProcessChanged] Game process disappeared for sourceID: " << sourceID << ", previous processId: " << ctx.processID << ", exeName: " << ctx.exeName;
            ctx.notifiedCaptureType = false;
        }
    }
}

void SourceMgr::StartGameRetry(UINT64 sourceID, const GameRetryContext& ctx)
{
    std::lock_guard<std::mutex> lock(m_retryMutex);
    GameRetryContext newCtx = ctx;
    
    // Initialize new fields
    newCtx.state = GAME_CAPTURE_STATE::GAME_CAPTURE_START;
    newCtx.lastStateChangeTime = std::chrono::steady_clock::now();
    newCtx.lastProcessCheckTime = newCtx.lastStateChangeTime;
    newCtx.processCaptureExists = true;  // Initially assume process exists
    newCtx.notifiedCaptureType = false;
    newCtx.recoveryAttempted = false;
    
    // Get game window and process information
    auto& compositeMetas = GetCompositeMetas();
    auto iter = compositeMetas.find(sourceID);
    if (iter != compositeMetas.end())
    {
        GAME_SOURCE game = std::get<GAME_SOURCE>(iter->second.source);
        newCtx.hwnd = game.windowDesc.hwnd;
        GetWindowThreadProcessId(newCtx.hwnd, &newCtx.processID);
        newCtx.exeName = game.windowDesc.szEXE;
        LOG(INFO) << "[SourceMgr::StartGameRetry] Starting game retry for sourceID: " << sourceID << ", hwnd: " << newCtx.hwnd << ", processId: " << newCtx.processID << ", exeName: " << newCtx.exeName;
    }
    else
    {
        LOG(WARNING) << "[SourceMgr::StartGameRetry] StartGameRetry called for sourceID " << sourceID << " but meta not found";
    }
    
    m_gameRetryContexts[sourceID] = newCtx;
}

void SourceMgr::StopGameRetry(UINT64 sourceID)
{
    std::lock_guard<std::mutex> lock(m_retryMutex);
    m_gameRetryContexts.erase(sourceID);
}

bool SourceMgr::GetRetryContext(UINT64 sourceID, GameRetryContext* outCtx)
{
    std::lock_guard<std::mutex> lock(m_retryMutex);
    auto                        it = m_gameRetryContexts.find(sourceID);
    if (it == m_gameRetryContexts.end())
        return false;
    *outCtx = it->second;
    return true;
}

void SourceMgr::TriggerGameRetry(UINT64 sourceID)
{
    GameRetryContext ctx;
    if (!GetRetryContext(sourceID, &ctx))
        return;

    const auto now = std::chrono::steady_clock::now();
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(now - ctx.firstFailureTime).count();
    if (elapsedMs > m_retryConfig.totalTimeoutMs)
    {
        LOG(WARNING) << "[SourceMgr::TriggerGameRetry] Game source retry timeout exceeded: " << sourceID << " (" << elapsedMs << "ms > " << m_retryConfig.totalTimeoutMs << "ms)";
        StopGameRetry(sourceID);
        return;
    }

    // State and timeout by attempt count
    {
        std::lock_guard<std::mutex> lock(m_retryMutex);
        if (auto it = m_gameRetryContexts.find(sourceID); it != m_gameRetryContexts.end())
        {
            // Update attempt count
            it->second.attemptCount++;
            // Check if arrived max retry count
            if (it->second.attemptCount > m_retryConfig.maxRetryCount)
            {
                it->second.state = GAME_CAPTURE_STATE::GAME_CAPTURE_STOP;
                LOG(INFO) << "[SourceMgr::TriggerGameRetry] Max retry attempts reached for sourceID: " << sourceID << " (" << it->second.attemptCount << " > " << m_retryConfig.maxRetryCount << ")";
                return;
            }
            
            // Set to waiting next retry state
            it->second.state = GAME_CAPTURE_STATE::GAME_CAPTURE_WAIT;
            it->second.lastStateChangeTime = now;
            LOG(INFO) << "[SourceMgr::TriggerGameRetry] Scheduled game retry for sourceID: " << sourceID << " (attempt " << it->second.attemptCount << "/" << m_retryConfig.maxRetryCount << ")";
        }
    }
}

void SourceMgr::StartTimer(void* param)
{
    StopTimer();

    ITimerMgr* timerMgr = reinterpret_cast<ITimerMgr*>(g_cief->QueryInterface(LSINAME_TIMERMGR));
    uint64_t   counter = m_timerCounter++;
    uint64_t   timerID = timerMgr->SetTimer(counter, 1000, this);
    if (timerID != 0)
    {
        m_timerID = timerID;
    }
}

void SourceMgr::StopTimer()
{
    if (m_timerID != 0)
    {
        ITimerMgr* timerMgr = reinterpret_cast<ITimerMgr*>(g_cief->QueryInterface(LSINAME_TIMERMGR));
        timerMgr->KillTimer(m_timerID);
        m_timerID = 0;
    }
}

void SourceMgr::HandleTimer(UINT64 timer_id)
{
    // Only handle our own periodic timer
    if (timer_id != m_timerID)
    {
        LOG(WARNING) << "[SourceMgr::HandleTimer] Unknown timer ID: " << timer_id;
        return;
    }
    
    // Create background task to process all game sources
    auto handler = [this]() {
        const auto now = std::chrono::steady_clock::now();
        
        // Process each game source that needs retry
        std::vector<UINT64> sourcesToProcess;
        {
            std::lock_guard<std::mutex> lock(m_retryMutex);
            for (const auto& pair : m_gameRetryContexts) {
                sourcesToProcess.push_back(pair.first);
            }
        }
        
        for (UINT64 sourceID : sourcesToProcess)
        {
            ProcessGameSource(sourceID, now);
        }
    };
    
    auto task = m_taskMgr.CreateThreadTask(handler);
    g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_IMMEDIATE);
    
    // Reset periodic timer
    StartTimer(nullptr);
}

void SourceMgr::ProcessGameSource(UINT64 sourceID, std::chrono::steady_clock::time_point now)
{
    GameRetryContext ctx;
    if (!GetRetryContext(sourceID, &ctx))
        return;
    
    auto& compositeMetaMap = GetCompositeMetas();
    auto iter = compositeMetaMap.find(sourceID);
    if (iter == compositeMetaMap.end())
    {
        LOG(WARNING) << "[SourceMgr::ProcessGameSource] ProcessGameSource: sourceID " << sourceID << " not found in composite metas, stopping retry";
        StopGameRetry(sourceID);
        return;
    }
    
    // Check process status periodically (every 1 second)
    auto currentTime = std::chrono::steady_clock::now();
    auto elapsedSinceLastCheck = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - ctx.lastProcessCheckTime).count();
    if (elapsedSinceLastCheck > 1000)
    {
        bool processExists = CheckProcessExists(ctx.processID);
        bool windowExists = CheckWindowExists(ctx.hwnd);
        // Update process-related status
        if (ctx.processCaptureExists && (!processExists || !windowExists))
        {
            LOG(INFO) << "[SourceMgr::ProcessGameSource] Game process/window no longer exists for sourceID: " << sourceID << ", processId: " << ctx.processID << ", processExists: " << (processExists ? "true" : "false") << ", windowExists: " << (windowExists ? "true" : "false");
            NotifyGameProcessChanged(sourceID, false, 0, NULL);
            
            // Try to find if game has restarted (based on exe name)
            if (!ctx.exeName.empty() && !ctx.recoveryAttempted)
            {
                DWORD newPID = 0;
                HWND newHWND = NULL;
                if (FindProcessByExeName(ctx.exeName, &newPID, &newHWND) && newPID != ctx.processID)
                {
                    LOG(INFO) << "[SourceMgr::ProcessGameSource] Found restarted game process for sourceID: " << sourceID << ", exeName: " << ctx.exeName << ", new processId: " << newPID;
                    NotifyGameProcessChanged(sourceID, true, newPID, newHWND);
                    ctx.recoveryAttempted = true;  // Mark recovery attempted
                }
            }
        }
        else if (!ctx.processCaptureExists)
        {
            // If process doesn't exist, periodically check if it has restarted
            DWORD newPID = 0;
            HWND newHWND = NULL;
            if (!ctx.exeName.empty() && FindProcessByExeName(ctx.exeName, &newPID, &newHWND))
            {
                LOG(INFO) << "[SourceMgr::ProcessGameSource] Detected restarted game process for sourceID: " << sourceID << ", exeName: " << ctx.exeName << ", new processId: " << newPID;
                NotifyGameProcessChanged(sourceID, true, newPID, newHWND);
            }
        }
        
        std::lock_guard<std::mutex> lock(m_retryMutex);
        if (auto it = m_gameRetryContexts.find(sourceID); it != m_gameRetryContexts.end())
        {
            it->second.lastProcessCheckTime = currentTime;
        }
    }
    
    // If game capture is successful, no further processing needed
    if (iter->second.isFallback == false)
        return;
    
    // Process game source based on state machine
    switch (ctx.state)
    {
    case GAME_CAPTURE_STATE::GAME_CAPTURE_START:
	{
		// Check for timeout
		auto timeout = (ctx.attemptCount <= 1) ? m_retryConfig.timeout_1 : m_retryConfig.timeout_n;
		auto timeoutMs = std::chrono::duration_cast<std::chrono::milliseconds>(timeout).count();

		auto currentTime = std::chrono::steady_clock::now();
		auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - ctx.lastStateChangeTime).count();
		if (elapsedMs > timeoutMs)
		{
			// Timeout, transition to waiting retry state
			std::lock_guard<std::mutex> lock(m_retryMutex);
			if (auto it = m_gameRetryContexts.find(sourceID); it != m_gameRetryContexts.end())
			{
				it->second.state = GAME_CAPTURE_STATE::GAME_CAPTURE_WAIT;
				it->second.lastStateChangeTime = currentTime;
				LOG(INFO) << "[SourceMgr::ProcessGameSource] Game capture timed out for sourceID: " << sourceID << " after " << timeoutMs << "ms, entering retry wait state";
			}
		}
	}
	break;
    case GAME_CAPTURE_STATE::GAME_CAPTURE_WAIT:
    {
        // Check if it's time to retry
        auto retryIntervalMs = std::chrono::duration_cast<std::chrono::milliseconds>(m_retryConfig.retryInterval).count();
        
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - ctx.lastStateChangeTime).count();
        if (elapsedMs > retryIntervalMs)
        {
            // Check if process exists
            bool processExists = CheckProcessExists(ctx.processID);
            
            if (!processExists)
            {
                // Process doesn't exist, continue waiting
                std::lock_guard<std::mutex> lock(m_retryMutex);
                if (auto it = m_gameRetryContexts.find(sourceID); it != m_gameRetryContexts.end())
                {
                    it->second.lastStateChangeTime = currentTime;
                    LOG(INFO) << "[SourceMgr::ProcessGameSource] Game process does not exist for sourceID: " << sourceID << ", continuing to wait for process restart";
                }
                break;
            }
            
            // Increment retry count
            std::lock_guard<std::mutex> lock(m_retryMutex);
            if (auto it = m_gameRetryContexts.find(sourceID); it != m_gameRetryContexts.end())
            {
                it->second.attemptCount++;
                
                // Check if max retry count reached
                if (it->second.attemptCount > m_retryConfig.maxRetryCount)
                {
                    it->second.state = GAME_CAPTURE_STATE::GAME_CAPTURE_STOP;
                    LOG(INFO) << "[SourceMgr::ProcessGameSource] Max retry attempts reached for sourceID: " << sourceID << ", stopping retry";
                }
                else
                {
                    // Restart capture
                    it->second.state = GAME_CAPTURE_STATE::GAME_CAPTURE_START;
                    it->second.lastStateChangeTime = currentTime;
                    // Get game source info and try to create new source
                    GAME_SOURCE game = std::get<GAME_SOURCE>(iter->second.source);
                    
                    LOG(INFO) << "[SourceMgr::ProcessGameSource] Attempting game capture retry " << it->second.attemptCount << "/" << m_retryConfig.maxRetryCount << " for sourceID: " << sourceID;
                    // Create temporary thread to avoid calling potentially blocking functions while holding lock
                    auto retryTask = [this, sourceID, game]() {
                        SOURCE_INFO gameInfo{};
                        gameInfo.type = VISUAL_GAME;
                        gameInfo.source = game;
                        
                        if (UINT64 newID = CreateSource(&gameInfo, nullptr))
                        {
                            TransferLayerBindingsWithTransform(sourceID, newID);
                            
                            std::lock_guard<std::mutex> metaLock(m_retryMutex);
                            auto& compositeMetas = GetCompositeMetas();
                            auto iter = compositeMetas.find(sourceID);
                            if (iter != compositeMetas.end())
                            {
                                iter->second.primarySourceID = newID;
                                compositeMetas[newID] = iter->second;
                                compositeMetas.erase(sourceID);
                                
                                LOG(INFO) << "[SourceMgr::ProcessGameSource] Game recovery succeeded: old sourceID=" << sourceID << ", new sourceID=" << newID;
                            }
                            
                            StopGameRetry(sourceID);
                        }
                        else
                        {
                            LOG(WARNING) << "[SourceMgr::ProcessGameSource] Game capture retry failed for sourceID: " << sourceID;
                        }
                    };
                    
                    auto task = m_taskMgr.CreateThreadTask(retryTask);
                    g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_IMMEDIATE);
                }
            }
        }
    }
    break;
    case GAME_CAPTURE_STATE::GAME_CAPTURE_STOP:
        // No operation in stop state
        break;
    }
}
