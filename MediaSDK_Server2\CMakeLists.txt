#cpp version
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)

#project
set(PROJECT_NAME MediaSDK_Server)

#source
file(GLOB_RECURSE SOURCE_SET
     "*.h"
     "*.hpp"
     "*.cpp"
	 "MediaSDK_Server.rc"

)

#V2 Header
file(GLOB V2Header_SET
     "mediasdk_v2_header/*.h"
     "mediasdk_v2_header/*.hpp"
)
source_group("Header Files\\mediasdk_v2_header" FILES ${V2Header_SET})

file(GLOB PLUGINS "mediasdk_v2_header/plugins/*.h")
source_group("Header Files\\mediasdk_v2_header\\plugins" FILES ${PLUGINS})

file(GLOB JSONHPP "mediasdk_v2_header/nlohmann/*.hpp")
source_group("Header Files\\mediasdk_v2_header\\nlohmann" FILES ${JSONHPP})

file(GLOB BASEHPP "mediasdk_v2_header/base/*.hpp")
source_group("Header Files\\mediasdk_v2_header\\base" FILES ${BASEHPP})

file(GLOB V3HOOKAPILAYER_SOURCE_SET
    "V3HookAPILayer/*.h"
    "V3HookAPILayer/*.cpp"
)
source_group("Source Files\\V3HookAPILayer" FILES ${V3HOOKAPILAYER_SOURCE_SET})

#sdk helper
file(GLOB NativeBridge_Headers_V2
     "NativeBridge/*.h"
     "NativeBridge/*.hpp"
)
source_group("Header Files\\NativeBridge" FILES ${NativeBridge_Headers_V2})

file(GLOB NativeBridge_Sources_V2
     "NativeBridge/*.cpp"
)
source_group("Source Files\\NativeBridge" FILES ${NativeBridge_Sources_V2})

#target
add_executable(${PROJECT_NAME} ${SOURCE_SET})
set_target_properties(${PROJECT_NAME}
	PROPERTIES
		FOLDER "LSNative"
)

#include
if(CMAKE_SIZEOF_VOID_P EQUAL 8)
	set(_lib_suffix 64)
else()
	set(_lib_suffix 32)
endif()
target_include_directories(${PROJECT_NAME} PRIVATE
		${CMAKE_CURRENT_SOURCE_DIR}/../Dependencies/Win32/${_lib_suffix}/include
		${CMAKE_CURRENT_SOURCE_DIR}/../Dependencies/Win32/${_lib_suffix}/include/cief
		${CMAKE_CURRENT_SOURCE_DIR}/../BaseLib
        ${CMAKE_CURRENT_SOURCE_DIR}/../BaseLib
        ${CMAKE_CURRENT_SOURCE_DIR}/mediasdk_v2_header
        ${CMAKE_CURRENT_SOURCE_DIR}/mediasdk_v2_header/plugins
        ${CMAKE_CURRENT_SOURCE_DIR}/../DirectXTK/Inc
        ${CMAKE_CURRENT_SOURCE_DIR}/../DirectXTex/DirectXTex
		${PARFAIT_INCLUDE_DIR}
		${CASTMATE_INCLUDE_DIRS}
)
target_include_directories(${PROJECT_NAME} PUBLIC .)

#lib
set(ADDITIONAL_LIBRARY_DEPENDENCIES
    "pdh;"
    "version;"
    "Rpcrt4;"
    "D3D11.lib;"
    ${JSONCPP_LIB}
    ${DETOURS_LIB}
	${CASTMATE_LIBRARIES}
    ${CMAKE_CURRENT_SOURCE_DIR}/../Dependencies/Win32/${_lib_suffix}/lib/swresample.lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../Dependencies/Win32/${_lib_suffix}/lib/avutil.lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../Dependencies/Win32/${_lib_suffix}/lib/avcodec.lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../Dependencies/Win32/${_lib_suffix}/lib/avformat.lib
)
target_link_libraries(${PROJECT_NAME} PRIVATE
	${ADDITIONAL_LIBRARY_DEPENDENCIES}
	IPCFramework
    TinyFramework
	Util
	BaseLib
	ProtocBridge
	dxgi
    DirectXTK
    DirectXTex
    d3dcompiler.lib
)

use_props(${PROJECT_NAME} "${CMAKE_CONFIGURATION_TYPES}" "${DEFAULT_CXX_PROPS}")
set(ROOT_NAMESPACE MediaSDKServer)

set_target_properties(${PROJECT_NAME} PROPERTIES
    VS_GLOBAL_KEYWORD "Win32Proj"
)

#compile and link
target_precompile_headers(${PROJECT_NAME} PRIVATE
    "$<$<COMPILE_LANGUAGE:CXX>:${CMAKE_CURRENT_SOURCE_DIR}/stdafx.h>"
)

if("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "ARM")
    set_target_properties(${PROJECT_NAME} PROPERTIES
        INTERPROCEDURAL_OPTIMIZATION_DEBUG_MBCS   "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE_MBCS "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE_MT   "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE      "FALSE"
    )
elseif("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "Win32")
    set_target_properties(${PROJECT_NAME} PROPERTIES
        INTERPROCEDURAL_OPTIMIZATION_RELEASE_MBCS "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE_MT   "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE      "FALSE"
    )
elseif("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "x64")
    set_target_properties(${PROJECT_NAME} PROPERTIES
        INTERPROCEDURAL_OPTIMIZATION_RELEASE_MBCS "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE_MT   "FALSE"
        INTERPROCEDURAL_OPTIMIZATION_RELEASE      "FALSE"
    )
endif()

target_compile_definitions(${PROJECT_NAME} PRIVATE
    "$<$<CONFIG:Debug_MBCS>:_DEBUG;STB_IMAGE_WRITE_IMPLEMENTATION>"
    "$<$<CONFIG:Debug>:_DEBUG>;STB_IMAGE_WRITE_IMPLEMENTATION"
    "$<$<CONFIG:Release_MBCS>:NDEBUG;_USRDLL;STB_IMAGE_WRITE_IMPLEMENTATION>"
    "$<$<CONFIG:Release_MT>:NDEBUG;_USRDLL;STB_IMAGE_WRITE_IMPLEMENTATION>"
    "$<$<CONFIG:Release>:NDEBUG;_USRDLL;STB_IMAGE_WRITE_IMPLEMENTATION>"
    "$<$<BOOL:${ENABLE_GPL}>:ENABLE_GPL>"
    "$<$<BOOL:${TRACY_ENABLE}>:TRACY_ENABLE>"
    "$<$<BOOL:${ZMQ_ENABLE}>:ZMQ_ENABLE>"
    "$<$<BOOL:${ENABLE_DEVTEST}>:ENABLE_DEVTEST>"
    "$<$<BOOL:${SPOUT_ENABLE}>:SPOUT_ENABLE>"
    "$<$<BOOL:${ENABLE_DXLOCK_CHECK}>:ENABLE_DXLOCK_CHECK>"
    "$<$<BOOL:${ENABLE_EFFECT}>:ENABLE_EFFECT>"
    "$<$<BOOL:${ENABLE_CEF}>:ENABLE_CEF>"
    "$<$<BOOL:${ENABLE_DEBUG_SDK_VERSION}>:ENABLE_DEBUG_SDK_VERSION>"
    "WIN32;"
    "_WINDOWS;"
    "MEDIASDKNODE_EXPORTS;"
    "_CRT_SECURE_NO_WARNINGS;"
    "_WINSOCK_DEPRECATED_NO_WARNINGS;"
    "STB_IMAGE_IMPLEMENTATION;"
    "WIN32_LEAN_AND_MEAN;"
    "NO_CRYPTO;"
    "USING_UV_SHARED=1;"
    "USING_V8_SHARED=1;"
    "V8_DEPRECATION_WARNINGS=1;"
    "V8_31BIT_SMIS_ON_64BIT_ARCH;"
    "V8_REVERSE_JSARGS;"
    "BUILDING_NODE_EXTENSION;"
    "V8_ENABLE_CHECKS;"
    "EGL_EGL_PROTOTYPES=0;"
    "GL_GLES_PROTOTYPES=0;"
    "_HAS_EXCEPTIONS=1;"
    "_CRT_SECURE_NO_DEPRECATE;"
    "_CRT_NONSTDC_NO_DEPRECATE;"
    "OPENSSL_NO_PINSHARED;"
    "OPENSSL_THREADS;"
    "MFX_VA;"
    "_ALLOW_MSC_VER_MISMATCH;"
    "_ALLOW_ITERATOR_DEBUG_LEVEL_MISMATCH;"
    "_ALLOW_RUNTIME_LIBRARY_MISMATCH;"
    "UNICODE;"
    "_UNICODE"
)

if(MSVC)
    if("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "ARM")
        target_compile_options(${PROJECT_NAME} PRIVATE
            "$<$<CONFIG:Debug>:/MDd>"
            "$<$<CONFIG:Release>:/MD>"
        )
    elseif("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "Win32")
        target_compile_options(${PROJECT_NAME} PRIVATE
            "$<$<CONFIG:Debug>:/MDd>"
            "$<$<CONFIG:Release>:/MD>" 
        )
    elseif("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "x64")
        target_compile_options(${PROJECT_NAME} PRIVATE
            "$<$<CONFIG:Debug>:/MDd>"
            "$<$<CONFIG:Release>:/MD>" 
        )
    endif()
endif()

if(MSVC)
    if("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "ARM")
        target_compile_options(${PROJECT_NAME} PRIVATE
            "$<$<CONFIG:Debug>:/MDd>"
            "$<$<CONFIG:Release>:/MD>"
        )
    elseif("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "Win32")
        target_compile_options(${PROJECT_NAME} PRIVATE
            "$<$<CONFIG:Debug>:/MDd>"
            "$<$<CONFIG:Release>:/MD>" 
        )
    elseif("${CMAKE_VS_PLATFORM_NAME}" STREQUAL "x64")
        target_compile_options(${PROJECT_NAME} PRIVATE
            "$<$<CONFIG:Debug>:/MDd>"
            "$<$<CONFIG:Release>:/MD>" 
        )
    endif()
   target_link_options(${PROJECT_NAME} PRIVATE 
                "/OPT:REF;"
                "/OPT:ICF;"
                "/INCREMENTAL:NO")
endif()

if(MSVC)
  add_definitions(/MP)
endif()

target_link_options(${PROJECT_NAME} PRIVATE
    $<$<CONFIG:Debug>:
        /INCREMENTAL;
        /WHOLEARCHIVE:TinyFramework;
        /DELAYLOAD:avutil.dll;
        /DELAYLOAD:swresample.dll;
        /DELAYLOAD:avcodec.dll;
        /DELAYLOAD:avformat.dll;
    >
    $<$<CONFIG:Release>:
        /OPT:REF;
        /OPT:ICF;
        /INCREMENTAL:NO;
        /WHOLEARCHIVE:TinyFramework;
        /DELAYLOAD:avutil.dll;
        /DELAYLOAD:swresample.dll;
        /DELAYLOAD:avcodec.dll;
        /DELAYLOAD:avformat.dll;
    >
    /NODEFAULTLIB:libcmt.lib;
    /SUBSYSTEM:WINDOWS;
)

#output
install_datatarget(${PROJECT_NAME} ".")
