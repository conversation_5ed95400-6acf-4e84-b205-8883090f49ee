#pragma once

#include "Filter.h"
#include "systemutil.h"

class FilterMgr
{
public:
	static FilterMgr* GetInstance();
	FilterMgr();
	~FilterMgr();

	UINT64 AllocFilterID(UINT64 type);
    void   BindObject(UINT64 id, void* object);
    void   UnBindObject(UINT64 id);

    Filter* GetFilterByID(UINT64 id);
    UINT64  CreateFilter(const FILTER* info, const UINT64* id = NULL);
    void    DeleteFilter(Filter* filter);
    void    SetFilterInfo(FILTER* info);

protected:
    UINT m_filterCounter = Util::GetMicrosecondTimestamp();
    std::vector<Filter*> m_filters{};
    std::map<UINT64, void*> m_bindObject{};
};