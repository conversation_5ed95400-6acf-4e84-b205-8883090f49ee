// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_audio;
import "ls_base.proto";

enum AUDIO_INPUT_TYPE
{
    AUDIO_INPUT_NONE = 0;
    AUDIO_INPUT_MICROPHONE = 1;
    AUDIO_INPUT_LOOPBACK = 2;
	AUDIO_INPUT_ANALOG = 3;
    AUDIO_INPUT_FAV = 4;
    AUDIO_INPUT_RTC = 5;
    AUDIO_INPUT_WIRELINE_IOS = 6;
    AUDIO_INPUT_WIRELINE_ANDROID = 7;
    AUDIO_INPUT_WIRELESS_IOS = 8;
    AUDIO_INPUT_WIRELESS_ANDROID = 9;
    AUDIO_INPUT_PCM = 10;
    AUDIO_INPUT_FF = 11;
    AUDIO_INPUT_BROWSER = 12;
    AUDIO_INPUT_APP = 13;
};

enum AUDIO_MONITOR_TYPE
{
    AUDIO_MONITOR_NONE = 0;
    AUDIO_MONITOR_STREAM = 1;            // output to stream
    AUDIO_MONITOR_DEVICE = 2;            // output to device
    AUDIO_MONITOR_STREAM_AND_DEVICE = 3; // output to stream and device
};

enum AUDIO_TYPE
{
    AUDIO_NONE = 0;
	AUDIO_WAS = 1;
    AUDIO_APP = 2;
	AUDIO_PCM = 3;
	AUDIO_VIS = 4;
};

enum AUDIO_TRACK
{
	AUDIO_TRACK_UNKNOWN = 0;
    AUDIO_TRACK_0 = 0x01;
    AUDIO_TRACK_1 = 0x02;
    AUDIO_TRACK_2 = 0x04;
    AUDIO_TRACK_3 = 0x08;
    AUDIO_TRACK_4 = 0x10;
    AUDIO_TRACK_5 = 0x20;
};

enum BYPASS_SYSTEM_ENHANCEMENT_MODE
{
	ENHANCEMENT_CLOSE = 0;
    ENHANCEMENT_OPEN = 1;
    ENHANCEMENT_AUTO = 2;
};

enum MICROPHONE_BOOST_GAIN_PROPORTION
{
    PROPORTION_ORIGINAL = 0;
    PROPORTION_0 = 1;
    PROPORTION_25 = 2;
    PROPORTION_50 = 3;
    PROPORTION_75 = 4;
    PROPORTION_100 = 5;
};

enum AUDIO_ANS_OPTION
{
	ANS_OPTION_CLOSE = 0;
	ANS_OPTION_LOW = 1;
	ANS_OPTION_MEDIUM = 2;
	ANS_OPTION_HIGH = 3;
	ANS_OPTION_AUTO = 4;
};

enum AUDIO_AGC_OPTION
{
	AGC_OPTION_AUTO = 0;
	AGC_OPTION_CLOSE = 1;
	AGC_OPTION_OPEN = 2;
};

message AudioSettingParam {
    optional float volume = 1;					  // audio volume after process
	optional float balanceing = 2;				  // audio balance factor, 0.0~0.1, default 0.5
    optional uint32 sync_offset = 3;			  // audio timestamp offset to sync with video, in senconds
	optional uint32 interval = 4;				  // audio interval time
    optional AUDIO_MONITOR_TYPE monitor_type = 5; // audio output type: 0��none��1��stream��2��devices��3: all
    optional bool down_mix_mono = 6;			  // whether to mix all audio channels to mono
	optional bool mute = 7;						  // audio whether mute
}

message AudioCaptureParam {
	optional uint32 sample_per_sec = 1;
	optional uint32 bits_per_sec = 2;
	optional uint32 channels = 3;
	optional uint32 frames = 4;
	optional uint32 planes = 5;
	optional uint32 audio_format = 6;
	optional uint32 channel_layout = 7;
}

message AudioUniAttr {
	optional AUDIO_TYPE type = 1;
	optional uint32 audio_track = 2;
	optional ls_base.DShow device = 3;
	optional AudioSettingParam setting_param = 4;
	optional AudioCaptureParam capture_param = 5;
	optional string aec_ref_id = 6;
	optional bool enable_aec = 7;
	optional AUDIO_AGC_OPTION agc_option = 8;
	optional AUDIO_ANS_OPTION ans_option = 9;
}

message AudioBuffer {
	bytes buffer_l = 1;
	bytes buffer_r = 2;
}

message EnumAppAudio {
	message Request {
	}

	message Response {
		repeated ls_base.DShow audio_devices = 1;
	}
}

message EnumCaptureAudio {
	message Request {
	}

	message Response {
		repeated ls_base.DShow audio_devices = 1;
	}
}

message EnumRenderAudio {
	message Request {
	}

	message Response {
		repeated ls_base.DShow audio_devices = 1;
	}
}

message SystemSupportAppAudio {
	message Request {
	}

	message Response {
		bool support = 1;
	}
}

message GetDefaultInput {
	message Request {
	}
	
	message Response {
		ls_base.DShow audio_device = 1;
	}
}

message GetDefaultOutput {
	message Request {
	}
	message Response {
		ls_base.DShow audio_device = 1;
	}
}

message CreateWASAudio {
	message Request {
		AudioUniAttr audio_uni_attr = 1;
		AUDIO_INPUT_TYPE audio_input_type = 2;
		bool is_lyrax = 3;
		optional float mic_input_level = 4;                       //��Χ[0.0-1.0]
		BYPASS_SYSTEM_ENHANCEMENT_MODE sys_enhancement_mode = 5;  //bypass system enhancement, Ĭ���Զ�ģʽ���ر���ƵӲ����ǿ(0:Auto 1:Open 2:Close)
		MICROPHONE_BOOST_GAIN_PROPORTION mic_boost_level = 6;     //microphone boost gain proportion, Ĭ��proportion_50
		repeated string microphone_boost_partnames = 7;           //microphone boost part name list for COM object match
	}

	message Response {
		string audio_id = 1;
	}
}

message CreateAPPAudio {
	message Request {
		AudioUniAttr audio_uni_attr = 1;
		optional bool exclude_pid = 2;
	}

	message Response {
		string audio_id = 1;
	}
}

message CreatePCMAudio {
	message Request {
		AudioUniAttr audio_uni_attr = 1;
		optional AudioBuffer buffer = 2;
	}

	message Response {
		string audio_id = 1;
	}
}

message UpdatePCM {
	message Request {
		string audio_id = 1;
		AudioCaptureParam capture_param = 2;
		AudioBuffer buffer = 3;
	}
}

message RemoveAudio {
	message Request {
		string audio_id = 1;
	}
}

message IsEmpty {
	message Request {
		string audio_id = 1;
	}

	message Response {
		bool empty = 1;
	}
}

message SetRenderDeviceID {
	message Request {
		string device_id = 1;
	}
}

message AddFilter {
	message Request {
		string audio_id = 1;
		string filter_id = 2;
	}
}

message RemoveFilter {
	message Request {
		string audio_id = 1;
		string filter_id = 2;
	}
}

message SetAudioUniAttr {
	message Request {
		string audio_id = 1;
		AudioUniAttr audio_uni_attr = 2;
	}
}

message GetAudioUniAttr {
	message Request {
		string audio_id = 1;
	}
	
	message Response {
		AudioUniAttr audio_uni_attr = 3;
	}
}

message GetPerformance {
	message Request {
		string audio_id = 1;
	}

	message Response {
		bool  valid = 1;        // the value is valid
		int32 reset_times = 2;  // The number of times the audio data queue is reset at this stage
		int32 cost = 3;         // Time consuming for resampling and filtering
		int32 offset = 4;       // User manually adjusted offset
	}
}

message SetANSOption {
	message Request {
		AUDIO_ANS_OPTION ans_option = 1;
	}
}

message EnableAudioInputEchoDetection {
	message Request {
		string audio_id = 1;
		int32 interval = 2;
	}
}

message EnableAudioInputNoiseDetection {
	message Request {
		string audio_id = 1;
		int32 interval = 2;
		int32 duration = 3;
	}
}

message SetAudioInputRenderDeviceID {
	message Request {
		string audio_id = 1;
		string render_device_id = 2;
	}
}

// start capturing audio from a specified audio source
message StartAudioCapture {
    message Request {
        string audio_id = 1;
        optional string replace_audio_path = 2;
        optional int64 delay_time_ms = 3;
		optional bool enable_write = 4;
    }

    message Response {
        string audio_capture_id = 2;
    }
}

// stop capturing audio from a specified audio source
message StopAudioCapture {
    message Request {
        string audio_capture_id = 1;
    }
}

// update the audio ID for a captured audio source
message UpdateCapturedAudioID {
	message Request {
		string audio_capture_id = 1;
		string audio_id = 2;
	}
}

// set the PTS (Presentation Time Stamp) for audio replacement.
message SetAudioReplacePTS {
    message Request {
        string audio_capture_id = 1;
        int64 begin_pts_ms = 2;
        int64 end_pts_ms = 3;
		
		bool enable_dump = 4;
		string dump_tag = 5;
		string dump_filename = 6;
    }
	message Response {
		bool hit = 1;
		int64 buffer_begin_pts_ms = 2;
		int64 buffer_end_pts_ms = 3;
	}
}