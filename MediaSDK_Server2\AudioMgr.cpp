#include "AudioMgr.h"
#include "stringutil.h"

#define MAKETRACKID(track) (((track)&0x7f)<<56)
#define MAKEAUDIOID(audio) ((audio)&0xffffffffffffffff)

AudioMgr g_audioMgr;

AudioMgr* AudioMgr::GetInstance()
{
	return &g_audioMgr;
}

AudioMgr::AudioMgr(){}

AudioMgr::~AudioMgr(){}

UINT64 AudioMgr::AllocAudioID(UINT64 track)
{
    while (1)
    {
        USHORT audioCounter = ::InterlockedIncrement16((SHORT*)&m_audioCounter);
        if (audioCounter == 0)
            audioCounter = ::InterlockedIncrement16((SHORT*)&m_audioCounter);

        UINT64 audioID = MAKETRACKID(track) | MAKEAUDIOID((UINT64)audioCounter);
        std::map<UINT64, Audio*>::iterator it = m_audios.find(audioID);
        if (it == m_audios.end())
        {
            return audioID;
        }
    }

    return -1;
}

Audio* AudioMgr::GetAudioByID(UINT64 id)
{
    std::map<UINT64, Audio*>::iterator it = m_audios.find(id);
    if (it != m_audios.end())
    {
        return it->second;
    }

    return NULL;
}

UINT64 AudioMgr::CreateAudio(const AUDIO_INFO* info, const UINT64* id /*= NULL*/)
{
    AUDIO_INFO audioInfo = *info;
    audioInfo.id = id ? *id : AllocAudioID(info->audioTrack);
    Audio* pAudio = new Audio(audioInfo);

    if (pAudio)
    {
		bool isVISAudio = info->type == AUDIO_VIS;
		bool success = false;
        if (!isVISAudio)
        {
            success = pAudio->SyncToMedia(true);
        }

		if (success || isVISAudio)
		{
			m_audios.insert(std::pair<UINT64, Audio*>(audioInfo.id, pAudio));
			audioInfo.isCreated = !isVISAudio;
			pAudio->SetAudioInfo(&audioInfo);
			return audioInfo.id;
		}

		if (!success)
		{
			LOG(ERROR) << "[AudioMgr::AddAudio] audio create failed from sdk, audioID: " << audioInfo.id << " audioType: " << audioInfo.type;
			delete pAudio;
		}
    }
    
    return 0;
}

void AudioMgr::DestoryAudio(Audio* audio)
{
	for (auto it = m_audios.begin(); it != m_audios.end();)
	{
		if (it->second == audio)
		{
			it = m_audios.erase(it);
			AUDIO_INFO audioInfo = {};
			audio->GetAudioInfo(&audioInfo);
			bool isVISAudio = audioInfo.type == AUDIO_VIS;
			if (!isVISAudio)
				audio->SyncToMedia(false);

			delete audio;
		}
		else
		{
			++it;
		}
	}
}

void AudioMgr::CreateVISAudio(const SOURCE_INFO* sourceInfo)
{
    AUDIO_INFO audioInfo{};
    audioInfo.id = sourceInfo->id;
	audioInfo.type = AUDIO_VIS;
    if (sourceInfo->type == VISUAL_FAV)
    {
        FAV_SOURCE source = std::get<FAV_SOURCE>(sourceInfo->source);
        Util::NumToString(audioInfo.id, &audioInfo.device.id);
        audioInfo.device.name = source.materialDesc.path;
        audioInfo.audioTrack = source.audioTrack;
        audioInfo.audioSetting = source.audioSetting;
    }
    else if (sourceInfo->type == VISUAL_ANALOG)
    {
		ANALOG_SOURCE source = std::get<ANALOG_SOURCE>(sourceInfo->source);
		audioInfo.audioTrack = source.audioTrack;
        audioInfo.device.id = source.audioDevice.id;
        audioInfo.device.name = source.audioDevice.name;
		audioInfo.audioSetting = source.audioSetting;
        audioInfo.audioCapture.channels = source.audioCapFormat.channels;
        audioInfo.audioCapture.bitsPerSec = source.audioCapFormat.bitsPerSample;
        audioInfo.audioCapture.samplePerSec = source.audioCapFormat.sampleRate;
    }
	else if (sourceInfo->type == VISUAL_CAMERA)
	{
        CAMERA_SOURCE source = std::get<CAMERA_SOURCE>(sourceInfo->source);
        audioInfo.audioTrack = source.audioTrack;
        audioInfo.device.id = source.audioDevice.id;
        audioInfo.device.name = source.audioDevice.name;
        audioInfo.audioSetting = source.audioSetting;
        audioInfo.audioCapture.channels = source.audioCapFormat.channels;
        audioInfo.audioCapture.bitsPerSec = source.audioCapFormat.bitsPerSample;
        audioInfo.audioCapture.samplePerSec = source.audioCapFormat.sampleRate;
	}
    else if (sourceInfo->type == VISUAL_BYTELINK)
    {
		BYTELINK_SOURCE source = std::get<BYTELINK_SOURCE>(sourceInfo->source);
        Util::NumToString(sourceInfo->id, &audioInfo.device.id);
        audioInfo.device.name = source.name;
		audioInfo.audioTrack = source.audioTrack;
		audioInfo.audioSetting = source.audioSetting;
    }
    else if (sourceInfo->type == VISUAL_RTC)
    {
        RTC_SOURCE source = std::get<RTC_SOURCE>(sourceInfo->source);
        Util::NumToString(sourceInfo->id, &audioInfo.device.id);
        audioInfo.device.name = source.uid;
        audioInfo.audioTrack = source.audioTrack;
    }

    CreateAudio(&audioInfo, &audioInfo.id);
}

UINT64 AudioMgr::AllocCaptureAudioID(UINT32 track)
{
	while (1)
	{
		USHORT audioCounter = ::InterlockedIncrement16((SHORT*)&m_audioCounter);
		if (audioCounter == 0)
			audioCounter = ::InterlockedIncrement16((SHORT*)&m_audioCounter);

		UINT64 captureAudioID = MAKETRACKID(track) | MAKEAUDIOID((UINT64)audioCounter);
		std::map<UINT64, CaptureAudio*>::iterator it = m_captureAudios.find(captureAudioID);
		if (it == m_captureAudios.end())
		{
			return captureAudioID;
		}
	}

	return -1;
}

CaptureAudio* AudioMgr::GetCaptureAudioByID(UINT64 id)
{
	std::map<UINT64, CaptureAudio*>::iterator it = m_captureAudios.find(id);
	if (it != m_captureAudios.end())
	{
		return it->second;
	}

	return NULL;
}

UINT64 AudioMgr::CreateCaptureAudio(const CAPTURE_AUDIO_INFO* info, const UINT64* id /* = NULL */)
{
    CAPTURE_AUDIO_INFO captureAudioInfo = *info;
    captureAudioInfo.id = id ? *id : AllocCaptureAudioID(info->audioTrack);
	CaptureAudio* pCaptureAudio = new CaptureAudio(captureAudioInfo);

	if (!pCaptureAudio)
	    return 0;

	m_captureAudios.insert(std::pair<UINT64, CaptureAudio*>(captureAudioInfo.id, pCaptureAudio));
	if (captureAudioInfo.enableWrite)
    {
        if (pCaptureAudio->SyncToAudioFrameProcessor(true))
        {
            captureAudioInfo.isCreated = true;

            AUDIO_CONTROL_INFO audioControlInfo{};
            audioControlInfo.captureAudioInfo = captureAudioInfo;
            audioControlInfo.cmd = AUDIO_CONTROL_SET_SYNC_OFFSET;
            pCaptureAudio->ControlCaptureAudio(&audioControlInfo);
        }
        else
        {
            delete pCaptureAudio;
            LOG(ERROR) << "[AudioMgr::CreateCaptureAudio] erase audio create failed from frame processor, captureAudioID: " << captureAudioInfo.id;
            return 0;
        }
    }
	
	return captureAudioInfo.id;
}

bool AudioMgr::DestroyCaptureAudio(CaptureAudio* captureAudio, bool enableWrite /*= false*/)
{
	for (auto it = m_captureAudios.begin(); it != m_captureAudios.end();)
	{
		if (it->second == captureAudio)
		{
            if (enableWrite)
            {
                if (!captureAudio->SyncToAudioFrameProcessor(false))
                {
                    LOG(ERROR) << "[AudioMgr::DestroyCaptureAudio] SyncToAudioFrameProcessor false";
                    return false;
                }
            }

            it = m_captureAudios.erase(it);
			delete captureAudio;
            return true;
		}
		else
		{
			++it;
		}
	}

	return false;
}
