// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_config;
import "ls_basicenum.proto";
import "ls_base.proto";

enum GPU_D3DKMT_PRIORITY
{
	GPU_D3DKMT_PRIORITY_DEFAULT = 0;
	GPU_D3DKMT_PRIORITY_IDLE = 1;
	GPU_D3DKMT_PRIORITY_BELOW_NORMAL = 2;
	GPU_D3DKMT_PRIORITY_NORMAL = 3;
	GPU_D3DKMT_PRIORITY_ABOVE_NORMAL = 4;
	GPU_D3DKMT_PRIORITY_HIGH = 5;
	GPU_D3DKMT_PRIORITY_REALTIME = 6;
};

enum BUFFER_TYPE
{
	BUFFER_TYPE_UNKNOWN = 0;
	BUFFER_TYPE_MEMORY = 1;
	BUFFER_TYPE_TEXTURE = 2;
};

message AppLog {
	uint64 did = 1;
	string uid = 2;
	optional string device_band = 3;
	optional string device_model = 4;
	optional string language = 5;
	optional string resolution = 6;
	string server_url = 7;
	string config_url = 8;
	string local_storage_path = 9;
	optional string os = 10;
}

message DebugConfig {
	string log_file = 1;
	string dump_file = 2;
}

message ReporterConfig {
	string aid = 1;
	string did = 2;
	string uid = 3;
	string os_version = 4;
	bool   oversea = 5;
	string report_type = 6; // only mediasdk_log, parfait can choose.
	string host = 7;
	optional string dll_root_path = 8;
	optional sint32 report_interval = 9;
	optional bool	parfait_crash = 10;
	optional string url = 11;
	map<string, string> crash_contexts = 12;
}

message Crashpad {
	bool			use_pipe = 1;
	optional string url = 2;
	optional string log_file = 3;
	optional string pipe_name = 4;
}

message VideoMixParam {
	ls_base.SizeS output_size = 1;
	ls_base.ColorConfig color_config = 2;
	ls_basicenum.VIDEO_PIXEL_FORMAT pixel_format = 3;
	bool focus_nv12 = 4;
	float fps = 5;
	BUFFER_TYPE buffer_type = 6;
}

message AudioConfig {
	sint32 sample_rate = 1;
	sint32 audio_channel = 2;
	sint32 frames = 4;
	sint32 planes = 5;
	sint32 audio_format = 6;
	sint32 channel_layout = 7;
}

message PerformanceInfo {
	float cpu_usage = 1;
	float cpu_global_usage = 2;
	float memory_usage = 3;
	float active_fps = 4;
	float sdk_memory_usage = 5;
	float total_memory = 6;
	float cpu_global_utilization = 7;
	float page_fault = 8;
	float present_ready_fps = 9;
	float commit_memory_usage = 10;
}

message VideoAdapterInfo {
	string adapter_model = 1;
	string pci_id = 2;
	string adapter_luid = 3;
	string gpu_driver_version = 4;
	uint64 dedicated_memory_size = 5;
	uint64 shared_memory_size = 6;
	uint64 gpu_3d_total = 7;
	uint64 gpu_3d_usage = 8;
	uint64 gpu_dedicated_memory_total = 9;
	uint64 gpu_dedicated_memory_usage = 10;
	bool is_encode_active = 11;
}

message PerformanceMatricsParam {
	string name = 1;
	uint32 threadshold = 2;// ms
}

message Initialize {
	message Request {
		VideoMixParam video_mix = 1;
		AudioConfig audio_config = 2;
		optional uint32	audio_peak_interval = 3;
		optional uint64	hwnd_main = 4;
		uint64 hwnd = 5;
		uint64 instance = 6;
		string work_directory = 7;
		string resource_directory = 8;
		string install_directory = 9;
		string store_directory = 10;
		bool   administrator = 11;
		optional string	json_config = 12;
		optional ReporterConfig reporter = 13;		// report and log param
		optional Crashpad crashpad = 14;
		DebugConfig server_debug = 15;
		DebugConfig client_debug = 16;
		string version = 18;
		optional bool enable_transition = 19;
	}

	message Response {
		sint32 dx_error = 1;
		uint32 pid = 2;
	}
}

message PreUnInitialize {
	message Request {
	}
}

message IsForegroundFullScreen {
    message Request {
    }
	message Response {
		bool fullscreen = 1;
	}
}

message GetFontFamilies {
    message Request {}
    message Response {
        repeated string fonts = 1;
    }
}

message WindowOverlapped {
	message Request {
		uint64 hwnd = 1;
	}

	message Response {
		bool overlapped = 1;
	}
}

message GetWindowOverlappedMonitors {
    message Request {
		uint64 hwnd = 1;
	}
	message Response {
		repeated string monitor_dids = 1;
		string          main_monitor_did = 2;
		repeated string all_monitor_did_list = 3;
	}
}

message SetDisplayMasks {
	message Request {
		optional string portrait_path = 1;
		optional string landscape_path = 2;
	}
}

message EnablePreview {
	message Request {
		bool enable = 1;
	}

	message Response {
		bool success = 1;
	}
}

message EnableInteract {
	message Request {
		bool enable = 1;
	}
}

message CreateFullScreenDetector {
    message Request {
        string detector_id = 1;
    }
}

message DestroyFullScreenDetector {
    message Request {
        string detector_id = 1;
    }
}

message SetFullScreenDetectorIgnoreProcessList {
    message Request {
        repeated string ignore_exe_name_list = 1; 
    }
}

message GetMediaFileInfo {
	message Request {
		string file_path = 1;
	}
	message Response {
		ls_base.MaterialParam material_param = 1;
	}
}

message GetDeviceInfo {
	message Request {
	}

	message Response {
		repeated VideoAdapterInfo devices = 1;
	}
}

// Get information about the graphics card currently used by DX
message GetCurrentAdapterInfo {
	message Request {
	}

	message Response {
		string name = 1;
		uint32 vendor_id = 2;
		uint32 device_id = 3; 
	}
}

message GetPerformance {
	message Request {
	}

	message Response {
		PerformanceInfo info = 1;
	}
}

message GetCpuInfo {
	message Request {}
	message Response {
		string name = 1;
		sint32 num = 2;
		sint32 clock_speed = 3;
		sint32 core_num = 4;
		int32 family = 5;
        int32 model = 6;
        int32 stepping = 7;
	}
}

message GetMonitorInfos {
	message Request {}
	message Monitor {
		sint32 gpu_index = 1;
		bool is_hdr = 2;
		sint32 left = 3;
		sint32 top = 4;
		sint32 right = 5;
		sint32 bottom = 6;
		sint32 refresh = 7;
		bool is_current = 8;
		sint32 phys_width = 9;
		sint32 phys_height = 10;
	}
	message Response {
		repeated Monitor monitors = 1;
	}
}

message StartGPUDetect {
	message Request {}
}

// Get System HAGS Value, 2 on 1 off
message GetSysHAGS {
	message Request {}

	message Response {
		uint32 val = 1;
	}
}

message StartRenderProfiler {
	message Request {}
}

message StartCollectPerformanceMatrics {
	message Request {
		repeated PerformanceMatricsParam param = 1;
	}
}

message SetTTNtpMS {
	message Request {
		uint64 ntp_ms = 1;
		uint64 local_ms = 2;
	}
}

message InitEffectPlatform {  
    message Request {
        string app_version = 1;
        string device_type = 2;
        string app_id = 3;
        string access_key = 4;
        string channel = 5;
        string effect_cache_dir = 6;
        string model_cache_dir = 7;
        string built_in_model_dir = 8;
        string loki_host = 9; // for Tiktok Live Studio: https://api.tiktokv.com
        string ve_cloud_host = 10; // for Tiktok Live Studio: https://api.tiktokv.com
        string model_status = 11;
        string region = 12;
		string device_id = 13;
		string user_id = 14;
		string ttls_hardware_level = 15;
		int32 time_out = 16;
    }
}

message UnInitEffectPlatform {  
	message Request {
	}
}

message LoadEffectModels {
	message Request {
		string request_id = 1;
		string model_name = 2;
		repeated string requirement = 3;
	}
}

message UpdateEffectConfig {
	message Request {
		string user_id = 1;
		string ttls_hardware_level = 2;
	}
}

message UpdateDynamicConfig {
	message Request {
		optional string json_config = 1;
		optional bool enable_transition = 2;
	}
}

message CheckEncoderSession {
	message Request {
		string encoder_name = 1;
		uint32 count = 2;
	}

	message Response {
		int32 result = 1;
	}
}

message SetParfaitContextInfo {
	message Request {
		string key = 1;
		string value = 2;
	}
}

message ResetParfait {
	message Request {
		string aid = 1;
		string did = 2;
		string uid = 3;
		string os_version = 4;
		bool   oversea = 5;
		string report_type = 6;
		string host = 7;
		string log_file = 8;
		optional string dll_root_path = 9;
		optional sint32 report_interval = 10;
		optional bool parfait_crash = 11;
		optional string url = 12;
	}
}


message StartColorPicker {
	message Request {
		optional uint64 hwnd = 1;
	}
}

message StartAiIPCServer {
    message Request {
    }
    message Response {
    }
}

message StopAiIPCServer {
    message Request {
    }
    message Response {
    }
}

message ReStartAiIPCServer {
    message Request {
    }
    message Response {
    }
}