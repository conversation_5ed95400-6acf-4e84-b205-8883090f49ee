#pragma once

#include "pb_export.h"
#include "pbmodule.h"
#include "PBSender.h"
#include "PBRouter.h"
#include "PBBridge.h"
#include "ls_audio.pb.h"

namespace LS
{

class Audio : public PB::Module
{
private:
    virtual RequestList GetRequestHandlers() const override;

public:
    struct EnumAppAudio : public PB::RequestHandler<ls_audio::EnumAppAudio>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct EnumCaptureAudio : public PB::RequestHandler<ls_audio::EnumCaptureAudio>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

	struct EnumRenderAudio : public PB::RequestHandler<ls_audio::EnumRenderAudio>
	{
		virtual bool doHandle(const In& req, Out& rsp) override;
	};

    struct GetDefaultInput : public PB::RequestHandler<ls_audio::GetDefaultInput>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetDefaultOutput : public PB::RequestHandler<ls_audio::GetDefaultOutput>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SystemSupportAppAudio : public PB::RequestHandler<ls_audio::SystemSupportAppAudio>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct CreateWASAudio : public PB::RequestHandler<ls_audio::CreateWASAudio>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct CreateAPPAudio : public PB::RequestHandler<ls_audio::CreateAPPAudio>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct CreatePCMAudio : public PB::RequestHandler<ls_audio::CreatePCMAudio>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct RemoveAudio : public PB::RequestHandler<ls_audio::RemoveAudio>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct IsEmpty : public PB::RequestHandler<ls_audio::IsEmpty>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct UpdatePCM : public PB::RequestHandler<ls_audio::UpdatePCM>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetRenderDeviceID : public PB::RequestHandler<ls_audio::SetRenderDeviceID>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetPerformance : public PB::RequestHandler<ls_audio::GetPerformance>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct AddFilter : public PB::RequestHandler<ls_audio::AddFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct RemoveFilter : public PB::RequestHandler<ls_audio::RemoveFilter>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetAudioUniAttr : public PB::RequestHandler<ls_audio::SetAudioUniAttr>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct GetAudioUniAttr : public PB::RequestHandler<ls_audio::GetAudioUniAttr>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetANSOption : public PB::RequestHandler<ls_audio::SetANSOption>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct EnableAudioInputEchoDetection : public PB::RequestHandler<ls_audio::EnableAudioInputEchoDetection>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct EnableAudioInputNoiseDetection : public PB::RequestHandler<ls_audio::EnableAudioInputNoiseDetection>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetAudioInputRenderDeviceID : public PB::RequestHandler<ls_audio::SetAudioInputRenderDeviceID>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct StartAudioCapture : public PB::RequestHandler<ls_audio::StartAudioCapture>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct StopAudioCapture : public PB::RequestHandler<ls_audio::StopAudioCapture>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct UpdateCapturedAudioID : public PB::RequestHandler<ls_audio::UpdateCapturedAudioID>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };

    struct SetAudioReplacePTS : public PB::RequestHandler<ls_audio::SetAudioReplacePTS>
    {
        virtual bool doHandle(const In& req, Out& rsp) override;
    };
    
    static void SetVISAudioInfo(AUDIO_SETTING& audioSetting, const ls_audio::AudioSettingParam& audio_setting);
};
} // namespace MediaSDK