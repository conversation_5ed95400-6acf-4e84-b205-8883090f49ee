#include "stdafx.h"
#include "canvas.h"
#include "../visual/layer.h"

namespace LS
{
LSCanvas::RequestList LSCanvas::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<LSCanvas::CreateCanvas>());
    list.push_back(std::make_unique<LSCanvas::DestoryCanvas>());
    list.push_back(std::make_unique<LSCanvas::SetCanvasInfo>());
    list.push_back(std::make_unique<LSCanvas::GetCanvasInfo>());
    list.push_back(std::make_unique<LSCanvas::AddLayers>());
    list.push_back(std::make_unique<LSCanvas::RemoveLayers>());
    list.push_back(std::make_unique<LSCanvas::AddFilter>());
    list.push_back(std::make_unique<LSCanvas::RemoveFilter>());

    list.push_back(std::make_unique<LSCanvas::VibeTriggerEffect>());
    list.push_back(std::make_unique<LSCanvas::SetCanvasSelectBorderType>());
    return list;
}

bool LSCanvas::CreateCanvas::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    CANVAS_INFO canvasInfo;
    canvasInfo.allSync = true;
    UINT64 canvasID = controller->CreateCanvas(&canvasInfo);
    if (canvasID <= 0)
    {
        LOG(ERROR) << "[LSCanvas::CreateCanvas] CreateCanvas failed, canvasID: " << canvasID;
        return false;
    }

    PREVIEW_INFO previewInfo;
    if (req.has_canvas_info())
    {
        if (req.canvas_info().has_view_rect())
        {
            previewInfo.rect = {req.canvas_info().view_rect().left(), req.canvas_info().view_rect().top(), req.canvas_info().view_rect().width(), req.canvas_info().view_rect().height()};
        }
        if (req.canvas_info().has_layout_view_rect())
        {
            previewInfo.layoutRect = {0, 0, req.canvas_info().layout_view_rect().width(), req.canvas_info().layout_view_rect().height()};
            canvasInfo.layoutRect = {0, 0, req.canvas_info().layout_view_rect().width(), req.canvas_info().layout_view_rect().height()};
        }
        if (req.canvas_info().has_output_size())
        {
            previewInfo.outputSize = {req.canvas_info().output_size().x(), req.canvas_info().output_size().y()};
        }
        previewInfo.fps = req.canvas_info().fps();
        previewInfo.bkColor = req.canvas_info().bk_color();
    }
    previewInfo.videoModel = req.canvas_info().video_model();
    UINT32 previewID = controller->AddPreview(&previewInfo);
    if (previewID <= 0)
    {
        LOG(ERROR) << "[LSCanvas::CreateCanvas] AddPreview failed, previewID: " << previewID;
        return false;
    }

    canvasInfo.previewID = previewID;
    controller->SetCanvasInfo(canvasID, &canvasInfo);

    previewInfo.id = previewID;
    previewInfo.canvasIDs.push_back(canvasID);
    previewInfo.curCanvasID = canvasID;
    controller->SetPreviewInfo(previewID, &previewInfo);

    std::string canvasIDStr = "";
    Util::NumToString(canvasID, &canvasIDStr);
    rsp.set_canvas_id(canvasIDStr);
    return true;
}

bool LSCanvas::DestoryCanvas::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    for (int i = 0; i < req.canvas_ids_size(); ++i)
    {
        std::string canvasIDStr = req.canvas_ids(i);
        UINT64 canvasID = 0;
        Util::StringToNum(canvasIDStr, &canvasID);

        if (!controller->CheckCanvasByID(canvasID))
        {
            LOG(WARNING) << "[LSCanvas::DestoryCanvas] check canvas not real exist, canvasID: " << canvasID;
            continue;
        }

        if (!controller->DestroyCanvas(canvasID))
        {
            LOG(ERROR) << "[Canvas::DestoryCanvas] destroy canvas failed, canvasID: " << canvasID;
            return true;
        }
    }

    return true;
}

bool LSCanvas::SetCanvasInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::SetCanvasInfo] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    PREVIEW_INFO previewInfo;
    if (req.has_canvas_info())
    {
        CANVAS_INFO canvasInfo;
        controller->GetCanvasInfo(canvasID, &canvasInfo);
        controller->GetPreviewInfo(canvasInfo.previewID, &previewInfo);

        if (req.has_canvas_info())
        {
            if (req.canvas_info().has_view_rect())
            {
                previewInfo.rect = {req.canvas_info().view_rect().left(), req.canvas_info().view_rect().top(), req.canvas_info().view_rect().width(), req.canvas_info().view_rect().height()};
            }
            if (req.canvas_info().has_layout_view_rect())
            {
                previewInfo.layoutRect = {0, 0, req.canvas_info().layout_view_rect().width(), req.canvas_info().layout_view_rect().height()};
                canvasInfo.layoutRect = {0, 0, req.canvas_info().layout_view_rect().width(), req.canvas_info().layout_view_rect().height()};
            }
            if (req.canvas_info().has_output_size())
            {
                previewInfo.outputSize = {req.canvas_info().output_size().x(), req.canvas_info().output_size().y()};
            }
            previewInfo.fps = req.canvas_info().fps();
            previewInfo.bkColor = req.canvas_info().bk_color();
        }

        controller->UpdatePreviewLayout(previewInfo);
        controller->UpdateCanvasLayout(canvasInfo);
    }

    return true;
}

bool LSCanvas::GetCanvasInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::SetCanvasInfo] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    CANVAS_INFO           canvasInfo{};
    controller->GetCanvasInfo(canvasID, &canvasInfo);

    PREVIEW_INFO          previewInfo;
    controller->GetPreviewInfo(canvasInfo.previewID, &previewInfo);

    ls_canvas::CanvasInfo canvas_info{};
    canvas_info.set_video_model(previewInfo.videoModel);
    canvas_info.set_fps(previewInfo.fps);
    canvas_info.set_bk_color(previewInfo.bkColor);
    ls_base::RectF view_rect{};
    view_rect.set_left(previewInfo.rect.X);
    view_rect.set_top(previewInfo.rect.Y);
    view_rect.set_width(previewInfo.rect.Width);
    view_rect.set_height(previewInfo.rect.Height);
    canvas_info.mutable_view_rect()->CopyFrom(view_rect);
    ls_base::RectF layout_view_rect{};
    layout_view_rect.set_left(previewInfo.layoutRect.X);
    layout_view_rect.set_top(previewInfo.layoutRect.Y);
    layout_view_rect.set_width(previewInfo.layoutRect.Width);
    layout_view_rect.set_height(previewInfo.layoutRect.Height);
    canvas_info.mutable_layout_view_rect()->CopyFrom(layout_view_rect);
    ls_base::SizeF output_size{};
    output_size.set_x(previewInfo.outputSize.Width);
    output_size.set_y(previewInfo.outputSize.Height);
    canvas_info.mutable_output_size()->CopyFrom(output_size);
    rsp.mutable_canvas_info()->CopyFrom(canvas_info);
    return true;
}

bool LSCanvas::AddLayers::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::AddLayer] canvas is not exist, canvasID: " << canvasID;
        return false;
    }

    for (int i = 0; i < req.layer_ids_size(); ++i)
    {
        UINT64 layerID = 0;
        Util::StringToNum(req.layer_ids(i), &layerID);
        if (!controller->FindLayerByID(layerID))
        {
            LOG(WARNING) << "[Canvas::AddLayer] layer not exist, layerID: " << layerID;
            continue;
        }

        if (controller->AddLayerByID(canvasID, layerID))
        {
            controller->HandleLayerSizeChange(layerID);
        }
        else
        {
            LOG(ERROR) << "[Canvas::AddLayer] AddLayerByID failed, layerID: " << layerID << ", canvasID: " << canvasID;
        }
    }
    return true;
}

bool LSCanvas::RemoveLayers::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::RemoveLayers] canvas is not exist, canvasID: " << canvasID;
        return false;
    }

    for (int i = 0; i < req.layer_ids_size(); ++i)
    {
        UINT64 layerID = 0;
        Util::StringToNum(req.layer_ids(i), &layerID);
        if (!controller->FindLayerByID(layerID))
        {
            LOG(WARNING) << "[Layout::RemoveLayers] layer not exist, layerID: " << layerID;
            continue;
        }

        LAYER_INFO layerInfo{};
        controller->GetLayerInfo(layerID, &layerInfo);

        SOURCE_INFO sourceInfo{};
        controller->GetSourceInfo(layerInfo.sourceID, &sourceInfo);
        const auto& iter = std::find(sourceInfo.layerIDs.begin(), sourceInfo.layerIDs.end(), layerID);
        if (iter != sourceInfo.layerIDs.end())
        {
            sourceInfo.layerIDs.erase(iter);
            controller->SetSourceInfo(sourceInfo.id, &sourceInfo);
        }

        controller->RemoveLayer(canvasID, layerID);
    }

    return true;
}

bool LSCanvas::AddFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::AddFilter] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[Canvas::AddFilter] filter not exist, filterID: " << filterID;
        return false;
    }

    controller->BindFilter(canvasID, filterID);
    return true;
}

bool LSCanvas::RemoveFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->FindCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::RemoveFilter] canvas not exist, canvasID: " << canvasID;
        return true;
    }

    UINT64 filterID = 0;
    Util::StringToNum(req.filter_id(), &filterID);
    if (!controller->FindFilterByID(filterID))
    {
        LOG(ERROR) << "[Visual::RemoveFilter] filter not exist, filterID: " << filterID;
        return true;
    }

    controller->UnBindFilter(canvasID, filterID);
    return true;
}

bool LSCanvas::VibeTriggerEffect::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->CheckCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::VibeTriggerEffect] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    VIBE_TRIGGER_EFFECT vibeTriggerEffect{};
    vibeTriggerEffect.canvasID = req.canvas_id();

    if (req.has_layers_zorder())
    {
        VIBE_LAYERS_ZORDER& layerZOrder = vibeTriggerEffect.layersZorder;
        layerZOrder.actionID = req.layers_zorder().action_id();
        layerZOrder.layerIDs.clear();
        for (int i = 0; i < req.layers_zorder().layer_ids_size(); ++i)
        {
            layerZOrder.layerIDs.push_back(req.layers_zorder().layer_ids(i));
        }
    }

    std::vector<VIBE_LAYER_VISIBLE>& layersVisible = vibeTriggerEffect.layersVisible;
    for (int i = 0; i < req.layers_visible_size(); ++i)
    {
        ls_canvas::VibeLayerVisible layer_visible = req.layers_visible(i);
        VIBE_LAYER_VISIBLE          layerVisible{};
        layerVisible.actionID = layer_visible.action_id();
        if (layer_visible.has_duration_ms())
        {
            layerVisible.durationMs = layer_visible.duration_ms();
        }
        layerVisible.layerID = layer_visible.layer_id();
        layerVisible.visible = layer_visible.visible();
        layerVisible.transitionProgressFuncType = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(layer_visible.transition_progress_func_type());

        layersVisible.push_back(layerVisible);
    }

    if (req.has_preprocess_info())
    {
        VIBE_PREPROCESS_INFO& preprocessInfo = vibeTriggerEffect.preprocessInfo;
        preprocessInfo.layersPreprocess.clear();
        for (int i = 0; i < req.preprocess_info().layers_preprocess_size(); ++i)
        {
            ls_canvas::VibeLayerPreprocess layer_preprocess = req.preprocess_info().layers_preprocess(i);
            VIBE_LAYER_PREPROCESS          layerPreprocess{};
            layerPreprocess.actionID = layer_preprocess.action_id();
            layerPreprocess.layerID = layer_preprocess.layer_id();
            layerPreprocess.needPreprocess = layer_preprocess.need_preprocess();
            preprocessInfo.layersPreprocess.push_back(layerPreprocess);
        }
    }

    if (req.has_audio_ambient())
    {
        VIBE_AUDIO_AMBIENT& audioAmbient = vibeTriggerEffect.audioAmbient;
        audioAmbient.actionID = req.audio_ambient().action_id();
        audioAmbient.actionType = static_cast<VIBE_ACTION_TYPE>(req.audio_ambient().action_type());
        audioAmbient.audioID = req.audio_ambient().audio_id();

        if (req.audio_ambient().has_audio_data())
        {
            audioAmbient.audioData.bufLeft = req.audio_ambient().audio_data().buffer_l();
            audioAmbient.audioData.bufRight = req.audio_ambient().audio_data().buffer_r();
        }
        
        if (req.audio_ambient().has_audio_capture())
        {
            ls_audio::AudioCaptureParam audio_capture = req.audio_ambient().audio_capture();
            AUDIO_CAPTURE&              audioCapture = audioAmbient.audioCapture;
            if (audio_capture.has_sample_per_sec())
            {
                audioCapture.samplePerSec = audio_capture.sample_per_sec();
            }
            if (audio_capture.has_bits_per_sec())
            {
                audioCapture.bitsPerSec = audio_capture.bits_per_sec();
            }
            if (audio_capture.has_channels())
            {
                audioCapture.channels = audio_capture.channels();
            }
            if (audio_capture.has_frames())
            {
                audioCapture.frames = audio_capture.frames();
            }
            if (audio_capture.has_planes())
            {
                audioCapture.planes = audio_capture.planes();
            }
            if (audio_capture.has_audio_format())
            {
                audioCapture.audioFormat = audio_capture.audio_format();
            }
            if (audio_capture.has_channel_layout())
            {
                audioCapture.channelLayout = audio_capture.channel_layout();
            } 
        }
    }

    std::vector<VIBE_LAYER_TRANSFORM>& layersTransform = vibeTriggerEffect.layersTransform;
    for (int i = 0; i < req.layers_transform_size(); ++i)
    {
        ls_canvas::VibeLayerTransform layer_transform = req.layers_transform(i);
        VIBE_LAYER_TRANSFORM layerTransform{};
        layerTransform.actionID = layer_transform.action_id();
        layerTransform.layerID = layer_transform.layer_id();
        if (layer_transform.has_duration_ms())
        {
            layerTransform.durationMs = layer_transform.duration_ms();
        }
        if (layer_transform.has_transition_progress_func_type())
        {
            layerTransform.transitionProgressFuncType = static_cast<TRANSITION_PROGRESS_FUNCTION_TYPE>(layer_transform.transition_progress_func_type());
        }

        std::string main_layer_id = layer_transform.layer_id();
        UINT64      mainLayerID = 0;
        Util::StringToNum(main_layer_id, &mainLayerID);
        LAYER_INFO mainLayerInfo;
        controller->GetLayerInfo(mainLayerID, &mainLayerInfo);
        LOG(INFO) << "[LSCanvas::VibeTriggerEffect] mainLayer Transform: " << mainLayerInfo.transform.toString();

        LAYER_INFO tmpLayerInfo{};
        tmpLayerInfo.transform = mainLayerInfo.transform;
        ls_base::Transform target_transform = layer_transform.target_transform();
        if (target_transform.has_angle())
        {
            tmpLayerInfo.transform.angle = layer_transform.target_transform().angle();
        }
        if (target_transform.has_clip())
        {
            tmpLayerInfo.transform.clipRange.x = layer_transform.target_transform().clip().x();
            tmpLayerInfo.transform.clipRange.y = layer_transform.target_transform().clip().y();
            tmpLayerInfo.transform.clipRange.z = layer_transform.target_transform().clip().z();
            tmpLayerInfo.transform.clipRange.w = layer_transform.target_transform().clip().w();
        }
        if (target_transform.has_flip_h())
        {
            tmpLayerInfo.transform.hFlip = layer_transform.target_transform().flip_h();
        }
        if (target_transform.has_flip_v())
        {
            tmpLayerInfo.transform.vFlip = layer_transform.target_transform().flip_v();
        }
        if (target_transform.has_min_scale())
        {
            tmpLayerInfo.transform.minScale.X = layer_transform.target_transform().min_scale().x();
            tmpLayerInfo.transform.minScale.Y = layer_transform.target_transform().min_scale().y();
        }
        if (target_transform.has_max_scale())
        {
            tmpLayerInfo.transform.maxScale.X = layer_transform.target_transform().max_scale().x();
            tmpLayerInfo.transform.maxScale.Y = layer_transform.target_transform().max_scale().y();
        }
        if (target_transform.has_scale())
        {
            tmpLayerInfo.transform.scale.X = layer_transform.target_transform().scale().x();
            tmpLayerInfo.transform.scale.Y = layer_transform.target_transform().scale().y();
        }
        if (target_transform.has_size())
        {
            tmpLayerInfo.transform.size.Width = layer_transform.target_transform().size().x();
            tmpLayerInfo.transform.size.Height = layer_transform.target_transform().size().y();
        }
        if (target_transform.has_translate())
        {
            tmpLayerInfo.transform.translate.X = layer_transform.target_transform().translate().x();
            tmpLayerInfo.transform.translate.Y = layer_transform.target_transform().translate().y();
        }
        if (layer_transform.has_canvas_size())
        {
            tmpLayerInfo.targetSize.Width = layer_transform.canvas_size().x();
            tmpLayerInfo.targetSize.Height = layer_transform.canvas_size().y();
        }
        tmpLayerInfo.layout = static_cast<LAYER_LAYOUT>(layer_transform.layout());
        LOG(INFO) << "[LSCanvas::VibeTriggerEffect] tmpLayer Transform: " << tmpLayerInfo.transform.toString();
        
        controller->CreateLayer(&tmpLayerInfo);
        tmpLayerInfo.canvasID = canvasID;

        UINT64 cmd = LAYER_CONTROL_SET_TARGET_SIZE | LAYER_CONTROL_SET_LAYOUT;
        controller->CalLayerTransform(tmpLayerInfo, cmd);
        controller->GetLayerInfo(tmpLayerInfo.id, &tmpLayerInfo);

        controller->DeleteLayer(tmpLayerInfo.id);

        layerTransform.targetTransform = tmpLayerInfo.transform;
        LOG(INFO) << "[LSCanvas:VibeTriggerEffect] target Transform: " << layerTransform.targetTransform.toString();
        layersTransform.push_back(layerTransform);
    }

    std::vector<VIBE_FILTER_INFO>& filterInfos = vibeTriggerEffect.filterInfos;
    filterInfos.clear();
    for (int i = 0; i < req.filter_infos_size(); ++i)
    {
        ls_canvas::VibeFilterInfo filter_info = req.filter_infos(i);
        VIBE_FILTER_INFO          filterInfo{};
        filterInfo.actionID = filter_info.action_id();
        filterInfo.actionType = static_cast<VIBE_ACTION_TYPE>(filter_info.action_type());
        
        UINT64  filterID = 0;
        Util::StringToNum(filter_info.filter_id(), &filterID);
        controller->GetFilterInfo(filterID, &filterInfo.filter);
        UINT64 mediaID = 0;
        Util::StringToNum(filter_info.media_id(), &mediaID);
        filterInfo.filter.mediaIDs.clear();
        filterInfo.filter.mediaIDs.push_back(mediaID);
        filterInfos.push_back(filterInfo);
    }
    
    std::vector<std::pair<std::string, bool>> resultActionIDs;
    if (!controller->VibeTriggerEffect(vibeTriggerEffect, resultActionIDs))
    {
        LOG(ERROR) << "[LSCanvas::VibeTriggerEffect] VibeTriggerEffect failed, canvasID: " << vibeTriggerEffect.canvasID;
        return false;
    }

    for (const auto& resultActionID : resultActionIDs)
    {
        auto* result_action_id = rsp.add_result_action_ids();
        result_action_id->set_action_id(resultActionID.first);
        result_action_id->set_success(resultActionID.second);
    }

    return true;
}

bool LSCanvas::SetCanvasSelectBorderType::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 canvasID = 0;
    Util::StringToNum(req.canvas_id(), &canvasID);
    if (!controller->CheckCanvasByID(canvasID))
    {
        LOG(ERROR) << "[Canvas::VibeTriggerEffect] canvas not exist, canvasID: " << canvasID;
        return false;
    }

    if (!controller->SetCanvasSelectBorderType(canvasID, static_cast<CANVAS_SELECTION_BORDER_TYPE>(req.border_type())))
    {
        LOG(ERROR) << "[LSCanvas::PreviewSetCanvasSelectBorderType] PreviewSetCanvasSelectBorderType faled, canvasID: " << canvasID << ", border_type: " << static_cast<int>(req.border_type());
        return false;
    }

    return true;
}
} // namespace LS