#pragma once

#ifdef __cplusplus
namespace bytecast 
{
#endif
typedef enum {
    LOG_LV_ERROR = 0,
    LOG_LV_WARNING,
    LOG_LV_INFO,
    LOG_LV_DEBUG,
    LOG_LV_VERBOSE,
} LogLevel;

/**************************** env check return value ***********************************/
#define CHK_RES_PASS                  0  // pass, env is ok
#define CHK_RES_SYS_ERROR            -1  // system error, maybe env is ok
#define CHK_RES_NOT_INSTALLED        -2  // not installed
#define CHK_RES_NOT_RUNNING          -3  // not running
#define CHK_RES_MULTI_DIFF_ACTIVE    -4  // 多活动网络，且策略不同，mybe env is ok
#define CHK_RES_ALL_IN_BLOCKED       -5  // 阻止所有外部接入
#define CHK_RES_BONJOUR_ALL_IN_BLOCKED      -6    // 阻止接入到Bonjour Server
#define CHK_RES_BONJOUR_ALL_OUT_BLOCKED     -7    // 阻止Bonjour Server接入和外放
#define CHK_RES_BONJOUR_UDP_IN_BLOCKED      -8    // 阻止UDP接入到Bonjour Server
#define CHK_RES_BONJOUR_UDP_OUT_BLOCKED     -9    // 阻止Bonjour Server以UDP协议外访
#define CHK_RES_BONJOUR_TCP_IN_BLOCKED      -10   // 阻止TCP接入到Bonjour Server
#define CHK_RES_BONJOUR_TCP_OUT_BLOCKED     -11   // 阻止Bonjour Server以TCP协议外访
#define CHK_RES_BONJOUR_MIX_BLOCKED         -12   // 混合型阻止Bonjour Server的访问
#define CHK_RES_MEDIASDK_ALL_IN_BLOCKED     -13   // 阻止接入到MediaSDK
#define CHK_RES_MEDIASDK_ALL_OUT_BLOCKED    -14   // 阻止MediaSDK外访
#define CHK_RES_MEDIASDK_UDP_IN_BLOCKED     -15   // 阻止UDP接入到MediaSDK
#define CHK_RES_MEDIASDK_UDP_OUT_BLOCKED    -16   // 阻止MediaSDK以UDP协议外访
#define CHK_RES_MEDIASDK_TCP_IN_BLOCKED     -17   // 阻止TCP接入到MediaSDK
#define CHK_RES_MEDIASDK_TCP_OUT_BLOCKED    -18   // 阻止MediaSDK以TCP协议外访
#define CHK_RES_MEDIASDK_MIX_BLOCKED        -19   // 混合型阻止MediaSDK的访问
 

/**************************** env repair return value ***********************************/
#define REPAIR_RES_SUCCESS                      0           
#define REPAIR_RES_NOT_ADMIN                    0x70001001  // program owner is not admin
#define REPAIR_RES_NO_MEMORY                    0x70001002  // 
#define REPAIR_RES_SERVICE_STATE_NOT_STOPPED    0x70001003  // state is not stopped
#define REPAIR_RES_SERVICE_START_FAILED         0x70001004  // start failed
#define REPAIR_RES_SERVICE_START2_FAILED        0x70001005  // change config then start failed
#define REPAIR_RES_SERVICE_CONFIG_CHANGE_FAILED 0x70001006  // change config failed which is forbidden
#define REPAIR_RES_COM_INIT_FAILED              0x70001007
#define REPAIR_RES_COM_CREATE_FAILED            0x70001008
#define REPAIR_RES_COM_GET_PROFILE_FAILED       0x70001009
#define REPAIR_RES_COM_GET_RULES_FAILED         0x7000100A
#define REPAIR_RES_COM_ENUM_RULES_FAILED        0x7000100B
#define REPAIR_RES_COM_QUERY_INTF_FAILED        0x7000100C
#define REPAIR_RES_COM_PUT_PROP_FAILED          0x7000100D
#define REPAIR_RES_COM_ADD_RULE_FAILED          0x7000100E
#define REPAIR_RES_NO_ATIVE_NET                 0x7000100F
#define REPAIR_RES_BLOCK_ALLIN_FAILED           0x70001010
#define REPAIR_RES_REMOVE_RULES_FAILED          0x70001011
#define REPAIR_RES_UNKNOWN_FAILED               0x70001FFF

/*****************************  ERROR CODE **********************************/
#define ECODE_BASE_WIRED_DRIVER         18000000
#define ECODE_BASE_WIRED_IOS            13000000
#define ECODE_BASE_WIRELESS_IOS         11000000
#define ECODE_BASE_WIRED_ANDROID        12000000
#define ECODE_BASE_WIRELESS_ANDROID     10000000
#define ECODE_BASE_DECODER              16000000

// service related
#define ECODE_SERVER_START_FAILED       31
#define ECODE_SERVER_STOP_FAILED        32

// virtual camera
#define ERROR_VIRTUALCAMERA_CREATE          60000001

// decoder 
#define ERROR_CODEC_VIDEO_DECODE            16003001
#define ERROR_CODEC_VIDEO_INIT              16003004
#define ERROR_CODEC_VIDEO_NULLPTR           16003006
#define ERROR_CODEC_VIDEO_WRONG_PARAM       16003907  // just warning
#define ERROR_CODEC_VIDOE_WRONG_DATA        16003008
#define ERROR_CODEC_VIDOE_NO_MEMORY         16003009
#define ERROR_CODEC_VIDOE_EXTERNAL          16003010
#define ERROR_CODEC_VIDOE_UNINIT            16003011
#define ERROR_CODEC_VIDOE_FRAME_NOT_FOUND   16003912   // just warning
#define ERROR_CODEC_VIDOE_DECODE_OVERLOAD   16003013
#define ERROR_CODEC_VIDOE_DECODE_OVERLOAD_M 16003913   // just mark
#define ERROR_CODEC_VIDOE_DECODE_TIMEOUT    16003014
#define ERROR_CODEC_VIDOE_UNSUPPORT_CODEC   16003015

#define ERROR_CODEC_DECODE_UNKOWN           16003099

#define ERROR_CODEC_AUDIO_INIT              16004001
#define ERROR_CODEC_AUDIO_DECODE            16004002

/*****************************  EVENT CODE **********************************/
#define DEVICE_ANDROID_PYHSICAL_ATTACHED    103
#define DEVICE_ANDROID_PYHSICAL_DETACHED    104
#define DEVICE_IOS_PYHSICAL_ATTACHED        105
#define DEVICE_IOS_PYHSICAL_DETACHED        106
#define EVENT_CAST_FIRST_FRAME              107
#define EVENT_IOS_DEVICE_STATE              109
#define EVENT_CAST_VIDEO_CATON              110
#define EVENT_DECODE_VIDEO_CATON            111
#define EVENT_WIRED_START_RECORED           304
#define EVENT_WIRED_STOP_RECORED            305
#define EVENT_WIRED_ACCEPT_RECORED          306
#define EVENT_WIRED_REFUSE_RECORED          307
#define EVENT_CLIENT_DEVICE_INFO            308
#define EVENT_CAST_TOTAL_TIME               309
#define EVENT_SERVICE_START                 310
#define EVENT_SERVICE_START_SUCCESS         311
#define EVENT_AUDIO_FOCUS_CHANGED           313
#define EVENT_CAST_START_MIRROR             321
#define EVENT_CAST_STOP_MIRROR              322
#define EVENT_WIRED_IN_ACCESSORY_MODE       323

#define EVENT_REDIRECT_MESSAGE              324

#define EVENT_BROWSE_START                  400
#define EVENT_BROWSE_STOP                   401
#define EVENT_BROWSE_SUCCESS                402
#define EVENT_INVITE_CAST                   410
#define EVENT_CAST_STOP                     411
#define EVENT_INVITE_CAST_SUCCESS           412
#define EVENT_INVITE_CAST_REJECT            413
#define EVENT_INVITE_CAST_ERROR             499
#define EVENT_VIRTUALCAMERA_CREATE          500
#define EVENT_VIRTUALCAMERA_DESTROY         501

#define EVENT_WIREDIOS_DEBUGINFO            601
#define EVENT_WIREDIOS_ONTRUSTDEBUG         603
#define EVENT_WIREDIOS_MOBILEVERSION        604
#define EVENT_WIREDIOS_DRIVERLOSTDEBUG      605
#define EVENT_WIREDIOS_CLIENTREQCONNECT     606
#define EVENT_WIREDIOS_NOTIFYCLIENTOPENAPP  607

#define EVENT_CAST_STOPPED                  1000
#define EVENT_DECODE_FIRST_FRAME            1001
#define EVENT_DECODE_MIRRORPORT             1009
#define EVENT_PERFORMANCE_STATISTICS        1010
#define EVENT_DECODER_WARNING               1011
#define EVENT_DECODER_DOWNGRADE             1012
#define EVENT_DECODER_FIRST_RESOLUTION      1013

#define EVENT_INVITE_BYTELINK_START         1114
#define EVENT_INVITE_BYTELINK_ACCEPT        1115
#define EVENT_INVITE_BYTELINK_REJECT        1116
#define EVENT_INVITE_BYTELINK_FAIL          1117
#define EVENT_INVITE_BYTELINK_CONNECTED     1118
#define EVENT_INVITE_BYTELINK_STREAMING     1119

#define EVENT_DEBUG_INFO_RESOLUTION         2001
#define EVENT_DEBUG_INFO_OVERFLOW           2002

#define EVENT_BYTELINK_RECONNECT_START      4002
#define EVENT_BYTELINK_RECONNECT_RESUME     4003

#define EVENT_CAST_ENCODE_FPS               5001

typedef enum {
    DRIVER_INSTALLED_ALREADY              = 100,
    DRIVER_INSTALLED_SUCCESS              = 101,  // have never been installed, ande never been running
    DRIVER_INSTALLED_FAILED               = 102,  
    DRIVER_INSTALLED_SUCCESS_0            = 150,  // have never been installed
    DRIVER_INSTALLED_SUCCESS_1            = 151,  // have been replaced by third application
    DRIVER_INSTALLED_SUCCESS_2            = 152,  // have been replaced by system update
    DRIVER_INSTALLED_SUCCESS_3            = 153,  // have been replaced by device manager update
    DRIVER_INSTALLED_SUCCESS_4            = 154,  // have been uninstalled by third application
    DRIVER_INSTALLED_SUCCESS_5            = 155,  // have been uninstalled by device manager
    DRIVER_INSTALLED_SUCCESS_6            = 156,  // have been replaced by aisi
    DRIVER_INSTALLED_SUCCESS_7            = 157,  // have been replaced by hisute
    DRIVER_INSTALLED_SUCCESS_8            = 158,  // have been replaced by device install
    DRIVER_INSTALLED_SUCCESS_DUP          = 160,  // have been installed

    DRIVER_INSTALL_NOTHING                = 201,
    DRIVER_INSTALLED_TIMEOUT              = 202,  // have never been installed, ande never been running
    DRIVER_INSTALLED_TIMEOUT_0            = 250,  // have never been installed
    DRIVER_INSTALLED_TIMEOUT_1            = 251,  // have been replaced by third application
    DRIVER_INSTALLED_TIMEOUT_2            = 252,  // have been replaced by system update
    DRIVER_INSTALLED_TIMEOUT_3            = 253,  // have been replaced by device manager update
    DRIVER_INSTALLED_TIMEOUT_4            = 254,  // have been uninstalled by third application
    DRIVER_INSTALLED_TIMEOUT_5            = 255,  // have been uninstalled by device manager
    DRIVER_INSTALLED_TIMEOUT_6            = 256,  // have been replaced by aisi
    DRIVER_INSTALLED_TIMEOUT_7            = 257,  // have been replaced by hisute
    DRIVER_INSTALLED_TIMEOUT_8            = 258,  // have been replaced by device install
    DRIVER_INSTALLED_TIMEOUT_DUP          = 260,  // have been installed
    DRIVER_INSTALL_ACCEPT_BY_USER         = 203,
    DRIVER_INSTALL_DECLINE_BY_USER        = 204,
    DRIVER_ANDROID_DEVIECE_KNOW           = 205,
    DRIVER_ANDROID_DEVIECE_UNKNOW         = 206,

    // VIDEO_STREAM_INFO_STATISTIC           = 301,  // source device encode fps
    // VIDEO_STREAM_INFO_CLOCKDIFF           = 302,  // source device clock diff


} EVENT_TYPE;

// check and repair Items
typedef enum {
    kITEM_BONJOUR_INSTALLED     = 0x0001, // chk return 0, -1, -2
    kITEM_BONJOUR_RUNNING       = 0x0002, // chk return 0, -1, -3
    kITEM_FIREWARLL_BOUJOUR     = 0x0003, // chk return 0, -1, -4 -5, -6 ...-12 
    kITEM_FIREWARLL_MEDIASDK    = 0x0004  // chk return 0, -1, -4 -5, -13 ...-19 
} ENV_ITEM;


/* -------- 设备发现：PC Sink主动扫描Source设备 & 邀请镜像：PC Sink主动邀请Source镜像过来 -------- */
// 设备扫描事件枚举：作为OnBrowseEvent回调参数，表示设备扫描相关事件：
typedef enum {
    // 设备扫描启动，对应CastMateMirror_StartBrowse接口调用时产生；
    BROWSE_START    = 0x0001,
    // 设备发现，对应发现新的Source设备时产生；
    BROWSE_FOUND    = 0x0002,
    // 设备消失，对应设备下线消失时产生；
    BROWSE_LOST     = 0x0003,
    // 设备扫描停止，对应CastMateMirror_StopBrowse接口调用时产生；
    BROWSE_STOP     = 0x0004
} MDNS_BROWSE_EVENT;

// 设备类型枚举：作为source_device_info_t设备信息属性，表示镜像设备类型：
typedef enum {
    // 无线镜像设备类型（Android无线 & iOS无线 两者都用这个表示）；
    WIRELESS_SOURCE        = 0x0001,
    // Android有线镜像设备类型；
    WIRED_ANDROID_SOURCE   = 0x0002,
    // iOS有线镜像设备类型；
    WIRED_IOS_SOURCE       = 0x0003,

    UNKNOW_SOURCE          = 0x0004
} SOURCE_DEVICE_TYPE;

// 设备描述结构体：作为OnBrowseEvent回调参数，用于描述MDNS扫描发现的Source设备详细信息
typedef struct {
    // 设备类型：用于标识所发现设备的类型，枚举值：bytelink无线设备，android有线设备，iOS有线设备
    SOURCE_DEVICE_TYPE deviceType;
    // 设备唯一标识：邀请镜像接口，需要上层传入deviceKey来指定具体Source设备
    const char* deviceKey;
    // 设备名称：用于展示的设备名称
    const char* hostName;
    // 设备描述详细信息（包含Source端上层业务方自定义的字段，以json格式表示）
    const char* extraRecord;
} source_device_info_t;

typedef enum {
    ERROR_CODE_CLIENT_STOP = -1000,
    ERROR_CODE_CONNECTION_OVERFLOW = -1001
} invite_error_code_t;

// 回调接口：回调上层设备发现 & 邀请投屏事件，需要由接入层实现相关回调后通过接口设置到SDK
struct IInviteListener {
    void* cls;

    // 设备扫描事件回调：
    void (*OnBrowseEvent)(void* handle, void* cls,
         MDNS_BROWSE_EVENT event, source_device_info_t* sourceDevice);

    // 设备扫描错误回调，启动扫描失败时产生；
    void (*OnBrowseError)(void* handle, void* cls, int errorCode, const char* errorMessage);

    // 邀请镜像结果回调：result true： Source 接受邀请；result false：Source 拒绝邀请；
    void (*OnInviteResult)(void* handle, void* cls, bool result);

    // 邀请镜像错误回调，邀请镜像失败时产生；
    void (*OnInviteError)(void* handle, void* cls, int errorCode, const char* errorMessage);
};

typedef enum
{
    CAST_MIRROR_MODE_NONE     = -1,
    CAST_MIRROR_MODE_FLUENCY  = 0, // 流畅模式
    CAST_MIRROR_MODE_LOWDELAY = 1, // 低延时
    CAST_MIRROR_MODE_RESERVER = 2
} CastMirrorMode;

typedef enum
{   
    PREEMPT_MODE_FREE = 0,           // 自由抢占模式
    PREEMPT_MODE_AVOID_HARASS = 1,   // 防骚扰模式
    PREEMPT_MODE_RESTRICTED = 2      // 独占模式
} CastPreemptMode;

typedef enum  {
    kALLOW_THIS_TIME = 0,       // allow this time
    kREJECT_THIS_TIME = 1,      // reject this time
    kALLOW_ALL_THE_TIME = 2,    // allow all the time
    kREJECT_ALL_THE_TIME = 3    // reject all the time
} CastRejectPolicy;

#ifdef __cplusplus
} // namespace bytecast
#endif