﻿#pragma once

#include <string>
#include <functional>
#include <map>
#include <set>
#include <thread>
#include "MediaSDKV2Define.h"
#include "mediasdk_v2_header/nlohmann/json.hpp"
#include "NativeBridge/RTCLiveHelper.h"
#include "MediaSDKCallBack.h"
#include "LSPublicHeader.h"
#include "EnumConverter.h"
#include "V3HookAPILayer/V3HookAPILayer.h"
#include "V3HookAPILayer/VideoFrameProcessor.h"
#include "V3HookAPILayer/SharedMemoryRingBuffer.h"

namespace sdk_helper
{
	struct SourceInfo
	{
		std::string visual_id;
		bool        is_duplicate_source = false;
		std::string root_visual_id;
		int64_t     ref_count = 0;
		bool        detach = false;
	};

	struct VisualInfo
	{
		std::string visual_id;
		int32_t     track_id;
		bool        detach = true;
	};

	struct GameSourceInfo
	{
		bool show_cursor = false;
	};

class MediaSDKControllerImplV2;
MediaSDKControllerImplV2* MediaSDKControllerV2ImplInit();
void MediaSDKControllerV2ImplUninit();

using LogMessageHandler = std::function<void(int32_t severity, const char* szFile, int32_t line, const char* szText)>;

class GlobalEventManager;
class MediaSDKControllerImplV2
{
public:
	MediaSDKControllerImplV2();
	~MediaSDKControllerImplV2();

	void SetHost(const std::string& host);
	void UpdateHost(const std::string& host);

	virtual void SetLogMessageHandler(sdk_helper::LogMessageHandler handler);
	virtual void SetThreadMonitorEventHandler(ThreadMonitorEventHandler handler);

	virtual bool Init(const INITIALIZE_INFO& param);
	virtual void Uninit();

	// CanvasItem
	virtual bool CreateLayer(const std::string& layer_id, const std::string& canvas_id, const std::string& source_id, const LAYER_INFO& layer_info);
	virtual bool CreateLayerWithFilter(const std::string& layer_id, const std::string& canvas_id, const std::string& source_id, const LAYER_INFO& layer_info, bool need_preprocess);
    virtual bool CreateEffectVisualFilter(const std::string& layer_id, const LAYER_INFO& layer_info);
	virtual bool DestroyLayer(const std::string& canvas_item_id);
	virtual bool UpdateLayerBindSourceWithTransform(const std::string& layer_id, const std::string& source_id, const TRANSFORM& transform);

	// 窗口源
	virtual bool EnumWindows(const bool need_icon, std::vector<WINDOW_DESC>* output);
	virtual bool CreateWindowSource(const std::string visual_id, const WINDOW_SOURCE& window, VISUAL_CAPTURE_TYPE* cap_type, VISUAL_SOURCE_RESULT* result);
	virtual bool WindowSourceSelectWindow(const std::string& visual_id, int64_t win_id);
	virtual bool WindowSourceSelectWindow(const std::string& visual_id, const std::string& class_name);
	virtual bool WindowSourceShowCursor(const std::string& visual_id, const bool show);
	virtual bool WindowSourceIsShowCursor(const std::string& visual_id, bool* show);

	// 屏幕源
	virtual bool EnumMonitors(std::vector<MONITOR_DESC>* output);
	virtual bool CreateMonitorSource(const std::string visual_id, const MONITOR_SOURCE& monitor, VISUAL_CAPTURE_TYPE* cap_type, VISUAL_SOURCE_RESULT* result);
	virtual bool MonitorSourceSetMonitor(const std::string& visual_id, const std::string& did);
	virtual bool MonitorSourceShowCursor(const std::string& visual_id, const bool show);
	virtual bool MonitorSourceIsShowCursor(const std::string& visual_id, bool* show);

	// 游戏源
	virtual bool CreateGameSource(const std::string visual_id, const GAME_SOURCE& game, VISUAL_SOURCE_RESULT* result);
	virtual bool GameSourceShowCursor(const std::string& visual_id, const bool show);
	virtual bool GameSourceIsShowCursor(const std::string& visual_id, bool* show);
	virtual bool GameSourceSelectTarget(const std::string visual_id, const GAME_SOURCE& game);
	virtual bool GameSourceLimitCaptureFPS(const std::string& visual_id, const bool enable_limit_fps, const uint32_t limit_fps);
	virtual bool GameSourceSetShareDataMode(const std::string& visual_id, bool enable_cpu);

	// 图片源
	virtual bool CreateImageSource(const std::string visual_id, const IMAGE_SOURCE& image, VISUAL_SOURCE_RESULT* result);
	virtual bool ImageSourceSetFile(const std::string& visual_id, const std::string& image_filepath);

	// 媒体源
	virtual bool CreateMediaSource(const std::string visual_id, const FAV_SOURCE& fav, VISUAL_SOURCE_RESULT* result);
	virtual bool MediaSourcePause(const std::string& visual_id);
	virtual bool MediaSourceIsPaused(const std::string& visual_id, bool* is_paused);
	virtual bool MediaSourceResume(const std::string& visual_id);
	virtual bool MediaSourceIsSeekable(const std::string& visual_id, bool* is_seekable);
	virtual bool MediaSourceSeek(const std::string& visual_id, const int64_t position_sec, bool absolute);
	virtual bool MediaSourceGetDuration(const std::string& visual_id, int64_t* duration_1_1000000_sec);
	virtual bool MediaSourceEnableLoop(const std::string& visual_id, const bool enable);
	virtual bool MediaSourceEnableHardwareAcceleration(const std::string& visual_id, const bool enable);
	virtual bool MediaSourceResetFile(const std::string& visual_id, const std::string& filepath);

	// 获取字体
	virtual bool TextSourceGetFonts(std::vector<std::string>* font_list);

	// 外部浏览器源
	virtual bool CreateExtrenalBrowserSource(const std::string visual_id, const BROWSER_SOURCE& browser, VISUAL_SOURCE_RESULT* result);

	// 枚举设备
	virtual bool EnumVideoInputDevices(std::vector<DSHOW>* device_info_list);
	virtual bool EnumAudioInputDevices(std::vector<DSHOW>* device_info_list);
	virtual bool EnumDeviceSupportFormats(const DSHOW& device_info, std::vector<VIDEO_CAPTURE_FORMAT>* video_format_list, std::vector<AUDIO_CAPTURE_FORMAT>* audio_format_list);
	virtual bool EnumDeviceSupportFormatsByDeviceName(const DSHOW& device_info, std::vector<AUDIO_CAPTURE_FORMAT>* audio_format_list);

	virtual bool CreateCameraSource(const std::string visual_id, const CAMERA_SOURCE& camera, VISUAL_SOURCE_RESULT* result);
	virtual bool ReopenCameraSource(const std::string& source_id, const CAMERA_SOURCE& camera, VISUAL_SOURCE_RESULT* result);
	virtual bool CameraSourceSetPrepareTransform(const std::string& visual_id, const TRANSFORM& transform_info);
	virtual bool CameraSourceGetState(const std::string& visual_id, bool* camera_is_ready, CAMERA_FILTER_STATE* state, CAMERA_CREATE_STEP_STATE* create_step_state);
	virtual bool CameraSourceSetAmpProperty(const std::string& visual_id, int64_t win_id, VIDEO_PROCAMP_TYPE key, int32_t value, int32_t flag);
	virtual bool CameraSourceGetAmpProperty(const std::string& visual_id, int64_t win_id, std::vector<VIDEO_PROC_AMP>* properties);
	virtual bool CameraSourceSetControlProperty(const std::string& visual_id, int64_t win_id, CAMERA_CONTROL_TYPE key, int32_t value, int32_t flag);
	virtual bool CameraSourceGetControlProperty(const std::string& visual_id, int64_t win_id, std::vector<CAMERA_CONTROL>* properties);
	virtual bool CameraSourceSetDetectColorRangeParam(const std::string& visual_id, uint32_t detect_interval_sec, uint32_t limited_detect_count);
	virtual bool CameraSourceStopDetectColorRange(const std::string& visual_id);
	virtual bool CameraSourceSetMaxCaptureFps(const std::string& visual_id, const float max_fps);

	// 采集卡源
	virtual bool CreateCaptureCardSource(const std::string visual_id, const ANALOG_SOURCE& analog, VISUAL_SOURCE_RESULT* result);
	virtual bool ReopenAnalogSource(const std::string& source_id, const ANALOG_SOURCE& analog, VISUAL_SOURCE_RESULT* result);

	// 涂鸦源
	virtual bool CreateGraffitiSource(const std::string visual_id, const GRAFFITI_SOURCE& graffiti, VISUAL_SOURCE_RESULT* result);
	virtual bool GraffitiSourceSetEditState(const std::string& visual_id, const bool enable);
	virtual bool GraffitiSourceGetEditState(const std::string& visual_id, bool* enable);
	virtual bool SetLayerNeedDrawBorder(const std::string& layer_id, bool need_draw_border);
	virtual bool GraffitiSourceSetType(const std::string& visual_id, const GRAFFITI_TYPE type);
	virtual bool GraffitiSourceSetPen(const std::string& visual_id, const int32_t r, const int32_t g, const int32_t b, const float a, const float width);
	virtual bool GraffitiSourceRemoveAll(const std::string& visual_id, const bool restore);
	virtual bool GraffitiSourceRemove(const std::string& visual_id);
	virtual bool GraffitiSourceRecover(const std::string& visual_id);
	virtual bool GraffitiSourceHasTracks(const std::string& visual_id, bool* value);
	virtual bool GraffitiSourceHasRestores(const std::string& visual_id, bool* value);

	// 投屏源
	virtual bool CreateMobileProjectorSource(const std::string visual_id, const BYTELINK_SOURCE& bytelink, VISUAL_SOURCE_RESULT* result);
	virtual bool MobileProjectorSourceSetOption(const std::vector<BYTELINK_OPTION>& options);
	virtual bool MobileProjectorSourceCheckBonjour(bool* value);

	// RTC源
	virtual bool CreateRTCSource(const std::string source_id, const RTC_SOURCE& rtc, VISUAL_SOURCE_RESULT* result);

	// 虚拟摄像头
	virtual bool CreateVirtualCameraLayer(const std::string& canvas_item_id, const VIRTUAL_CAMERA_SOURCE& virtual_camera);
	virtual bool VirtualCameraSourceReopen(const std::string& ref_id);

	// Layer 通用
	virtual bool VisualDestroy(const std::string& source_id);
	virtual bool LayerSetVisible(const std::string& layer_id, const bool is_visible);

	virtual bool GetSourceFPS(const std::string& source_id, float* fps);
	virtual bool VisualGetEffectProfiler(const std::string& layer_id, EffectProfilerInfo* effect_profiler_info);
    virtual bool SelectLayer(uint32_t preview_id, const std::string& layer_id);
	virtual bool LayerSetLock(const std::string& layer_id, bool lock);
	virtual bool LayerClipMask(const std::string& layer_id);

	// Layer Z-Order
	virtual bool LayerSetOrder(const uint32_t preview_id, const std::vector<std::string>& layer_list);
	virtual bool LayerMoveTop(const std::string& layer_id);

	// Layer 保存画面
	virtual bool VisualSaveAsImage(const std::string& visual_id, const std::string& filepath);

	// Layer 设置标志
    virtual bool LayerSetAlwaysOnTop(const std::string& layer_id, const bool always_on_top);
    virtual bool LayerSetAvoidOutput(const std::string& layer_id, const bool avoid_output);
    virtual bool LayersSetExclude(const std::vector<std::string>& layer_ids, const int stream_index);

	// 限制源的移动范围, 0-1范围
	virtual bool LayerSetMoveRange(const std::string& layer_id, const float x0, const float y0, const float x1, const float y1);

	// Layer 通用 -- Transform 相关
	virtual bool LayerSetTransform(const std::string& layer_id, const TRANSFORM& info);
	virtual bool LayerSetPosition(const std::string& layer_id, const float x, const float y);
	virtual bool LayerSetScale(const std::string& layer_id, float scale_x, float scale_y);
	virtual bool LayerSetMinScale(const std::string& layer_id, float min_scale_x, float min_scale_y);
    virtual bool LayerSetMaxScale(const std::string& layer_id, float max_scale_x, float max_scale_y);
	virtual bool LayerSetRotate(const std::string& layer_id, const float angle);
	virtual bool LayerSetVerticalFlip(const std::string& layer_id, const bool flip);
	virtual bool LayerSetHorizontalFlip(const std::string& layer_id, const bool flip);
	virtual bool LayerSetClip(const std::string& layer_id, float x, float y, float z, float w);
	virtual bool SetVirtualCameraFlipV(const bool flip);
	virtual bool SetVirtualCameraFlipH(const bool flip);
	virtual bool SetVirtualCameraRotate(const float angle);
	virtual bool SetVisualDestroyedWhenAllReferenceRemoved(const std::string& visual_id, bool destroy);

	// 获取视频和图片的帧宽高、旋转角
	virtual bool GetMediaFileInfo(const std::string& file_path, MATERIAL_DESC* param);

	/*---------------------------------------------- Visual Filter ----------------------------------------------*/
	virtual bool MediaFilterDestroy(const std::string& layer_id, const std::string& filter_id, FILTER_TYPE type, const std::string& filter_type = "");
    virtual bool MediaFilterSetActive(const std::string& media_id, const std::string& filter_id, bool active, FILTER_TYPE type, const std::string& filter_type = "");
	virtual bool CreateCornerVisualFilter(const std::string& visual_id, const std::string& filter_id, const CornerFilter& param);
	virtual bool CreateEdgeVisualFilter(const std::string& visual_id, const std::string& filter_id, const EdgeFilter& param);
	virtual bool CreateMediaColorAdjustFilter(const std::string& media_id, const std::string& filter_id, const ColorAdjustFilter& param, FILTER_TYPE type);
	virtual bool CreateChromaKeyVisualFilter(const std::string& visual_id, const std::string& filter_id, const ChromaKeyFilter& param);
    virtual bool CreateScaleVisualFilter(const std::string& visual_id, const std::string& filter_id, const ScaleFilter& param);
	virtual bool CreateShapeVisualFilter(const std::string& visual_id, const std::string& filter_id, const ShapeFilter& param);
	virtual bool CreateColorLutVisualFilter(const std::string& visual_id, const std::string& filter_id, const ColorLutFilter& param);
	virtual bool CreateSharpnessVisualFilter(const std::string& visual_id, const std::string& filter_id, const SharpnessFilter& param);
	virtual bool CreateOverlayVisualFilter(const std::string& visual_id, const std::string& filter_id, const OverlayFilter& param);
	virtual bool CreateHintVisualFilter(const std::string& visual_id, const std::string& filter_id, const HintFilter& param);

	virtual bool SetColorFilterSaturation(const std::string& media_id, const std::string& filter_id, float saturation, FILTER_TYPE type);
    virtual bool SetColorFilterHueShift(const std::string& media_id, const std::string& filter_id, float hue_shift, FILTER_TYPE type);
    virtual bool SetColorFilterAddColor(const std::string& media_id, const std::string& filter_id, uint32_t add_color, FILTER_TYPE type);
    virtual bool SetColorFilterMulColor(const std::string& media_id, const std::string& filter_id, uint32_t mul_color, FILTER_TYPE type);
    virtual bool SetColorFilterBrightness(const std::string& media_id, const std::string& filter_id, const std::string& filter_type, float brightness, FILTER_TYPE type);
	virtual bool SetColorFilterGamma(const std::string& media_id, const std::string& filter_id, const std::string& filter_type, float gamma, FILTER_TYPE type);
    virtual bool SetColorFilterContrast(const std::string& media_id, const std::string& filter_id, const std::string& filter_type, float contrast, FILTER_TYPE type);
    virtual bool SetColorFilterOpacity(const std::string& media_id, const std::string& filter_id, const std::string& filter_type, float opacity, FILTER_TYPE type);
	virtual bool SetColorFilterChroma(const std::string& layer_id, const std::string& filter_id, uint32_t chroma);
	virtual bool SetColorFilterSimilarity(const std::string& layer_id, const std::string& filter_id, float similarity);
	virtual bool SetColorFilterSmoothness(const std::string& layer_id, const std::string& filter_id, float smoothness);
	virtual bool SetColorFilterSpill(const std::string& layer_id, const std::string& filter_id, float spill);
	virtual bool SetOverlayFilterProperty(const std::string& layer_id, const std::string& filter_id, const OverlayFilter& param);
	virtual bool SetHintFilterProperty(const std::string& layer_id, const std::string& filter_id, const HintFilter& param);
    virtual bool SetColorLutFilterProperty(const std::string& layer_id, const std::string& filter_id, const ColorLutFilter& param);
	virtual bool SetSharpnessFilterProperty(const std::string& layer_id, const std::string& filter_id, const SharpnessFilter& param);
    virtual bool SetScaleFilterProperty(const std::string& layer_id, const std::string& filter_id, const ScaleFilter& param);
	virtual bool SetShapeFilterProperty(const std::string& layer_id, const std::string& filter_id, const ShapeFilter& param);
	virtual bool ResetFilterOrder(const std::string& layer_id, const std::vector<std::string>& filter_ids);

	/*---------------------------------------------- Audio Filter ----------------------------------------------*/
	virtual bool AudioFilterDestroy(const std::string& audio_id, const std::string& filter_id);
	virtual bool AudioFilterSetEnable(const std::string& audio_id, const std::string& filter_id, bool enable);
	virtual bool AudioFilterGetEnable(const std::string& audio_id, const std::string& filter_id, bool* enable);

	virtual bool CreateSamiCommonMetricsAudioFilter(const std::string& audio_id, const std::string& filter_id, const SAMI_COMMON_METRICS_FILTER& param);
	virtual bool SamiCommonMetricsAudioFilterReset(const std::string& audio_id, const std::string& filter_id);

	virtual bool CreateSamiNoiseSuppressAudioFilter(const std::string& audio_id, const std::string& filter_id);
	virtual bool SamiNoiseSuppressAudioFilterSetModel(const std::string& audio_id, const std::string& filter_id, const std::string& config_file);
	virtual bool SamiNoiseSuppressAudioFilterSetSpeechRatio(const std::string& audio_id, const std::string& filter_id, const float speech_ratio);

	virtual bool CreateSpeexNoiseSuppressAudioFilter(const std::string& audio_id, const std::string& filter_id);
	virtual bool SpeexNoiseSuppressAudioFilterSetSuppressLevel(const std::string& audio_id, const std::string& filter_id, int32_t suppress_level);
	virtual bool SpeexNoiseSuppressAudioFilterGetSuppressLevel(const std::string& audio_id, const std::string& filter_id, int32_t* suppress_level);

	virtual bool CreateSamiMdspEffectAudioFilter(const std::string& audio_id, const std::string& filter_id, bool enable, const SAMI_MDSP_EFFECT_FILTER& param);
	virtual bool SamiMdspEffectAudioFilterSetParam(const std::string& audio_id, const std::string& filter_id, std::string param);

	virtual bool EffectPlatformInit(const INIT_EFFECT_PLATFORM& param);
	virtual bool EffectPlatformUninit();
	virtual bool EffectPlatformLoadModels(const std::string& request_id, const std::string& model_name, const std::vector<std::string>& requirements);
	virtual bool EffectPlatformUpdateConfig(const std::string& user_id, const std::string& ttls_hardware_level);

	virtual bool EffectEnable(const std::string& layer_id, bool enable);
	virtual bool EffectSetBkImage(const std::string& layer_id, const std::string& key, const std::string& filepath);
    virtual bool EffectComposerAdd(const std::string& layer_id, const std::vector<std::string>& composer_list, const std::vector<std::string>& tag_list);
    virtual bool EffectComposerSet(const std::string& layer_id, const std::vector<std::string>& composer_list, const std::vector<std::string>& tag_list);
	virtual bool EffectComposerRemove(const std::string& layer_id, const std::vector<std::string>& composer_list);
    virtual bool EffectComposerUpdate(const std::string& layer_id, const std::string& path, const std::string& key, float value, const std::string& tag_list);
    virtual bool EffectComposerReplace(const std::string& layer_id, const std::vector<std::string>& old_composer_list, const std::vector<std::string>& new_composer_list, const std::vector<std::string>& tag_list);
	virtual bool EffectComposerSetText(const std::string& layer_id, const std::string& key, const std::string& value);
	virtual bool EffectSetMsg(const std::string& layer_id, const EFFECT_MSG& effect_msg);
	virtual bool EffectComposerGetExclusion(const std::string& layer_id, const std::string& node_path, const std::string& node_tag, int32_t* res);
	virtual bool EffectSetPicQualityBrightness(const std::string& visual_id, const BRIGHT_CONFIG& param);
	virtual std::string ComposerToStr(const EFFECT_COMPOSER& composer);

	/*---------------------------------------------- 音频相关接口 ----------------------------------------------*/
	// WASAPI音频源
	virtual bool CreateWASAPIAudioSource(const AUDIO_INFO& param);
	virtual bool WASAPIAudioSourceEnumInputDevices(std::vector<DSHOW>* info_list);
	virtual bool WASAPIAudioSourceEnumOutputDevices(std::vector<DSHOW>* info_list);
	virtual bool WASAPIAudioSourceGetDefaultInputDevice(DSHOW* info);
	virtual bool WASAPIAudioSourceGetDefaultOutputDevice(DSHOW* info);

	// APP音频源
	virtual bool CreateAppAudioSource(const AUDIO_INFO& param);
	virtual bool AppAudioSourceEnum(std::vector<DSHOW>* output_list);
	virtual bool AppAudioSourceIsSystemSupport(bool* support);

	// PCM音频源
	virtual bool CreatePCMAudioSource(const AUDIO_INFO& param);
	virtual bool PCMAudioSourceUpdate(const std::string& audio_id, const AUDIO_INFO& data);

	// 音频输入管理
    virtual bool AudioSourceDestroy(const std::string& audio_id);
    virtual bool AudioSourceSetProperty(const std::string& audio_id, const AUDIO_SETTING& prop);
	virtual bool AudioSourceIsMute(const std::string& audio_id, bool* mute);
	virtual bool AudioSourceSetMute(const std::string& audio_id, const bool mute);
	virtual bool AudioSourceMixToMono(const std::string& audio_id, const bool enable);
	virtual bool AudioSourceIsEnableMixToMono(const std::string& audio_id, bool* enable);
	virtual bool AudioSourceSetBalance(const std::string& audio_id, const float val);
	virtual bool AudioSourceGetBalance(const std::string& audio_id, float* val);
	virtual bool AudioSourceSetSyncOffset(const std::string& audio_id, const int32_t sync_offset);
	virtual bool AudioSourceGetSyncOffset(const std::string& audio_id, uint32_t* sync_offset);
	virtual bool AudioSourceSetTracks(const std::string& audio_id, uint32_t audio_track_ids);
	// 音量增益系数
	virtual bool AudioSourceSetVolumn(const std::string& audio_id, const float volume);
	virtual bool AudioSourceGetVolumn(const std::string& audio_id, float* volume);
	// 统计音量频率
	virtual bool AudioSourceSetInterval(const std::string& audio_id, const int32_t interval_ms);
	virtual bool AudioSourceSetMonitorType(const std::string& audio_id, const AUDIO_MONITOR_TYPE type);
	virtual bool AudioSourceGetMonitorType(const std::string& audio_id, AUDIO_MONITOR_TYPE* type);
	// 性能消耗统计
	virtual bool AudioSourceGetPerformance(const std::string& audio_id, AUDIO_PERFORMANCE_INFO* info);

	// AEC
	virtual bool AudioSourceSetRenderDeviceID(const std::string& render_device_id);

	// Lyrax
	virtual bool LyraxEngineCreateAudioSource(const AUDIO_INFO& param);
	virtual bool LyraxEngineSetAudioRefId(const std::string& audio_id, const std::string& ref_id);
	virtual bool LyraxEngineSetAECOption(const std::string& audio_id, bool enable);
	virtual bool LyraxEngineSetANSOption(const std::string& audio_id, AUDIO_ANS_OPTION option);
	virtual bool LyraxEngineSetAGCOption(const std::string& audio_id, int option);
    virtual bool LyraxEngineEnableAudioInputEchoDetection(const std::string& audio_id, const int interval);
		virtual bool LyraxEngineEnableAudioInputNoiseDetection(const std::string& audio_id, const int interval, const int duration);
    virtual bool LyraxEngineSetAudioInputRenderDeviceID(const std::string& audio_id, const char* render_device_id);

	/*---------------------------------------------- 视频编码推流相关接口 ----------------------------------------------*/
	// 推流,录制
	virtual bool StartStream(const std::vector<OUTPUT_INFO>& params);
	virtual bool StopStream(const std::vector<STOP_STREAM_PARAM>& stream_params);
	virtual bool StreamSetRoomID(const std::string& room_id);
	virtual bool SetIFrameSEI(const std::vector<SEI_INFO>& params);

	// 视频相关设置
	virtual bool EnumVideoEncoders(std::vector<VIDEO_ENCODER_INFO>* video_encoder_info_list);
    virtual bool SetVideoSetting(const std::map<uint32_t, VideoParam>& videoModelMapVideoParam);

	virtual bool StreamSetAbrConfig(const std::string& stream_id, uint32_t offset);
	virtual bool EncoderGetInfo(const std::string& avcodec_tag, int32_t* error_code, VIDEO_CODEC_PARAM* video_codec_param);
	virtual bool IsStreamInProcess(const std::string& streamID, bool* is_streaming);

	/*---------------------------------------------- 网络测试相关 ----------------------------------------------*/
	virtual bool BwProbeStartStream(const OUTPUT_INFO& param, bool* already_running);
	virtual bool BwProbeStopStream(const std::string& stream_id, bool* is_fallbacking);

	/*---------------------------------------------- 预览画布相关接口 ----------------------------------------------*/
	// 预览窗口相关操作
    virtual bool CreateModel(const PREVIEW_INFO& preview_info);
	virtual bool DestroyModel(const uint32_t video_model);
	virtual bool PreviewWindowSetPosition(const uint32_t preview_id, const Gdiplus::RectF& view_rect, const Gdiplus::RectF& view_rect_layout);
	virtual bool PreviewWindowEnableShow(const uint32_t preview_id, bool show);
	virtual bool PreviewWindowEnableDraw(const uint32_t preview_id, const bool enable);
	virtual bool PreviewWindowEnableInteract(const uint32_t preview_id, const bool enable);
	virtual bool SetCanvasSelectBorderType(const uint32_t preview_id, CANVAS_SELECTION_BORDER_TYPE border_type);
	virtual bool PreviewWindowOpenProjector(const std::string& preview_id, const int32_t track_id, const int64_t win_id, const int32_t x, const int32_t y, const int32_t width, const int32_t height, const PREVIEW_PARAMS& params);
	virtual bool PreviewWindowCloseProjector(const std::string& preview_id, const int32_t track_id);
    virtual bool PreviewWindowSaveAsImage(const uint32_t preview_id, const IMAGE_FORMAT fmt, const std::string& file_path);
    virtual bool SetProjectorWndParams(const std::string& preview_id, const int32_t track_id, const PREVIEW_PARAMS& params);
    virtual bool SetProjectorPosition(const std::string& preview_id, const int32_t track_id, const Gdiplus::RectF& rect);

	// 画布相关操作
	virtual bool CreateCanvas(const std::string& canvas_id);
	virtual bool DestroyCanvas(const std::string& canvas_id);
	virtual bool GetCanvasOnPreview(const uint32_t preview_id, std::string* canvas_id);
    virtual bool SetCanvasToPreview(const uint32_t preview_id, const std::string& canvas_id, const std::string& transition_id);
    virtual bool CreateTransition(const std::string& canvas_id, const std::string& transition_id, const TransitionFilter& transition_filter);
	virtual bool DestroyTransition(const std::string& canvas_id, const std::string& transition_id);
    virtual bool SetTransitionProperty(const std::string canvas_id, const std::string& transition_id, const TransitionFilter& transition_filter);
    virtual bool VibeTriggerEffect(const VIBE_TRIGGER_EFFECT& vibe_trigger_effect, std::vector<std::pair<std::string, bool>>& result_action_ids);

	// 源预览窗口
    virtual bool CreatePreview(const PREVIEW_INFO& preview_info);
    virtual bool DestroyPreview(const uint32_t preview_id);
	virtual bool StartLayerPreview(const std::string& layer_id, const std::string& preview_id, const int64_t parent_win_id, const LAYER_PREVIEW& setting, const PREVIEW_PARAMS& params);
	virtual bool StopLayerPreview(const std::string& preview_id);
	virtual bool SetLayerPreviewSetting(const std::string& preview_id, const LAYER_PREVIEW& setting);
	virtual bool SetLayerPreviewParams(const std::string& visual_preview_id, const PREVIEW_PARAMS& params);

	/*---------------------------------------------- RTC相关接口 ----------------------------------------------*/
	virtual bool RTCControllerStart(const RTC_LINK& param);
	virtual bool RTCControllerStop();
	virtual bool RTCControllerStartScreenShare(UINT32 videoModelID, const CLIP_AREA_INFO& info, bool enableAudio, UINT32 audioTrack);
	virtual bool RTCControllerStopScreenShare(const int32_t track_id);
	virtual bool RTCControllerUpdateScreenShareClipInfo(const CLIP_AREA_INFO& param);
	virtual bool RTCControllerSetAudioOutput(const std::string& device_id);
	virtual bool RTCControllerSendRTCUserMessage(const std::string& uid, const std::string& msg);
	virtual bool RTCControllerSendRTCRoomMessage(const std::string& msg);
	virtual bool RTCControllerEnableLocalAudio(const bool enable);
	virtual bool RTCControllerEnableLocalVideo(const bool enable);
	virtual bool RTCControllerSetAudioPropertiesReport(const int32_t interval);
	virtual bool RTCControllerMuteRemoteAudio(const std::string& uid, const int32_t stream_index, const bool mute);
	virtual bool RTCControllerPublishVideoStream(MEDIA_STREAM_TYPE type);
	virtual bool RTCControllerUnPublishVideoStream(MEDIA_STREAM_TYPE type);
	virtual bool RTCControllerStartLiveTranscoding(const std::string& agrs);
	virtual bool RTCControllerUpdateLiveTranscoding(const std::string& agrs);
	virtual bool RTCControllerStopLiveTranscoding(const std::string& agrs);
	virtual bool RTCControllerStartForwardStreamToRooms(const std::vector<ROOM_INFO>& info_list, int32_t* ret);
	virtual bool RTCControllerUpdateForwardStreamToRooms(const std::vector<ROOM_INFO>& info_list, int32_t* ret);
	virtual bool RTCControllerStopForwardStreamToRooms();
	virtual bool RTCControllerUpdateClipScaleInfo(const CLIP_AREA_INFO& param);
	virtual bool RTCControllerSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type);
	virtual bool RTCControllerUnSubscribeScreen(const std::string& uid, MEDIA_STREAM_TYPE type);

	/*---------------------------------------------- 统计相关接口 ----------------------------------------------*/
	virtual bool GetStreamActiveStatistic(const std::string& stream_id, ACTIVE_STATISTIC* info);
	virtual bool GetActiveFPS(float* fps);
	virtual bool ReportTeaDataRenderProfiler();
	virtual bool ReportTeaDataPerformanceMatrics(const std::vector<PERFORMANCE_MATRICS>& param_list);
	virtual bool SetTTNtpMS(uint64_t ntp_ms, uint64_t local_ms);
	virtual bool CheckEncoderSession(const std::string& encode_name, uint32_t count, int32_t* result);
	virtual bool GetPresentNotReadyFPS(float* present_not_ready_fps);

	/*----------------------------------------------更新实验配置----------------------------------------------*/
	virtual bool UpdateDynamicConfig(const std::string& json_info);

	/*---------------------------------------------- 消息透传 ----------------------------------------------*/
	virtual bool OnKeyboardEvent(uint64_t wparam, uint64_t lparam);

	/*---------------------------------------------- Mock ----------------------------------------------*/
	virtual bool MockFrozen(int32_t ipc_sleep_timeout_ms, int32_t render_sleep_timeout_ms);

	/*---------------------------------------------- VideoQualityManager ----------------------------------------------*/
	virtual bool VideoQualityManagerInitialize(const VideoQualityManagerInitializeParam& param, VideoQualityManagerGoLiveParamsOut* default_go_live_params);
	virtual bool VideoQualityManagerQueryCameraRecommendedParams(const VideoQualityManagerQueryCameraRecommendedParamsRequest& request, VideoQualityManagerQueryCameraRecommendedParamsResponse* response);
	virtual bool VideoQualityManagerQueryGoLiveRecommendedParams(const VideoQualityManagerQueryGoLiveRecommendedParamsRequest& request, VideoQualityManagerQueryGoLiveRecommendedParamsResponse* response);
	virtual bool VideoQualityManagerQueryManuallySelectedResult(const VideoQualityManagerQueryManuallySelectedResultRequest& request, VideoQualityManagerQueryManuallySelectedResultResponse* response);
	virtual bool VideoQualityManagerQueryCameraBestParamsForTarget(const VideoQualityManagerQueryCameraBestParamsForTargetRequest& request, VideoQualityManagerQueryCameraBestParamsForTargetResponse* response);
	virtual bool StartAdaptiveGearStrategyReport(const std::string& stream_id, const std::string& abr_config);

	virtual bool ReconfigVideoOutput(const VideoOutputParamsRequest& request, VideoOutputParamsResponse* response);
	virtual bool FallbackVideoEncoder(const FallbackVideoEncoderParamsRequest& request);
	virtual bool SetPreprocessDefaultSize(const std::string& visual_id, int cx, int cy);
	virtual bool RemovePreprocessDefaultSize(const std::string& visual_id);
	virtual bool GetVisualFrame(const VisualTextureReq& request, VisualTextureRsp* response);
    virtual bool EnableFineTuning(uint32_t preview_id, bool enable);
	virtual v3::AudioFrameProcessor* GetAudioFrameProcessor();
    virtual v3::VideoFrameProcessor* GetVideoFrameProcessor();

private:
	bool AddSourceInfo(const std::string& visual_id, const bool detached);
	bool RemoveSourceInfo(const std::string& visual_id);

	bool CreateSamiNoiseSuppressAudioFilterActually(const std::string& audio_id, const std::string& filter_id);
	void SetPreviewDefaultSetting(int32_t preview_id);
	bool AudioSourceSetTracksActually(const std::string& audio_id, const std::vector<int>& addVec, const std::vector<int>& removeVec);

	static mediasdk::CreateCanvasItemParams GetCanvasItemParam(TRANSFORM transform, bool visible);
	mediasdk::CreateVisualFilterParams GetVisualFilterParams(const std::vector<FILTER>& filters, std::vector<std::string>& composers, EFFECT_FILTER& effect_filter, std::vector<std::string>& tags);
	mediasdk::StreamParams MapStartStreamContext(const OUTPUT_INFO& info);
	mediasdk::StreamParams MapStartStreamParam(const OUTPUT_INFO& param);
	nlohmann::json MapVideoCodecParam(const std::string& codec_id, const VIDEO_CODEC_PARAM& param);
	nlohmann::json MapAudioCodecParam(const std::string& codec_id, const AUDIO_CODEC_PARAM& param);
	nlohmann::json MapStreamCdnList(const OUTPUT_INFO& param);
	nlohmann::json MapStreamSourceConfig(const OUTPUT_INFO& param);
	nlohmann::json MapRecordSourceConfig(const OUTPUT_INFO& param);

	void StartConnectAudioCallback();
    void StopConnectAudioCallback();
    void ConnectAudioCallback();
	void OnMonoAudioPCMS16DataCallback(
        const std::string&                               audio_input_id,
        const std::string&                               capture_audio_id,
        v3::AudioFrameProcessor::AudioInputFrameTypeEnum frame_type,
        int64_t                                          timestamp_ns,
        int16_t*                                         pcm_s16_data,
        int32_t                                          nb_sample_count);

private:
	std::unique_ptr<SDKCallBackMgr> sdk_callback_mgr_;
	sdk_helper::CallHelper call_helper_;

    std::unique_ptr<v3::HookAPILayer>           hook_api_layer_;
    std::unique_ptr<v3::SharedMemoryRingBuffer> audio_output_buffer_;
    std::thread                                 audio_output_buffer_connect_thread_;
    std::atomic_bool                            audio_output_buffer_connect_thread_stop_ = true;

	std::atomic_bool				  has_init_ = false;
	std::mutex                        source_info_map_mutex_;
	std::map<std::string, SourceInfo> source_info_map_;

	std::mutex                        visual_info_mutex_;
	std::map<std::string, VisualInfo> visual_info_;

	std::mutex                            game_source_info_mutex_;
	std::map<std::string, GameSourceInfo> game_source_info_;

	std::map<std::string, int32_t> visual_track_map_;

	// 相机
	std::map<std::string, bool> camera_source_visual_visible_;

	// 复制源
	std::set<std::string> dup_source_visual_id_;

	// rtc
	uint32_t                       rtc_video_track_id_ = 0;
	uint32_t                       rtc_audio_track_id_ = 0;
	int                            rtc_fps_ = 0;
	std::unique_ptr<RTCLiveHelper> rtc_helper_;
	uint32_t                       screenShare_audio_track_ids_ = 0;
	bool                           screenShare_enable_audio_ = false;
	// audio filter
	std::map<std::string, std::string> SamiNS_model_path_map_;
	std::map<std::string, std::string> Sami_filter_id_map_;
	bool                               Sami_create_flag_ = false;

	// 网络测试
	bool already_running_ = false;

	// 避免重复删除音频轨道
	std::set<std::string> audio_track_not_remove_;

	// 虚拟摄像头
	std::string virtual_camera_id_;

	// 鼠标点击
	bool     has_track_id_ = false;
	uint32_t click_preview_id_ = 0;

	// 音频
	std::unordered_map<std::string, uint32_t> audio_id_map_;

	bool hasRTMPQ_ = false;

	// host info
	std::string init_json_;
	std::string host_info_;
};

} // namespace sdk_helper
