﻿#include "Scene.h"
#include "ModeSceneMgr.h"
#include "MediaSDKControllerV2Impl.h"
#include "stringutil.h"

extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;

Scene::Scene() {}

Scene::~Scene() {}

void Scene::SetSceneID(UINT64 id)
{
	m_sceneInfo.id = id;	
}

void Scene::SetSceneInfo(const SCENE_INFO* sceneInfo)
{
	m_sceneInfo = *sceneInfo;
}

void Scene::SetModeID(UINT32 id)
{
	m_sceneInfo.mode = (LIVE_MODE)id;
}

void Scene::CreateCanvas(UINT32 oldCanvasIdx, Canvas* canvas, UINT64 canvasID)
{
	if (!canvas)
		return;
	canvas->SetSceneID(m_sceneInfo.id);

	CANVAS_INFO_EX canvasInfoEx;
    canvas->GetCanvasInfo(&canvasInfoEx);
	if (!canvas->Create())
	{
        LOG(ERROR) << "[Scene::CreateCanvas] Create Real Canvas failed, canvasID: " << canvasInfoEx.id;
        return;
	}

	UINT32 videoModel = UINT32_MAX;
	// Unique Preview, example: StudioMode Preview
	Preview* pView = ModeSceneMgr::GetInstance()->GetPreviewByID(canvasInfoEx.previewID);
	if (!pView)
	{
		// Exist VideoModel Bind Preview
        videoModel = ModeSceneMgr::GetInstance()->GetVideoModelByOldCanvasIdx(oldCanvasIdx, m_sceneInfo.mode);
        pView = ModeSceneMgr::GetInstance()->GetPreviewByVideoModel(videoModel, &canvasInfoEx.previewID);
	}
	
    if (pView)
	{
        PREVIEW_INFO viewInfo;
        pView->GetPreviewInfo(&viewInfo);
        viewInfo.canvasIDs.push_back(canvasID);
        pView->SetPreviewInfo(&viewInfo);

        canvasInfoEx.layoutRect = viewInfo.layoutRect;
	}
	else
	{
		// Preview Not Exist
        PREVIEW_INFO viewInfo;
        viewInfo.videoModel = videoModel;
        viewInfo.canvasIDs.push_back(canvasID);
        UINT64 previewID = ModeSceneMgr::GetInstance()->AddPreview(&viewInfo, nullptr);
		if (previewID > 0)
		{
            canvasInfoEx.previewID = previewID;
            canvasInfoEx.layoutRect = viewInfo.layoutRect;
		}
		else
		{
            LOG(ERROR) << "[Scene::CreateCanvas] AddPreview failed, canvasID: " << canvasID;
		}
	}

	canvasInfoEx.isCreated = true;
	canvas->SetCanvasInfo(&canvasInfoEx);
    m_canvas.insert(std::pair<UINT32, Canvas*>(oldCanvasIdx, canvas));
}

bool Scene::DestroyCanvas(Canvas* pCanvas)
{
	if (!pCanvas)
		return false;

	for (std::map<UINT32, Canvas*>::iterator it = m_canvas.begin(); it != m_canvas.end(); it++)
	{
        if (it->second == pCanvas && pCanvas->Destroy())
		{
			m_canvas.erase(it);
            return true;
		}
	}

	return false;
}

bool Scene::RemoveCanvas(Canvas* pCanvas)
{
    if (!pCanvas)
        return false;

    for (std::map<UINT32, Canvas*>::iterator it = m_canvas.begin(); it != m_canvas.end(); it++)
    {
        if (it->second == pCanvas)
        {
            m_canvas.erase(it);
            pCanvas->SetSceneID(0);
            return true;
        }
    }

    return false;
}

Canvas* Scene::GetCanvas(UINT32 oldCanvasIdx)
{
	std::map<UINT32, Canvas*>::iterator it = m_canvas.find(oldCanvasIdx);
	if (it == m_canvas.end())
		return 0;
	return it->second;
}

void Scene::ReBindCanvas(UINT64 oldCanvasID, UINT64 newCanvasID)
{
    LOG(INFO) << "[Scene::ReBindCanvas] sceneID: " << m_sceneInfo.id << ", oldCanvasID: " << oldCanvasID << ", newCanvasID: " << newCanvasID;
	if (Canvas* pOldCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(oldCanvasID))
	{
        RemoveCanvas(pOldCanvas);
	}

	CANVAS_INFO_EX newCanvasInfo;
	if (Canvas* pNewCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(newCanvasID))
	{
        m_canvas.insert(std::pair<UINT32, Canvas*>(static_cast<UINT32>(m_sceneInfo.mode), pNewCanvas));
        pNewCanvas->SetSceneID(m_sceneInfo.id);
	}
}

void Scene::Select(bool in, SWITCH_SCENE_TYPE type)
{
	for (int i = 0; i < 2; ++i)
	{
		Canvas* pCanvas = GetCanvas(i);
		if (pCanvas)
		{
			if (type == SWITCH_SCENE_NORMAL)
			{
				pCanvas->Select(in);
			}
			else if (type == SWITCH_SCENE_PRELOAD)
			{
				pCanvas->PreLoadCanvas(in);
			}
			else if (type == SWITCH_SCENE_RELOAD)
			{
				pCanvas->ReLoadCanvas(in);
			}
			else if (type == SWITCH_SCENE_REPRELOAD)
			{
				pCanvas->RePreLoadCanvas(in);
			}
		}
	}
}

void Scene::GetSceneInfo(SCENE_INFO_EX* info, bool child /*= false*/)
{
	*(SCENE_INFO*)info = m_sceneInfo;
	if (child)
	{
		for (int i = 0; i < 2; i++)
		{
			Canvas* pCanvas = GetCanvas((LIVE_MODE)i);
			if (pCanvas)
			{
				CANVAS_INFO_EX canvasInfo = {};
				pCanvas->GetCanvasInfo(&canvasInfo, true);
				info->canvas.push_back(canvasInfo);
			}
		}
	}
}

void SceneLandscape::InitializeCanvas()
{
	CANVAS_INFO canvasInfo = {};
	canvasInfo.sceneID = m_sceneInfo.id;
	canvasInfo.oldCanvasIdx = LIVE_MODE_LANDSCAPE;
	canvasInfo.id = 0;
    canvasInfo.previewID = m_sceneInfo.previewID;
	UINT64 canvasID = ModeSceneMgr::GetInstance()->AddCanvas(&canvasInfo, 0);
	Canvas* canvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
	CreateCanvas(canvasInfo.oldCanvasIdx, canvas, canvasID);
}

void ScenePortrait::InitializeCanvas()
{
	CANVAS_INFO canvasInfo = {};
	canvasInfo.sceneID = m_sceneInfo.id;
	canvasInfo.oldCanvasIdx = LIVE_MODE_PORTRAIT;
	canvasInfo.id = 0;
    canvasInfo.previewID = m_sceneInfo.previewID;
	UINT64 canvasID = ModeSceneMgr::GetInstance()->AddCanvas(&canvasInfo, 0);
	Canvas* canvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
    CreateCanvas(canvasInfo.oldCanvasIdx, canvas, canvasID);
}

void SceneDBCanvas::InitializeCanvas()
{
	for (int i = 0; i < 2; i++)
	{
		CANVAS_INFO canvasInfo = {};
		canvasInfo.sceneID = m_sceneInfo.id;
        canvasInfo.oldCanvasIdx = (LIVE_MODE)i;
		canvasInfo.id = 0;
		UINT64 canvasID = ModeSceneMgr::GetInstance()->AddCanvas(&canvasInfo, 0);
		Canvas* canvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID);
        CreateCanvas(canvasInfo.oldCanvasIdx, canvas, canvasID);
	}
}
