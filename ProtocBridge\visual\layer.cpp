#include "stdafx.h"
#include "layer.h"

namespace LS
{
LSLayer::RequestList LSLayer::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<LSLayer::CreateLayer>());
    list.push_back(std::make_unique<LSLayer::DestoryLayer>());
    list.push_back(std::make_unique<LSLayer::CreateSceneLayer>());
    list.push_back(std::make_unique<LSLayer::SetLayerInfo>());
    list.push_back(std::make_unique<LSLayer::GetLayerInfo>());
    list.push_back(std::make_unique<LSLayer::GetLayerRefSourceID>());
    list.push_back(std::make_unique<LSLayer::AddFilter>());
    list.push_back(std::make_unique<LSLayer::RemoveFilter>());
    list.push_back(std::make_unique<LSLayer::ResetFilterOrder>());
    list.push_back(std::make_unique<LSLayer::SelectLayer>());
    list.push_back(std::make_unique<LSLayer::UnSelectLayer>());
    list.push_back(std::make_unique<LSLayer::MoveLayerOrder>());
    list.push_back(std::make_unique<LSLayer::ClipLayer>());
    list.push_back(std::make_unique<LSLayer::GetLayerSnapshot>());
    list.push_back(std::make_unique<LSLayer::GetLayerSnapshot2>());
    list.push_back(std::make_unique<LSLayer::UpdateLayerSource>());
    return list;
}

bool LSLayer::CreateLayer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    LAYER_INFO iLayerInfo{};
    UINT64     sourceID = 0;
    Util::StringToNum(req.source_id(), &sourceID);

    auto metas = controller->GetCompositeMetas();
    for (const auto& meta : metas)
    {
        if (meta.second.fallbackSourceID == sourceID && !meta.second.isFallback)
        {
            sourceID = meta.second.primarySourceID;
        }
    }

    iLayerInfo.sourceID = sourceID;
    SetLayerUniformInfo(iLayerInfo, req.layer_info());
    controller->CreateLayer(&iLayerInfo);

    SOURCE_INFO sourceInfo{};
    controller->GetSourceInfo(sourceID, &sourceInfo);
    sourceInfo.layerIDs.push_back(iLayerInfo.id);
    controller->SetSourceInfo(sourceID, &sourceInfo);

    if (sourceInfo.type == VISUAL_IMAGE)
    {
        IMAGE_SOURCE image = std::get<IMAGE_SOURCE>(sourceInfo.source);
        controller->GetMediaFileInfo(image.materialDesc.path, &image.materialDesc);
        iLayerInfo.transform.size = image.materialDesc.size;
    }
    else if (sourceInfo.type == VISUAL_FAV)
    {
        FAV_SOURCE fav = std::get<FAV_SOURCE>(sourceInfo.source);
        controller->GetMediaFileInfo(fav.materialDesc.path, &fav.materialDesc);
        iLayerInfo.transform.size = fav.materialDesc.size;
    }
    controller->SetLayerInfo(iLayerInfo.id, iLayerInfo);

    std::string layer_id = "";
    Util::NumToString(iLayerInfo.id, &layer_id);
    rsp.set_layer_id(layer_id);
    return true;
}

bool LSLayer::DestoryLayer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    for (int i = 0; i < req.layer_ids_size(); ++i)
    {
        UINT64 layerID = 0;
        Util::StringToNum(req.layer_ids(i), &layerID);
        if (!controller->FindLayerByID(layerID))
        {
            LOG(ERROR) << "[Layer::DestoryLayer] layer not exist, layerID: " << layerID;
            continue;
        }

        LAYER_INFO layerInfo{};
        controller->GetLayerInfo(layerID, &layerInfo);

        SOURCE_INFO sourceInfo{};
        controller->GetSourceInfo(layerInfo.sourceID, &sourceInfo);
        const auto& iter = std::find(sourceInfo.layerIDs.begin(), sourceInfo.layerIDs.end(), layerID);
        if (iter != sourceInfo.layerIDs.end())
        {
            sourceInfo.layerIDs.erase(iter);
            controller->SetSourceInfo(sourceInfo.id, &sourceInfo);
        }

        controller->DeleteLayer(layerID);
    }

    return true;
}

bool LSLayer::CreateSceneLayer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    // TODO: @xuwanhui

    return true;
}

bool LSLayer::SetLayerInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::SetLayerInfo] layer not exist, layerID: " << layerID;
        return false;
    }

    LAYER_INFO iLayerInfo{};
    controller->GetLayerInfo(layerID, &iLayerInfo);

    UINT64 cmd = LAYER_CONTROL_NONE;
    SetLayerUniformInfo(iLayerInfo, req.layer_info(), &cmd);
    controller->ControlLayer(layerID, iLayerInfo, static_cast<LAYER_CONTROL_CMD>(cmd));
    return true;
}

bool LSLayer::GetLayerInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::GetLayerInfo] layer not exist, layerID: " << layerID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(layerID, &oLayerInfo);

    CANVAS_INFO canvasInfo{};
    controller->GetCanvasInfo(oLayerInfo.canvasID, &canvasInfo);

    PREVIEW_INFO previewInfo;
    controller->GetPreviewInfo(canvasInfo.previewID, &previewInfo);

    ls_layer::LayerInfo out_layer_info{};
    GetLayerUniformInfo(previewInfo, oLayerInfo, out_layer_info);
    rsp.mutable_layer_info()->CopyFrom(out_layer_info);
    return true;
}

bool LSLayer::GetLayerRefSourceID::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::GetLayerRefSourceID] layer not exist, layerID: " << layerID;
        return false;
    }

    LAYER_INFO oLayerInfo{};
    controller->GetLayerInfo(layerID, &oLayerInfo);

    if (!controller->FindSourceByID(oLayerInfo.sourceID))
    {
        LOG(ERROR) << "[Layer::GetLayerRefSourceID] source not exist, layerID: " << layerID << ", sourceID: " << oLayerInfo.sourceID;
        return false;
    }

    std::string source_id = "";
    Util::NumToString(oLayerInfo.sourceID, &source_id);
    rsp.set_source_id(source_id);
    return true;
}

bool LSLayer::SelectLayer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::SelectLayer] layer not exist, layerID: " << layerID;
        return false;
    }
    
    controller->SelectLayer(layerID);
    return true;
}

bool LSLayer::UnSelectLayer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->SelectLayer(0);
    return true;
}

bool LSLayer::MoveLayerOrder::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::MoveLayerOrder] layer not exist, layerID: " << layerID;
        return false;
    }

    LAYER_INFO iLayerInfo{};
    controller->GetLayerInfo(layerID, &iLayerInfo);
    controller->MoveLayerOrder(iLayerInfo.canvasID, layerID, (MOVE_ORDER)req.move_order());
    return true;
}

bool LSLayer::ClipLayer::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::ClipLayer] layer not exist, layerID: " << layerID;
        return false;
    }

    LAYER_INFO layerInfo{};
    controller->GetLayerInfo(layerID, &layerInfo);
    controller->ControlLayer(layerID, layerInfo, LAYER_CONTROL_SET_CLIP_MASK);
    return true;
}

bool LSLayer::GetLayerSnapshot::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::GetLayerSnapshot] visual not exist, layerID: " << layerID;
        return false;
    }

    controller->GetLayerSnapshot(layerID, req.path());
    return true;
}

bool LSLayer::GetLayerSnapshot2::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::GetLayerSnapshot2] visual not exist, layerID: " << layerID;
        return false;
    }

    controller->GetLayerSnapshot2(layerID, req.path());
    return true;
}

bool LSLayer::UpdateLayerSource::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::UpdateLayerSource] layer not exist, layerID: " << layerID;
        return false;
    }

    UINT64 sourceID = 0;
    Util::StringToNum(req.target_source_id(), &sourceID);
    if (!controller->FindSourceByID(sourceID))
    {
        LOG(ERROR) << "[Layer::UpdateLayerSource] source not exist, sourceID: " << sourceID;
        return false;
    }

    controller->UpdateLayerSource(layerID, sourceID);
    return true;
}

bool LSLayer::AddFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::AddFilter] layer not exist, layerID: " << layerID;
        return false;
    }

    for (int i = 0; i < req.filter_ids_size(); ++i)
    {
        UINT64 filterID = 0;
        Util::StringToNum(req.filter_ids(i), &filterID);
        if (!controller->FindFilterByID(filterID))
        {
            LOG(ERROR) << "[Layer::AddFilter] filter not exist, filterID: " << filterID;
            continue;
        }

        controller->BindFilter(layerID, filterID);
    }
    return true;
}

bool LSLayer::RemoveFilter::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::RemoveFilter] visual not exist, layerID: " << layerID;
        return true;
    }

    for (int i = 0; i < req.filter_ids_size(); ++i)
    {
        UINT64 filterID = 0;
        Util::StringToNum(req.filter_ids(i), &filterID);
        if (!controller->FindFilterByID(filterID))
        {
            LOG(ERROR) << "[Layer::RemoveFilter] filter not exist, filterID: " << filterID;
            continue;
        }

        controller->UnBindFilter(layerID, filterID);
    }
    return true;
}

bool LSLayer::ResetFilterOrder::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 layerID = 0;
    Util::StringToNum(req.layer_id(), &layerID);
    if (!controller->FindLayerByID(layerID))
    {
        LOG(ERROR) << "[Layer::ResetFilterOrder] layer not exist, layerID: " << layerID;
        return false;
    }

    std::vector<std::string> filterIDs{};
    for (int i = 0; i < req.filter_ids_size(); ++i)
    {
        UINT64 filterID = 0;
        Util::StringToNum(req.filter_ids(i), &filterID);
        if (!controller->FindFilterByID(filterID))
        {
            LOG(ERROR) << "[Layer::ResetFilterOrder] filter not exist, filterID: " << filterID;
            continue;
        }
        filterIDs.push_back(req.filter_ids(i));
    }
    controller->ResetFilterOrder(layerID, filterIDs);
    return true;
}

void LSLayer::SetLayerUniformInfo(LAYER_INFO& layerInfo, const ls_layer::LayerInfo& layer_info, UINT64* cmd)
{
    if (layer_info.has_visible())
    {
        layerInfo.show = layer_info.visible();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_SHOW;
    }
    if (layer_info.has_locked())
    {
        layerInfo.locked = layer_info.locked();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_LOCK;
    }
    if (layer_info.has_layer_size())
    {
        layerInfo.transform.size.Width = layer_info.layer_size().x();
        layerInfo.transform.size.Height = layer_info.layer_size().y();
    }
    if (layer_info.has_canvas_size())
    {
        layerInfo.targetSize.Width = layer_info.canvas_size().x();
        layerInfo.targetSize.Height = layer_info.canvas_size().y();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_TARGET_SIZE;
    }
    if (layer_info.has_ref_layout())
    {
        layerInfo.refLayout.x = layer_info.ref_layout().x();
        layerInfo.refLayout.y = layer_info.ref_layout().y();
        layerInfo.refLayout.z = layer_info.ref_layout().z();
        layerInfo.refLayout.w = layer_info.ref_layout().w();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_REF_LAYOUT;
    }
    if (layer_info.has_layout_style())
    {
        layerInfo.layout = (LAYER_LAYOUT)layer_info.layout_style();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_LAYOUT;
    }
    if (layer_info.has_fixed_edge())
    {
        layerInfo.fixedEdge = (LAYER_FIXED_EDGE)layer_info.fixed_edge();
    }
    if (layer_info.has_move_range())
    {
        CLIPF moveRange;
        moveRange.x = layer_info.move_range().x();
        moveRange.y = layer_info.move_range().y();
        moveRange.z = layer_info.move_range().z();
        moveRange.w = layer_info.move_range().w();
        layerInfo.moveRange = moveRange;
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_MOVE_RANGE;
    }

    if (layer_info.has_transform())
    {
        TRANSFORM&         sourceTrans = layerInfo.transform;
        ls_base::Transform visualTrans = layer_info.transform();
        if (visualTrans.has_flip_h())
        {
            sourceTrans.hFlip = visualTrans.flip_h();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_FLIPH;
        }
        if (visualTrans.has_flip_v())
        {
            sourceTrans.vFlip = visualTrans.flip_v();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_FLIPV;
        }
        if (visualTrans.has_angle())
        {
            sourceTrans.angle = visualTrans.angle();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_ROTATE;
        }
        if (visualTrans.has_scale())
        {
            sourceTrans.scale.X = visualTrans.scale().x();
            sourceTrans.scale.Y = visualTrans.scale().y();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_SCALE;
        }
        if (visualTrans.has_min_scale())
        {
            sourceTrans.minScale.X = visualTrans.min_scale().x();
            sourceTrans.minScale.Y = visualTrans.min_scale().y();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_MIN_SCALE;
        }
        if (visualTrans.has_translate())
        {
            sourceTrans.translate.X = visualTrans.translate().x();
            sourceTrans.translate.Y = visualTrans.translate().y();

            if (cmd)
                *cmd |= LAYER_CONTROL_SET_TRANSLATE;
        }
        if (visualTrans.has_clip())
        {
            sourceTrans.clipRange.x = visualTrans.clip().x();
            sourceTrans.clipRange.y = visualTrans.clip().y();
            sourceTrans.clipRange.z = visualTrans.clip().z();
            sourceTrans.clipRange.w = visualTrans.clip().w();
            if (cmd)
                *cmd |= LAYER_CONTROL_SET_CLIP;
        }
    }
    if (layer_info.has_prepare_clip())
    {
        layerInfo.prepareClip.x = layer_info.prepare_clip().x();
        layerInfo.prepareClip.y = layer_info.prepare_clip().y();
        layerInfo.prepareClip.z = layer_info.prepare_clip().z();
        layerInfo.prepareClip.w = layer_info.prepare_clip().w();
        if (cmd)
            *cmd |= LAYER_CONTROL_SET_PREPARE_CLIP;
    }

    if (!layer_info.visual_flags().empty())
    {
        for (int i = 0; i < layer_info.visual_flags_size(); ++i)
        {
            VISUAL_FLAG flag = (VISUAL_FLAG)layer_info.visual_flags(i);
            auto        it = std::find(layerInfo.visualFlags.begin(), layerInfo.visualFlags.end(), flag);
            if (it == layerInfo.visualFlags.end())
            {
                layerInfo.visualFlags.push_back(flag);
            }
        }

        if (cmd && !layerInfo.visualFlags.empty())
            *cmd |= LAYER_CONTROL_SET_VISUAL_FLAGS;
    }

    if (layer_info.has_test_effect_filter())
    {
        layerInfo.testEffectFilter = layer_info.test_effect_filter();
    }
}

void LSLayer::GetLayerUniformInfo(const PREVIEW_INFO& previewInfo, const LAYER_INFO& layerInfo, ls_layer::LayerInfo& layer_info)
{
    ls_base::SizeF target_size{};
    target_size.set_x(previewInfo.layoutRect.Width);
    target_size.set_y(previewInfo.layoutRect.Height);
    layer_info.mutable_canvas_size()->CopyFrom(target_size);

    layer_info.set_layout_style((ls_basicenum::VISUAL_LAYOUT)layerInfo.layout);
    layer_info.set_fixed_edge((ls_layer::LAYER_FIXED_EDGE)layerInfo.fixedEdge);
    layer_info.set_visible(layerInfo.show);
    layer_info.set_locked(layerInfo.locked);

    for (const VISUAL_FLAG& flag : layerInfo.visualFlags)
    {
        layer_info.add_visual_flags((ls_layer::LAYER_FLAG)flag);
    }
    ls_base::ClipF move_range{};
    move_range.set_x(layerInfo.moveRange.x);
    move_range.set_y(layerInfo.moveRange.y);
    move_range.set_z(layerInfo.moveRange.z);
    move_range.set_w(layerInfo.moveRange.w);
    layer_info.mutable_move_range()->CopyFrom(move_range);

    ls_base::SizeF size{};
    size.set_x(layerInfo.transform.size.Width);
    size.set_y(layerInfo.transform.size.Height);
    layer_info.mutable_layer_size()->CopyFrom(size);

    ls_base::ClipF prepare_clip{};
    prepare_clip.set_x(layerInfo.prepareClip.x);
    prepare_clip.set_y(layerInfo.prepareClip.y);
    prepare_clip.set_z(layerInfo.prepareClip.z);
    prepare_clip.set_w(layerInfo.prepareClip.w);
    layer_info.mutable_prepare_clip()->CopyFrom(prepare_clip);

    ls_base::Transform transform{};
    TRANSFORM          sourceTrans = layerInfo.transform;
    transform.set_flip_h(sourceTrans.hFlip);
    transform.set_flip_v(sourceTrans.vFlip);
    transform.set_angle(sourceTrans.angle);

    ls_base::ScaleF scale{};
    scale.set_x(sourceTrans.scale.X);
    scale.set_y(sourceTrans.scale.Y);
    transform.mutable_scale()->CopyFrom(scale);

    ls_base::ScaleF min_scale{};
    min_scale.set_x(sourceTrans.minScale.X);
    min_scale.set_y(sourceTrans.minScale.Y);
    transform.mutable_min_scale()->CopyFrom(min_scale);

    ls_base::TranslateF translate{};
    translate.set_x(sourceTrans.translate.X);
    translate.set_y(sourceTrans.translate.Y);
    transform.mutable_translate()->CopyFrom(translate);

    ls_base::ClipF clip{};
    clip.set_x(sourceTrans.clipRange.x);
    clip.set_y(sourceTrans.clipRange.y);
    clip.set_z(sourceTrans.clipRange.z);
    clip.set_w(sourceTrans.clipRange.w);
    transform.mutable_clip()->CopyFrom(clip);

    ls_base::SizeF trans_size{};
    trans_size.set_x(sourceTrans.size.Width);
    trans_size.set_y(sourceTrans.size.Height);
    transform.mutable_size()->CopyFrom(trans_size);
    layer_info.mutable_transform()->CopyFrom(transform);
}
} // namespace MediaSDK