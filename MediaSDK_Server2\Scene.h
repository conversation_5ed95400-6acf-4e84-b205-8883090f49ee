﻿#pragma once

#include "Canvas.h"
#include <map>

enum SWITCH_SCENE_TYPE
{
	SWITCH_SCENE_NORMAL = 0,
	SWITCH_SCENE_PRELOAD = 1,
	SWITCH_SCENE_RELOAD = 2,
	SWITCH_SCENE_REPRELOAD = 3,
};

class Scene
{
public:
	Scene();
	virtual ~Scene();
	virtual void SetSceneID(UINT64 id);
	virtual void SetSceneInfo(const SCENE_INFO* sceneInfo);
	virtual void SetModeID(UINT32 id);

	virtual void InitializeCanvas() {}
	virtual void CreateCanvas(UINT32 oldCanvasIdx, Canvas* canvas, UINT64 canvasID);
	virtual bool DestroyCanvas(Canvas* pCanvas);
    virtual bool RemoveCanvas(Canvas* pCanvas);
	virtual Canvas* GetCanvas(UINT32 oldCanvasIdx);

	virtual void ReBindCanvas(UINT64 oldCanvasID, UINT64 newCanvasID);

	virtual void Select(bool in, SWITCH_SCENE_TYPE type);
	virtual void GetSceneInfo(SCENE_INFO_EX* info, bool child = false);

protected:
	SCENE_INFO m_sceneInfo;
	std::map<UINT32, Canvas*> m_canvas;
};

class SceneLandscape :public Scene
{
public:
	SceneLandscape() { m_sceneInfo.mode = LIVE_MODE_LANDSCAPE; }
	~SceneLandscape(){}
	virtual void InitializeCanvas();
};

class ScenePortrait :public Scene
{
public:
	ScenePortrait() { m_sceneInfo.mode = LIVE_MODE_PORTRAIT; }
	~ScenePortrait() {}
	virtual void InitializeCanvas();
};

class SceneDBCanvas :public Scene
{
public:
	SceneDBCanvas() { m_sceneInfo.mode = LIVE_MODE_DBCANVAS; }
	~SceneDBCanvas() {}
	virtual void InitializeCanvas();
};
