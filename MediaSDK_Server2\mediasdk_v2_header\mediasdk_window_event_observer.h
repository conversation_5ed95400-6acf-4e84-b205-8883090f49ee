#pragma once

#include "mediasdk_defines.h"

namespace mediasdk {

class MediaSDKWindowEventObserver {
 public:
  virtual void OnRButtonDown(uint32_t preview_id) = 0;

  virtual void OnRButtonUp(uint32_t preview_id) = 0;

  virtual void OnLButtonDblClk(uint32_t preview_id) = 0;

  virtual void OnLButtonDown(uint32_t preview_id) = 0;

  virtual void OnLButtonUp(uint32_t preview_id) = 0;

  virtual void OnMouseLeave(uint32_t preview_id) = 0;

  virtual void OnMouseHover(uint32_t preview_id) = 0;

  virtual void OnLButtonDownIgnoreTrackState(uint32_t preview_id) = 0;
};

}  // namespace mediasdk
