#include "ColorCorrectionFilter.h"
#include <d3dcompiler.h>

namespace v3
{

using namespace Microsoft::WRL;

static DirectX::XMFLOAT3 CalcClearColor(uint32_t bk_color)
{
    int r = (bk_color >> 16) & 0xFF;
    int g = (bk_color >> 8) & 0xFF;
    int b = bk_color & 0xFF;
    return {r * 1.0f / 255, g * 1.0f / 255, b * 1.0f / 255};
}

static float ColorNonLinearToLinear(float color)
{
    return (color <= 0.04045f) ? (color / 12.92f)
                               : powf((color + 0.055f) / 1.055f, 2.4f);
}

static DirectX::XMFLOAT3 CalcLinearColor(DirectX::XMFLOAT3 color)
{
    float r = ColorNonLinearToLinear(color.x);
    float g = ColorNonLinearToLinear(color.y);
    float b = ColorNonLinearToLinear(color.z);
    return {r, g, b};
}

static DirectX::XMMATRIX BuildBrightnessAndContrastMatrix(
    const ColorCorrectionFilterParams& params)
{
    // clang-format off
	// color = (color - (0.5f * (1.0f-brightness))) * contrast + 0.5f * (1.0f+brightness);
	DirectX::XMMATRIX brightness_contrast_mat{};
	float b = params.brightness;
	float c = tan((45 + 44 * params.contrast) / 180 * DirectX::XM_PI);
	float z = 0.5f * (1.0f + b) + c * (b - 0.5f);
	brightness_contrast_mat = { c,    0.0f, 0.0f, 0.0f,
								  0.0f, c,    0.0f, 0.0f,
								  0.0f, 0.0f, c,    0.0f,
								  z,    z,    z,    1.0f };
    // clang-format on
    return brightness_contrast_mat;
}

static DirectX::XMMATRIX BuildHueShiftMatrix(const ColorCorrectionFilterParams& params)
{
    // clang-format off
	DirectX::XMMATRIX hue_mat{};
	// ref: https://stackoverflow.com/questions/8507885/shift-hue-of-an-rgb-color
	float cos_a = cos(DirectX::XMConvertToRadians(params.hue_shift * 180.0f));
	float sin_a = sin(DirectX::XMConvertToRadians(params.hue_shift * 180.0f));
	float mat_00 = cos_a + (1.0f - cos_a) / 3.0f;
	float mat_01 = 1.0f / 3.0f * (1.0f - cos_a) - sqrt(1.0f / 3.0f) * sin_a;
	float mat_02 = 1.0f / 3.0f * (1.0f - cos_a) + sqrt(1.0f / 3.0f) * sin_a;
	float mat_10 = 1.0f / 3.0f * (1.0f - cos_a) + sqrt(1.0f / 3.0f) * sin_a;
	float mat_11 = cos_a + 1.0f / 3.0f * (1.0f - cos_a);
	float mat_12 = 1.0f / 3.0f * (1.0f - cos_a) - sqrt(1.0f / 3.0f) * sin_a;
	float mat_20 = 1.0f / 3.0f * (1.0f - cos_a) - sqrt(1.0f / 3.0f) * sin_a;
	float mat_21 = 1.0f / 3.0f * (1.0f - cos_a) + sqrt(1.0f / 3.0f) * sin_a;
	float mat_22 = cos_a + 1.0f / 3.0f * (1.0f - cos_a);
	hue_mat = { mat_00, mat_01, mat_02, 0.0f,
				  mat_10, mat_11, mat_12, 0.0f,
				  mat_20, mat_21, mat_22, 0.0f,
				  0.0f,   0.0f,   0.0f,   1.0f };
    // clang-format on
    return hue_mat;
}

DirectX::XMMATRIX BuildSaturationMatrix(const ColorCorrectionFilterParams& params)
{
    // clang-format off
	DirectX::XMMATRIX sat_mat{};
	// ref: https://www.graficaobscura.com/matrix/index.html
	const float rwgt = 0.3086f;
	const float gwgt = 0.6094f;
	const float bwgt = 0.0820f;
	const float s = params.saturation < 0.0f ?
		params.saturation + 1.0f : params.saturation * 5.0f + 1.0f;
	const float a = (1.0f - s) * rwgt + s;
	const float b = (1.0f - s) * rwgt;
	const float c = (1.0f - s) * rwgt;
	const float d = (1.0f - s) * gwgt;
	const float e = (1.0f - s) * gwgt + s;
	const float f = (1.0f - s) * gwgt;
	const float g = (1.0f - s) * bwgt;
	const float h = (1.0f - s) * bwgt;
	const float i = (1.0f - s) * bwgt + s;

	sat_mat = {
		  a,    b,    c,    0.0f,
		  d,    e,    f,    0.0f,
		  g,    h,    i,    0.0f,
		  0.0f, 0.0f, 0.0f, 1.0f,
	};

    // clang-format on
    return sat_mat;
}

DirectX::XMMATRIX BuildCorrectionColorMatrix(const ColorCorrectionFilterParams& params)
{
    DirectX::XMMATRIX color_mat{};

    auto add_color = CalcClearColor(params.add_color);
    auto linear_add_color = CalcLinearColor(add_color);

    auto mul_color = CalcClearColor(params.mul_color);
    auto linear_mul_color = CalcLinearColor(mul_color);

    const float a = linear_mul_color.x;
    const float b = linear_mul_color.y;
    const float c = linear_mul_color.z;
    const float d = linear_add_color.x;
    const float e = linear_add_color.y;
    const float f = linear_add_color.z;

    // clang-format off
	color_mat = {
		a,     0.0f,  0.0f,  0.0f,
		0.0f,  b,     0.0f,  0.0f,
		0.0f,  0.0f,  c,     0.0f,
		d,     e,     f,     1.0f,
	};
    // clang-format on
    return color_mat;
}

DirectX::XMMATRIX BuildColorCorrectionMatrix(const ColorCorrectionFilterParams& params)
{
    DirectX::XMMATRIX brightness_contrast_mat =
        BuildBrightnessAndContrastMatrix(params);
    DirectX::XMMATRIX hue_mat = BuildHueShiftMatrix(params);
    DirectX::XMMATRIX sat_mat = BuildSaturationMatrix(params);
    DirectX::XMMATRIX color_mat = BuildCorrectionColorMatrix(params);
    DirectX::XMMATRIX final_mat =
        brightness_contrast_mat * sat_mat * hue_mat * color_mat;

    return final_mat;
}

static float GammaCorrection(float input_gamma)
{
    return (input_gamma < 0.0f) ? (-input_gamma + 1.0f)
                                : (1.0f / (input_gamma + 1.0f));
}

static bool BuildColorCorrectionFilterShader(ID3D11Device* dev, ID3D11PixelShader** ps)
{
    const char pixel_shader_code[] = R"(

Texture2D<float4> Texture : register(t0);
sampler TextureSampler : register(s0);
cbuffer PSBuffer : register(b0)
{
	matrix color_matrix;
	float gamma;
	float opacity;
};

float4 SpritePixelShader(float4 color    : COLOR0,
    float2 texCoord : TEXCOORD0) : SV_Target0
{
	float4 tmpColor = Texture.Sample(TextureSampler, texCoord) * color;
	tmpColor.rgb = pow(tmpColor.rgb, gamma);
	tmpColor = mul(color_matrix, tmpColor);
    tmpColor.a *= opacity;
	return tmpColor;
}
)";

    bool success = false;

    do
    {
        ComPtr<ID3DBlob> blob;
        ComPtr<ID3DBlob> errorBlob;
        HRESULT          compileHR = D3DCompile(
            pixel_shader_code,
            strlen(pixel_shader_code),
            nullptr,
            nullptr,
            nullptr,
            "SpritePixelShader",
            "ps_4_0",
            D3D10_SHADER_OPTIMIZATION_LEVEL1,
            0,
            blob.GetAddressOf(),
            errorBlob.GetAddressOf());

        HRESULT hr2 = dev->CreatePixelShader(blob->GetBufferPointer(), blob->GetBufferSize(),
                                             nullptr, ps);

        success = true;

    } while (0);

    return success;
}

ColorCorrectionFilter::ColorCorrectionFilter()
{
    set_custom_shader_callback_ = [this]() {
        ID3D11Buffer* cbarry[] = {ps_cb_->GetBuffer()};
        ctx_->PSSetConstantBuffers(0, 1, cbarry);
        ctx_->PSSetShader(pixel_shader_.Get(), nullptr, 0);
    };
}

ColorCorrectionFilter::~ColorCorrectionFilter()
{
    Uninit();
}

bool ColorCorrectionFilter::Init(ID3D11Device* dev, ID3D11DeviceContext* ctx)
{
    std::unique_lock<std::mutex> lock(mutex_);

    UninitNolock();

    bool success = false;

    try
    {
        do
        {
            if (!dev || !ctx)
                break;

            dev_ = dev;
            ctx_ = ctx;

            ps_cb_ = std::make_unique<DirectX::ConstantBuffer<PSParam>>(dev);
            render_ = std::make_unique<DirectX::SpriteBatch>(ctx);

            bool ok = BuildColorCorrectionFilterShader(dev_, pixel_shader_.GetAddressOf());
            if (!ok)
                break;

            success = true;

        } while (0);
    }
    catch (...)
    {
        success = false;
    }

    ready_ = success;

    if (!success)
    {
        UninitNolock();
    }

    return success;
}

void ColorCorrectionFilter::Uninit()
{
    std::unique_lock<std::mutex> lock(mutex_);
    UninitNolock();
}

void ColorCorrectionFilter::UninitNolock()
{
    ready_ = false;
    params_uploaded_ = false;
    render_.reset();
    ps_cb_.reset();
    pixel_shader_.Reset();
    dev_ = nullptr;
    ctx_ = nullptr;
}

bool ColorCorrectionFilter::IsReady()
{
    return ready_;
}

bool ColorCorrectionFilter::Render(ID3D11ShaderResourceView* src_srv)
{
    std::unique_lock<std::mutex> lock(mutex_);

    if (!ready_ || !src_srv)
        return false;

    try
    {
        do
        {
            UpdateGPUParams();

            if (!params_uploaded_)
                break;

            render_->Begin(DirectX::SpriteSortMode_Immediate,
                           nullptr, nullptr, nullptr, nullptr, set_custom_shader_callback_);
            render_->Draw(src_srv, DirectX::FXMVECTOR());
            render_->End();

        } while (0);
    }
    catch (...)
    {
        return false;
    }
    return true;
}

void ColorCorrectionFilter::UpdateGPUParams()
{
    try
    {
        if (need_update_params_ || !params_uploaded_)
        {
            PSParam ps_param;
            ps_param.color_matrix = BuildColorCorrectionMatrix(params_);
            ps_param.gamma = GammaCorrection(params_.gamma);
            ps_param.opacity = params_.opacity;
            ps_cb_->SetData(ctx_, ps_param);

            need_update_params_ = false;
            params_uploaded_ = true;
        }
    }
    catch (...)
    {
    }
}

bool ColorCorrectionFilter::UpdateParams(const ColorCorrectionFilterParams& params, bool force)
{
    std::unique_lock<std::mutex> lock(mutex_);

    if (!ready_)
        return false;

    bool need_update = force ? true : memcmp(&params, &params_, sizeof(params)) != 0;

    if (need_update)
    {
        memcpy(&params_, &params, sizeof(params));
        need_update_params_ = true;
    }

    return true;
}

} // namespace v3