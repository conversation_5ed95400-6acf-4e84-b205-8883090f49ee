﻿#include "AiSdkIPCMgr.h"
#include "IO/TinyThread.h"
#include "MediaMgr.h"
#include "ModeSceneMgr.h"
#include "stringutil.h"

using namespace TinyFramework;
CHAR reqMappingName[] = ("191EEA47-C864-4B54-B886-59613261483C-AiSdkIPCReq");
CHAR rspMappingName[] = ("191EEA47-C864-4B54-B886-59613261483C-AiSdkIPCRsp");

extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;
extern CIEF::ICIEF*                          g_cief;

#pragma pack(push)
#pragma pack(4)

struct RspIPCData
{
    int     success;
    int     width;
    int     height;
    int     row_pitch;
    int     format;
    int     buffSize;
    int     shared_mem_name_size;
    char    shared_mem_name[64];
    int64_t timestamp;
};

struct ReqIPCData
{
    char    visual_id[100];
    int     targetWidth;
    int     targetHeight;
    int     clip_x;
    int     clip_y;
    int     clip_z;
    int     clip_w;
    int64_t timestamp;
};

struct GetVisualFrameContext
{
    std::string visual_id;

    std::mutex                               mutex;
    bool                                     done = false;
    bool                                     op_success = false;
    VisualTextureRsp                         rsp;
    std::shared_ptr<std::condition_variable> cv;
};

#pragma pack(pop)

AiSdkIPCMgr::AiSdkIPCMgr()
{
    m_cv = std::make_shared<std::condition_variable>();
    task_mgr_.Init();
}

AiSdkIPCMgr::~AiSdkIPCMgr()
{
    task_mgr_.Uninit();
}

AiSdkIPCMgr* AiSdkIPCMgr::GetInstance()
{
    static AiSdkIPCMgr* mgr = new AiSdkIPCMgr();
    return mgr;
}

bool AiSdkIPCMgr::StartLoop()
{
    std::unique_lock<std::mutex> lock(api_mutex_);

    if (!m_stop)
        return true;

    m_stop = false;
    m_workThread.Submit(BindCallback(&AiSdkIPCMgr::Loop, this));

    return true;
}

void AiSdkIPCMgr::StopLoop()
{
    std::unique_lock<std::mutex> lock(api_mutex_);
    StopLoopNolock();
}

void AiSdkIPCMgr::StopLoopNolock()
{
    LOG(INFO) << "[AiSdkIPCMgr::StopLoop] Begin";
    m_stop = true;
    m_cv->notify_all();
    m_requestBuffer.Close();
    m_responseBuffer.Close();
    m_workThread.Close();
    LOG(INFO) << "[AiSdkIPCMgr::StopLoop] End";
}

void AiSdkIPCMgr::Loop()
{
    LOG(INFO) << "[AiSdkIPCMgr::Loop] begin";

    bool request_buffer_ready = false;
    bool response_buffer_ready = false;
    bool connect_ready = false;

    LOG(INFO) << "[AiSdkIPCMgr::Loop] Connect begin";

    while (!m_stop)
    {
        if (!request_buffer_ready)
        {
            request_buffer_ready = m_requestBuffer.Create(reqMappingName, sizeof(ReqIPCData), 1);
            if (!request_buffer_ready)
            {
                size_t elem_size = 0;
                bool   ok = m_requestBuffer.Open(reqMappingName, &elem_size, NULL);
                request_buffer_ready = ok && elem_size == sizeof(ReqIPCData);
            }
        }

        if (!response_buffer_ready)
        {
            response_buffer_ready = m_responseBuffer.Create(rspMappingName, sizeof(RspIPCData), 1);
            if (!response_buffer_ready)
            {
                size_t elem_size = 0;
                bool   ok = m_responseBuffer.Open(rspMappingName, &elem_size, NULL);
                response_buffer_ready = ok && elem_size == sizeof(RspIPCData);
            }
        }

        if (request_buffer_ready && response_buffer_ready)
            break;

        {
            std::unique_lock<std::mutex> lock(m_mutex);
            m_cv->wait_for(lock, std::chrono::milliseconds(100));
        }
    }

    m_requestBuffer.Clear();
    m_responseBuffer.Clear();

    connect_ready = request_buffer_ready && response_buffer_ready;

    LOG(INFO) << StringPrintf("[AiSdkIPCMgr::Loop] Connect end, connect_ready: %d", (int)connect_ready);

    v3::SharedMemoryBuffer buffer;
    std::string            buffer_name;
    int64_t                buffer_count = 0;

    while (!m_stop && connect_ready)
    {
        ReqIPCData ipc_request;
        size_t     tmp_size = sizeof(ipc_request);
        bool       request_ok = m_requestBuffer.Pop(&ipc_request, &tmp_size, 1000);

        if (!request_ok || tmp_size != sizeof(ipc_request))
        {
            continue;
        }

        VisualTextureReq request = {std::string(ipc_request.visual_id), ipc_request.targetWidth, ipc_request.targetHeight,
                                    ipc_request.clip_x, ipc_request.clip_y, ipc_request.clip_z, ipc_request.clip_w};

        auto ctx = std::make_shared<GetVisualFrameContext>();
        ctx->op_success = false;
        ctx->done = false;
        ctx->cv = m_cv;
        ctx->visual_id = request.visualID;

        auto task = task_mgr_.CreateThreadTask([ctx, request]() {
            UINT64 layerID = 0;
            Util::StringToNum(request.visualID, &layerID);
            LAYER_INFO layerInfo{};
            ModeSceneMgr::GetInstance()->GetLayerInfoByID(layerID, &layerInfo);
            VisualTextureReq req = request;
            Util::NumToString(layerInfo.sourceID, &req.visualID);

            VisualTextureRsp rsp{};
            bool             op_success = g_sdkController && g_sdkController->GetVisualFrame(req, &rsp);

            {
                std::unique_lock<std::mutex> lock(ctx->mutex);
                if (!ctx->done)
                {
                    ctx->done = true;
                    ctx->op_success = op_success;
                    ctx->rsp = std::move(rsp);
                    ctx->cv->notify_all();
                }
            }
        });
        g_cief->GetThreadMgr()->AddTaskToBackThread(task, CIEF::TASKPRIORITY::TASKPRIORITY_NORMAL);

        {
            std::unique_lock<std::mutex> lock(m_mutex);
            m_cv->wait_for(lock, std::chrono::milliseconds(2000));
        }

        {
            std::unique_lock<std::mutex> lock(ctx->mutex);
            ctx->done = true;
        }

        if (m_stop)
            break;

        bool op_success = false;
        bool enough_data = false;
        bool buffer_ready = false;

        RspIPCData ipc_response{};
        ipc_response.success = 0;
        ipc_response.timestamp = ipc_request.timestamp;

        do
        {
            if (!ctx->op_success)
                break;

            op_success = true;

            ipc_response.width = ctx->rsp.width;
            ipc_response.height = ctx->rsp.height;
            ipc_response.row_pitch = ctx->rsp.rowPitch;
            ipc_response.format = (int)ctx->rsp.format;
            ipc_response.buffSize = ctx->rsp.rowPitch * ctx->rsp.height;

            if (ctx->rsp.data.size() < ipc_response.buffSize)
                break;

            enough_data = true;

            if (buffer.Size() < ipc_response.buffSize)
            {
                int64_t tp = std::chrono::high_resolution_clock::now().time_since_epoch().count();
                buffer_name = StringPrintf("buffer_%lld_%lld", buffer_count++, tp);
                buffer.Close();

                bool ok = buffer.Create(buffer_name, ipc_response.buffSize);
                if (!ok)
                    break;
            }

            memcpy(ipc_response.shared_mem_name, buffer_name.c_str(), buffer_name.size());
            ipc_response.shared_mem_name_size = buffer_name.size();

            memcpy(buffer.Data(), ctx->rsp.data.data(), ipc_response.buffSize);
            buffer_ready = true;

            ipc_response.success = 1;

        } while (0);

        m_responseBuffer.Push(&ipc_response, sizeof(ipc_response), true);

        if (!ipc_response.success)
        {
            LOG(INFO) << StringPrintf("[AiSdkIPCMgr::Loop] GetVisualFrame fail, visual_id:%s, op_success:%d, enough_data:%d, buffer:%d",
                                      request.visualID.c_str(), (int)op_success, (int)enough_data, (int)buffer_ready);
        }
    }

    LOG(INFO) << "[AiSdkIPCMgr::Loop] end";
}