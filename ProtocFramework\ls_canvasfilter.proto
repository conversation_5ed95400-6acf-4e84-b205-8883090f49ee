//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package ls_canvasfilter;
import "ls_base.proto";

enum TRANSITION_TYPE
{
    TRANSITION_TYPE_NONE = 0;
	TRANSITION_TYPE_SLIDE = 1;
    TRANSITION_TYPE_SWIPE = 2;
    TRANSITION_TYPE_CARTOON = 3;
    TRANSITION_TYPE_FADE = 4;
    TRANSITION_TYPE_FADE2COLOR = 5;
    TRANSITION_TYPE_LUMINANCE_WIDE = 6;
    TRANSITION_TYPE_MOVE = 7;
};

enum TRANSITION_DIRECTION
{
    TRANSITION_DIRECTION_LEFT = 0;
    TRANSITION_DIRECTION_RIGHT = 1;
    TRANSITION_DIRECTION_UP = 2;
    TRANSITION_DIRECTION_DOWN = 3;
};

enum TRANSITION_PROGRESS_FUNCTION_TYPE
{
    TRANSITION_PROGRESS_FUNCTION_LINEAR = 0;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_SINE = 1;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_SINE = 2;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_SINE = 3;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUAD = 4;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUAD = 5;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUAD = 6;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_CUBIC = 7;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CUBIC = 8;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CUBIC = 9;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUART = 10;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUART = 11;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUART = 12;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_QUINT = 13;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_QUINT = 14;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_QUINT = 15;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_EXPO = 16;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_EXPO = 17;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_EXPO = 18;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_CIRC = 19;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_CIRC = 20;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_CIRC = 21;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_BACK = 22;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_BACK = 23;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_BACK = 24;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_ELASTIC = 25;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_ELASTIC = 26;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_ELASTIC = 27;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_BOUNCE= 28;
    TRANSITION_PROGRESS_FUNCTION_EASE_OUT_BOUNCE = 29;
    TRANSITION_PROGRESS_FUNCTION_EASE_IN_OUT_BOUNCE = 30;
};

message SlideTransition
{
    TRANSITION_DIRECTION direction = 1;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE function = 2;
}

enum SWIPE_TYPE
{
    SWIPE_IN = 0;
    SWIPE_OUT = 1;
};

message SwipeTransition
{
    TRANSITION_DIRECTION direction = 1;
    SWIPE_TYPE swipe_type = 2;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE function = 3;
}

message CartoonTransition
{
    string file_path = 1;
    float middle_progress = 2;
}

message FadeTransition
{
    optional TRANSITION_PROGRESS_FUNCTION_TYPE function = 1;
}

message Fade2ColorTransition
{
    uint32 color = 1;
    float middle_progress = 2;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE function = 3;
}

message LuminanceWideTransition
{
    string file_path = 1;
    bool invert = 2;
    float softness = 3;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE function = 4;
}

message MatchedLayerID
{
    string source_layer_id = 1;
    string target_layer_id = 2;
}

enum TRANSITION_MOVE_TYPE
{
    TRANSITION_MOVE_NONE = 0;
    TRANSITION_MOVE_SLIDE = 1;
    TRANSITION_MOVE_SCALE = 2;
    TRANSITION_MOVE_FADE = 3;
};

message MoveTransitionInfo
{
    TRANSITION_MOVE_TYPE move_type = 1;
    ls_base.TranslateF move_in_from_position = 2;
    ls_base.TranslateF move_out_to_position = 3;
    optional TRANSITION_DIRECTION move_in_from_direction = 4;
    optional TRANSITION_DIRECTION move_out_to_direction = 5;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE move_in_function = 6;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE move_out_function = 7;
    optional TRANSITION_PROGRESS_FUNCTION_TYPE move_to_function = 8;
}

message MoveTransition
{
    float middle_progress = 1;
    repeated MatchedLayerID matched_layer_ids = 2;
    MoveTransitionInfo move_transition_info = 3;
}

message TransitionFilter 
{
    TRANSITION_TYPE transition_type = 1;
    optional uint64 duration_ms = 2;
    SlideTransition slide_transition = 3;
    SwipeTransition swipe_transition = 4;
    CartoonTransition cartoon_transition = 5;
    FadeTransition fade_transition = 6;
    Fade2ColorTransition fade2color_transition = 7;
    LuminanceWideTransition luminance_wide_transition = 8;
    MoveTransition move_transition = 9;
}

message ColorAdjustFilter 
{
    optional float brightness = 1; // [-100.0, 100.0]
    optional float saturation = 2; // [-100.0, 100.0]
    optional float gamma = 3;      // [-3.0, 3.0]
    optional float contrast = 4;   // [-100.0, 100.0]
    optional float hue_shift = 5;  // [-180.0, 180.0]
    optional float opacity = 6;    // [0.0, 100.0]
    optional uint32 add_color = 7; // [0, 0x00FFFFFF], as a 4-bytes argb format, each byte corresponding to a uint32 value out of 255. For example, 0x00FFFFFF equals (A,R,G,B)=(0.0,1.0,1.0,1.0)
    optional uint32 mul_color = 8; // [0, 0x00FFFFFF], as a 4-bytes argb format, each byte corresponding to a uint32 value out of 255. For example, 0x00FFFFFF equals (A,R,G,B)=(0.0,1.0,1.0,1.0)
}

enum CANVAS_FILTER_TYPE
{
    CANVAS_FILTER_NONE = 0;
    CANVAS_FILTER_TRANSITION = 1;
    CANVAS_FILTER_COLOR_ADJUST = 2;
}

message CanvasFilter
{
    TransitionFilter transition_filter = 1;
    ColorAdjustFilter color_adjust_filter = 2;
    optional bool enable = 3;
}

message Create {
    message Request {
        CANVAS_FILTER_TYPE filter_type = 1;
        CanvasFilter canvas_filter = 2;
    }

	message Response {
		string filter_id = 1;
	}
}

message Remove {
    message Request {
        string filter_id = 1;
    }
}

message SetCanvasFilter {
    message Request {
        string filter_id = 1;
        CanvasFilter canvas_filter = 2;
    }
}

message GetCanvasFilter {
    message Request {
        string filter_id = 1;
    }

    message Response {
        CanvasFilter canvas_filter = 1;
        CANVAS_FILTER_TYPE type = 2;
    }
}