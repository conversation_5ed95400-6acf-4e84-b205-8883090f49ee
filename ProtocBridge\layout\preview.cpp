#include "stdafx.h"
#include "preview.h"
#include "../visual/visual.h"

namespace LS
{
Preview::RequestList Preview::GetRequestHandlers() const
{
    RequestList list;
    list.push_back(std::make_unique<Preview::CreatePreview>());
    list.push_back(std::make_unique<Preview::DestoryPreview>());
    list.push_back(std::make_unique<Preview::SetPreviewInfo>());
    list.push_back(std::make_unique<Preview::EnablePreviewByVideoModel>());
    return list;
}

bool Preview::CreatePreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 id = 0;
    Util::StringToNum(req.id(), &id);
    UINT64 previewID = 0;
    if (controller->FindCanvasByID(id))
    {
        CANVAS_INFO canvasInfo{};
        controller->GetCanvasInfo(id, &canvasInfo);

        PREVIEW_INFO previewInfo;
        controller->GetPreviewInfo(canvasInfo.previewID, &previewInfo);
        previewID = canvasInfo.previewID;

        previewInfo.hwnd = (HWND)req.parent_hwnd();
        LOG(INFO) << "[CreatePreview] hwnd: " << previewInfo.hwnd;
        if (req.has_preview_attr())
        {
            if (req.preview_attr().has_transform() && req.preview_attr().transform().has_translate())
            {
                previewInfo.rect.X = req.preview_attr().transform().translate().x();
                previewInfo.rect.Y = req.preview_attr().transform().translate().y();
            }
            if (req.preview_attr().has_size())
            {
                previewInfo.rect.Width = req.preview_attr().size().x();
                previewInfo.rect.Height = req.preview_attr().size().y();
            }
        }

        previewInfo.curCanvasID = id;
        controller->PreviewCanvas(&previewInfo);
    }
    else if (controller->FindLayerByID(id))
    {
        LAYER_PREVIEW preview{};
        if (req.has_preview_attr())
        {
            Visual::SetPreviewAttr(preview, req.preview_attr());
        }
        PREVIEW_PARAMS params{};
        controller->StartLayerPreview(id, req.parent_hwnd(), &preview, &previewID, params);
    }
    else if (controller->FindSourceByID(id))
    {
        // TODO: @xuwanhui
    }

    std::string preview_id = "";
    Util::NumToString(previewID, &preview_id);
    rsp.set_preview_id(preview_id);
    return true;
}

bool Preview::DestoryPreview::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 id = 0;
    Util::StringToNum(req.id(), &id);
    UINT64 previewID = 0;
    Util::StringToNum(req.preview_id(), &previewID);
    if (controller->FindCanvasByID(id))
    {
        controller->DestroyPreview(previewID);
    }
    else if (controller->FindLayerByID(id))
    {
        controller->StopLayerPreview(id, previewID);
    }
    else if (controller->FindSourceByID(id))
    {
        // TODO: @xuwanhui
    }

    return true;
}

bool Preview::SetPreviewInfo::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    UINT64 id = 0;
    Util::StringToNum(req.id(), &id);
    UINT64 previewID = 0;
    Util::StringToNum(req.preview_id(), &previewID);

    if (controller->FindCanvasByID(id))
    {
        CANVAS_INFO canvasInfo{};
        controller->GetCanvasInfo(id, &canvasInfo);

        PREVIEW_INFO previewInfo;
        controller->GetPreviewInfo(previewID, &previewInfo);

        if (req.has_preview_attr())
        {
            if (req.preview_attr().has_transform() && req.preview_attr().transform().has_translate())
            {
                previewInfo.rect.X = req.preview_attr().transform().translate().x();
                previewInfo.rect.Y = req.preview_attr().transform().translate().y();
            }
            if (req.preview_attr().has_size())
            {
                previewInfo.rect.Width = req.preview_attr().size().x();
                previewInfo.rect.Height = req.preview_attr().size().y();
            }
        }

        if (id == previewInfo.curCanvasID)
        {
            controller->UpdatePreviewLayout(previewInfo);
        }
        else
        {
            previewInfo.curCanvasID = id;
            controller->PreviewCanvas(&previewInfo);
        }
    }
    else if (controller->FindLayerByID(id))
    {
        LAYER_PREVIEW preview{};
        controller->GetLayerPreview(id, previewID, &preview);
        if (req.has_preview_attr())
        {
            Visual::SetPreviewAttr(preview, req.preview_attr());
        }
        controller->SetLayerPreviewLayout(id, previewID, &preview);
    }
    else if (controller->FindSourceByID(id))
    {
        // TODO: @xuwanhui
    }

    return true;
}

bool Preview::EnablePreviewByVideoModel::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    controller->EnablePreviewByVideoModel(req.video_model_id(), req.enable());
    return true;
}

} // namespace MediaSDK