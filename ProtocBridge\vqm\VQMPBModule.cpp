﻿#include <fstream>
#include <sstream>
#include <map>

#include "VQMPBModule.h"
#include "PBBridge.h"
#include "../MediaSDK_Server2/MediaMgr.h"
#include "../MediaSDK_Server2/EnumConverter.h"

namespace LS
{

VQMPBModule::RequestList VQMPBModule::GetRequestHandlers() const
{
    LOG(INFO) << "[LSNATIVE] GetRequestHandlers";
    RequestList list;
    list.push_back(std::make_unique<VQMPBModule::Initialize>());
    list.push_back(std::make_unique<VQMPBModule::QueryManuallySelectedResult>());
    list.push_back(std::make_unique<VQMPBModule::QueryGoLiveRecommendedParams>());
    list.push_back(std::make_unique<VQMPBModule::QueryCameraRecommendedParams>());
    list.push_back(std::make_unique<VQMPBModule::QueryCameraBestParamsForTarget>());
    list.push_back(std::make_unique<VQMPBModule::ReconfigVideoOutput>());
    list.push_back(std::make_unique<VQMPBModule::SetPreprocessDefaultSize>());
    list.push_back(std::make_unique<VQMPBModule::RemovePreprocessDefaultSize>());
    list.push_back(std::make_unique<VQMPBModule::FallbackVideoEncoder>());
    list.push_back(std::make_unique<VQMPBModule::StartAdaptiveGearStrategyReport>());
    return list;
}

void VQMPBModule::ModuleCreate()
{
}

static void MapVideoQualityManagerGearResolution(const ls_videoqualitymanager::GearResolution in, VideoQualityManagerGearResolution* out)
{
    for (int i = 0; i < in.wide_screen_size(); ++i)
    {
        out->wideScreen.push_back(in.wide_screen(i));
    }
    for (int i = 0; i < in.standard_screen_size(); ++i)
    {
        out->standardScreen.push_back(in.standard_screen(i));
    }
}

static void MapVideoQualityManagerGear(const ls_videoqualitymanager::Gear& in, VideoQualityManagerGear* out)
{
    if (!out)
        return;

    out->id = in.id();
    out->name = in.name();
    MapVideoQualityManagerGearResolution(in.resolution(), &out->resolution);
    out->fps = in.fps();
}

static void MapVideoQualityManagerBwGearIdEntry(const ls_videoqualitymanager::BwGearIdEntry& in, VideoQualityManagerBwGearIdEntry* out)
{
    out->min = in.min();
    out->max = in.max();
    out->gear_id = in.gear_id();
}

static void MapVideoQualityManagerVideoBitrate(const ls_videoqualitymanager::VideoBitrate& in, VideoQualityManagerVideoBitrate* out)
{
    out->min = in.min();
    out->target = in.target();
    out->max = in.max();
}

static void MapVideoQualityManagerBitrate(const ls_videoqualitymanager::Bitrate& in, VideoQualityManagerBitrate* out)
{
    out->audio = in.audio();
    out->gear_id = in.gear_id();

    for (auto it : in.video_wide_screen())
    {
        VideoQualityManagerVideoBitrate r;
        MapVideoQualityManagerVideoBitrate(it.second, &r);
        out->video_wide_screen[it.first] = r;
    }

    for (auto it : in.video_standard_screen())
    {
        VideoQualityManagerVideoBitrate r;
        MapVideoQualityManagerVideoBitrate(it.second, &r);
        out->video_standard_screen[it.first] = r;
    }
}

static void MapVideoQualityManagerTopicConfig(const ls_videoqualitymanager::TopicConfig& in, VideoQualityManagerTopicConfig* out)
{
    for (int i = 0; i < in.bw_table_size(); ++i)
    {
        VideoQualityManagerBwGearIdEntry r;
        MapVideoQualityManagerBwGearIdEntry(in.bw_table(i), &r);
        out->bw_table.push_back(r);
    }

    for (auto it : in.device_level_table())
    {
        (out->device_level_table)[it.first] = it.second;
    }

    out->quality_perf_level_step = in.quality_perf_level_step();
    out->enable_camera_enc_linkage = in.enable_camera_enc_linkage();

    for (int i = 0; i < in.bitrate_table_size(); ++i)
    {
        VideoQualityManagerBitrate r;
        MapVideoQualityManagerBitrate(in.bitrate_table(i), &r);
        out->bitrate_table.push_back(r);
    }

    for (int i = 0; i < in.topic_id_list_size(); ++i)
    {
        out->topic_id_list.push_back(in.topic_id_list(i));
    }

    out->topic_type = in.topic_type();
}

static void MapVideoQualityManagerStrategyConfig(const ls_videoqualitymanager::StrategyConfig& in, VideoQualityManagerStrategyConfig* out)
{
    out->priority = in.priority();
    out->bitrate_table_index = in.bitrate_table_index();
    out->default_level = in.default_level();

    for (auto it : in.gear_shift_rule())
    {
        out->gear_shift_rule[it.first] = it.second;
    }

    out->gear_shift_type = in.gear_shift_type();
    out->name = in.name();
}

static void MapVideoQualityManagerBwReserveFactorEntry(const ls_videoqualitymanager::BwReserveFactorEntry& in, VideoQualityManagerBwReserveFactorEntry* out)
{
    out->min = in.min();
    out->max = in.max();
    out->reservation = in.reservation();
}

static void MapVideoQualityManagerDualCanvasConfig(const ls_videoqualitymanager::DualCanvasConfig& in, VideoQualityManagerDualCanvasConfig* out)
{
    for (int i = 0; i < in.bw_probe_reservation_size(); ++i)
    {
        VideoQualityManagerBwReserveFactorEntry r;
        MapVideoQualityManagerBwReserveFactorEntry(in.bw_probe_reservation(i), &r);
        out->bw_probe_reservation.push_back(r);
    }
    out->bitrate_alloc_ratio = in.bitrate_alloc_ratio();
}

static void MapVideoQualityManagerCameraSuggestedConfig(const ls_videoqualitymanager::CameraSuggestedConfig& in, VideoQualityManagerCameraSuggestedConfig* out)
{
    for (int i = 0; i < in.resolution_size(); ++i)
    {
        out->resolution.push_back(in.resolution(i));
    }
    for (int i = 0; i < in.fps_size(); ++i)
    {
        out->fps.push_back(in.fps(i));
    }
}

static void MapVideoQualityManagerCameraRecommendConfig(const ls_videoqualitymanager::CameraRecommendConfig& in, VideoQualityManagerCameraRecommendConfig* out)
{

    for (auto it : in.device_level_camera_recommend_config())
    {
        VideoQualityManagerCameraSuggestedConfig r;
        MapVideoQualityManagerCameraSuggestedConfig(it.second, &r);
        out->device_level_camera_recommend_config[it.first] = r;
    }

    out->raise_threshold_for_fps = in.raise_threshold_for_fps();
}

static void MapVideoQualityManagerRect(const ls_videoqualitymanager::Rect& in, VideoQualityManagerRect* out)
{
    out->width = in.width();
    out->height = in.height();
}

static void MapVideoQualityManagerCameraDefaultRecommend(const ls_videoqualitymanager::CameraDefaultRecommend& in, VideoQualityManagerCameraDefaultRecommend* out)
{
    out->fps = in.fps();
    MapVideoQualityManagerRect(in.resolution(), &out->resolution);
}

static void MapVideoQualityManagerRecommendStrategy(const ls_videoqualitymanager::RecommendStrategy& in, VideoQualityManagerRecommendStrategy* out)
{
    for (int i = 0; i < in.topic_config_size(); i++)
    {
        VideoQualityManagerTopicConfig r;
        MapVideoQualityManagerTopicConfig(in.topic_config(i), &r);
        out->topic_config.push_back(r);
    }
    for (int i = 0; i < in.gear_table_priority_size(); ++i)
    {
        VideoQualityManagerStrategyConfig r;
        MapVideoQualityManagerStrategyConfig(in.gear_table_priority(i), &r);
        out->gear_table_priority.push_back(r);
    }

    MapVideoQualityManagerDualCanvasConfig(in.dual_canvas_config(), &out->dual_canvas_config);

    MapVideoQualityManagerCameraRecommendConfig(in.camera_recommend_config(), &out->camera_recommend_config);

    out->default_bitrate_table_topic = in.default_bitrate_table_topic();

    if (in.has_camera_default_recommend())
    {
        VideoQualityManagerCameraDefaultRecommend r;
        MapVideoQualityManagerCameraDefaultRecommend(in.camera_default_recommend(), &r);
        out->camera_default_recommend = r;
    }
}

static void MapVideoQualityManagerGearResolution(const VideoQualityManagerGearResolution& in, ls_videoqualitymanager::GearResolution* out)
{
    for (auto val : in.wideScreen)
    {
        out->add_wide_screen(val);
    }
    for (auto val : in.standardScreen)
    {
        out->add_standard_screen(val);
    }
}

static void MapVideoQualityManagerGearOut(const VideoQualityManagerGearOut& in, ls_videoqualitymanager::GearOut* out)
{
    out->set_id(in.id);
    out->set_name(in.name);
    auto r = out->mutable_resolution();
    MapVideoQualityManagerGearResolution(in.resolution, r);
    out->set_fps(in.fps);
}

static void MapVideoQualityManagerVideoBitrate(const VideoQualityManagerVideoBitrate& in, ls_videoqualitymanager::VideoBitrate* out)
{
    out->set_min(in.min);
    out->set_target(in.target);
    out->set_max(in.max);
}

static void MapVideoQualityManagerBitrateOut(const VideoQualityManagerBitrateOut& in, ls_videoqualitymanager::BitrateOut* out)
{
    out->set_audio(in.audio);
    out->set_gear_id(in.gear_id);

    if (!in.video_wide_screen.empty())
    {
        auto m = out->mutable_video_wide_screen();
        for (auto it : in.video_wide_screen)
        {
            ls_videoqualitymanager::VideoBitrate r;
            MapVideoQualityManagerVideoBitrate(it.second, &r);
            (*m)[it.first] = r;
        }
    }

    if (!in.video_standard_screen.empty())
    {
        auto m = out->mutable_video_standard_screen();
        for (auto it : in.video_standard_screen)
        {
            ls_videoqualitymanager::VideoBitrate r;
            MapVideoQualityManagerVideoBitrate(it.second, &r);
            (*m)[it.first] = r;
        }
    }

    out->set_dual_canvas_video_bitrate_alloc_ratio(in.dual_canvas_video_bitrate_alloc_ratio);
}

static void MapVideoQualityManagerGoLiveParamsOut(const VideoQualityManagerGoLiveParamsOut& in, ls_videoqualitymanager::GoLiveParamsOut* out)
{
    MapVideoQualityManagerGearOut(in.gear, out->mutable_gear());
    MapVideoQualityManagerBitrateOut(in.bitrate, out->mutable_bitrate());
    out->set_decision_process_info(in.decision_process_info);
}

bool VQMPBModule::Initialize::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    VideoQualityManagerInitializeParam in_param;
    VideoQualityManagerGoLiveParamsOut out_param;

    // gear table
    for (const auto& gear_info : req.gear_table())
    {
        VideoQualityManagerGear r;
        MapVideoQualityManagerGear(gear_info, &r);
        in_param.gear_table.push_back(r);
    }

    // recommend strategy
    MapVideoQualityManagerRecommendStrategy(req.recommend_strategy(), &in_param.recommend_strategy);

    // current device level
    for (const auto& device_level : req.current_topic_device_level())
    {
        in_param.current_topic_device_level[device_level.first] = device_level.second;
    }

    in_param.use_old_recommend_way = req.use_old_recommend_way();

    bool success = mgr->VideoQualityManagerInitialize(in_param, &out_param);

    if (success)
    {
        MapVideoQualityManagerGoLiveParamsOut(out_param, rsp.mutable_default_go_live_params());
    }

    return success;
}

bool VQMPBModule::QueryManuallySelectedResult::doHandle(const In& req, Out& rsp)
{

    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    VideoQualityManagerQueryManuallySelectedResultRequest  in_param;
    VideoQualityManagerQueryManuallySelectedResultResponse out_param;

    in_param.gear_id = req.gear_id();
    if (req.has_topic_id())
    {
        in_param.topic_id = req.topic_id();
    }

    bool success = mgr->VideoQualityManagerQueryManuallySelectedResult(in_param, &out_param);

    if (success)
    {
        auto r = rsp.mutable_go_live_params();
        MapVideoQualityManagerGoLiveParamsOut(out_param.go_live_params, r);
    }

    return success;
}

static void MapVideoQualityManagerCameraParams(const ls_videoqualitymanager::CameraParams& in, VideoQualityManagerCameraParams* out)
{
    out->width = in.width();
    out->height = in.height();
    out->fps = in.fps();
    out->max_fps = in.max_fps();
    out->min_fps = in.min_fps();
    out->format = static_cast<VIDEO_PIXEL_FORMAT>(in.format());
}

static void MapVideoQualityManagerTopicGoLiveParams(const VideoQualityManagerTopicGoLiveParams& in, ls_videoqualitymanager::TopicGoLiveParams* out)
{
    MapVideoQualityManagerGoLiveParamsOut(in.performance_priority, out->mutable_performance_priority());
    MapVideoQualityManagerGoLiveParamsOut(in.quality_priority, out->mutable_quality_priority());
}

static void MapVideoQualityManagerRecommendedGoLiveParams(const VideoQualityManagerRecommendedGoLiveParams& in, ls_videoqualitymanager::RecommendedGoLiveParams* out)
{
    out->set_topic_type(in.topic_type);
    for (int i = 0; i < in.topic_id_list.size(); ++i)
    {
        out->add_topic_id_list(in.topic_id_list[i]);
    }

    MapVideoQualityManagerTopicGoLiveParams(in.topic_go_live_params, out->mutable_topic_go_live_params());
}

bool VQMPBModule::QueryGoLiveRecommendedParams::doHandle(const In& req, Out& rsp)
{

    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    VideoQualityManagerQueryGoLiveRecommendedParamsRequest  in_param;
    VideoQualityManagerQueryGoLiveRecommendedParamsResponse out_param;

    if (req.has_bw_probe_bps())
    {
        in_param.bw_probe_bps = req.bw_probe_bps();
    }

    for (auto it : req.current_topic_device_level())
    {
        in_param.current_topic_device_level[it.first] = it.second;
    }

    if (req.has_camera_max_params())
    {
        VideoQualityManagerCameraParams r;
        MapVideoQualityManagerCameraParams(req.camera_max_params(), &r);
        in_param.camera_max_params = r;
    }

    bool success = mgr->VideoQualityManagerQueryGoLiveRecommendedParams(in_param, &out_param);

    if (success)
    {
        if (!out_param.go_live_recommend_params.empty())
        {

            auto m = rsp.mutable_go_live_recommend_params();

            for (const auto& info : out_param.go_live_recommend_params)
            {
                auto r = rsp.add_go_live_recommend_params();
                MapVideoQualityManagerRecommendedGoLiveParams(info, r);
            }
        }
    }

    return success;
}

static void MapVideoQualityManagerGoLiveParams(const ls_videoqualitymanager::GoLiveParams& in, VideoQualityManagerGoLiveParams* out)
{
    out->width = in.width();
    out->height = in.height();
    out->fps = in.fps();
}

static void MapVideoQualityManagerCameraStrategy(const ls_videoqualitymanager::CameraStrategy& in, VideoQualityManagerCameraStrategy* out)
{

    for (int i = 0; i < in.format_list_size(); ++i)
    {
        VIDEO_PIXEL_FORMAT fmt = static_cast<VIDEO_PIXEL_FORMAT>(in.format_list(i));
        out->format_list.push_back(fmt);
    }

    out->width = in.width();
    out->height = in.height();
    out->fps = in.fps();
    out->min_fps = in.min_fps();
    out->max_fps = in.max_fps();
}

static void MapVideoQualityManagerCameraParamsOut(const VideoQualityManagerCameraParamsOut& in, ls_videoqualitymanager::CameraParamsOut* out)
{
    out->set_width(in.width);
    out->set_height(in.height);
    out->set_fps(in.fps);
    out->set_max_fps(in.max_fps);
    out->set_min_fps(in.min_fps);
    out->set_format(static_cast<ls_basicenum::VIDEO_PIXEL_FORMAT>(in.format));

    for (const auto& val : in.optional_fps)
    {
        out->add_optional_fps(val);
    }
}

bool VQMPBModule::QueryCameraRecommendedParams::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    VideoQualityManagerQueryCameraRecommendedParamsRequest  in_param;
    VideoQualityManagerQueryCameraRecommendedParamsResponse out_param;

    in_param.camera_name = req.camera_name();
    if (req.has_current_device_level())
    {
        in_param.current_device_level = req.current_device_level();
    }

    for (int i = 0; i < req.capture_params_list_size(); ++i)
    {
        VideoQualityManagerCameraParams r;
        MapVideoQualityManagerCameraParams(req.capture_params_list(i), &r);
        in_param.capture_params_list.push_back(r);
    }

    {
        VideoQualityManagerGoLiveParams r;
        MapVideoQualityManagerGoLiveParams(req.current_enc_params(), &r);
        in_param.current_enc_params = r;
    }

    if (req.has_current_camera_params())
    {
        VideoQualityManagerCameraParams r;
        MapVideoQualityManagerCameraParams(req.current_camera_params(), &r);
        in_param.current_camera_params = r;
    }

    if (req.has_topicid())
    {
        in_param.topicId = req.topicid();
    }

    // 如果用户选择了话题，则传入话题
    {
        VideoQualityManagerCameraStrategy r;
        MapVideoQualityManagerCameraStrategy(req.strategy(), &r);
        in_param.strategy = r;
    }

    bool success = mgr->VideoQualityManagerQueryCameraRecommendedParams(in_param, &out_param);

    if (success)
    {
        MapVideoQualityManagerCameraParamsOut(out_param.recommend_params, rsp.mutable_recommend_params());
        rsp.set_resolution_prompt(out_param.resolution_prompt);
        rsp.set_fps_prompt(out_param.fps_prompt);

        for (const auto& info : out_param.optional_params)
        {
            auto r = rsp.add_optional_params();
            MapVideoQualityManagerCameraParamsOut(info, r);
        }
    }

    return success;
}

bool VQMPBModule::QueryCameraBestParamsForTarget::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    VideoQualityManagerQueryCameraBestParamsForTargetRequest  in_param;
    VideoQualityManagerQueryCameraBestParamsForTargetResponse out_param;

    MapVideoQualityManagerCameraStrategy(req.camera_strategy(), &in_param.camera_strategy);

    for (int i = 0; i < req.capture_params_list_size(); ++i)
    {
        VideoQualityManagerCameraParams r;
        MapVideoQualityManagerCameraParams(req.capture_params_list(i), &r);
        in_param.capture_params_list.push_back(r);
    }

    in_param.current_device_level = req.current_device_level();

    bool success = mgr->VideoQualityManagerQueryCameraBestParamsForTarget(in_param, &out_param);

    if (success)
    {
        MapVideoQualityManagerCameraParamsOut(out_param.camera_best_params, rsp.mutable_camera_best_params());
    }

    return success;
}

bool VQMPBModule::ReconfigVideoOutput::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    VideoOutputParamsRequest  in_param;
    in_param.stream_id = req.stream_id();
    in_param.fps = req.fps();
    in_param.width = req.width();
    in_param.height = req.height();
    in_param.reason = req.reason();
    in_param.abr_strategy = req.abr_strategy();
    VideoOutputParamsResponse out_param;

    bool success = mgr->ReconfigVideoOutput(in_param, &out_param);
    rsp.set_result(out_param.result);
    return success;
}

bool VQMPBModule::FallbackVideoEncoder::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    FallbackVideoEncoderParamsRequest  in_param;
    in_param.codec_id = req.codec_id();
    in_param.codec_name = req.codec_name();
    in_param.bitrate = req.bitrate();
    in_param.codec_param_json = req.codec_param_json();

    bool success = mgr->FallbackVideoEncoder(in_param);

    return success;
}

bool VQMPBModule::SetPreprocessDefaultSize::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    bool success = mgr->SetPreprocessDefaultSize(req.visual_id(), req.cx(), req.cy());
    return success;
}

bool VQMPBModule::RemovePreprocessDefaultSize::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    bool success = mgr->RemovePreprocessDefaultSize(req.visual_id());
    return success;
}

bool VQMPBModule::StartAdaptiveGearStrategyReport::doHandle(const In& req, Out& rsp)
{
    auto controller = PBBridge::GetInstance().GetController();
    if (!controller)
        return false;

    media_mgr::MediaMgr* mgr = (media_mgr::MediaMgr*)controller->GetMediaMgrImpl();
    if (!mgr)
        return false;

    bool success = mgr->StartAdaptiveGearStrategyReport(req.stream_id(), req.abr_config());
    return success;
}

} // namespace MediaSDK