#include "Preview.h"
#include "ModeSceneMgr.h"
#include "MediaMgr.h"
#include "MediaSDKControllerV2Impl.h"
#include "stringutil.h"
#include "FilterMgr.h"

extern sdk_helper::MediaSDKControllerImplV2* g_sdkController;
extern media_mgr::MediaMgr* g_mediaMgr;

Preview::Preview() {}

Preview::~Preview() {}

bool Preview::Create()
{
    if (!g_sdkController->CreatePreview(m_previewInfo))
    {
        LOG(ERROR) << "[Preview::CreatePreview] CreatePreview failed, previewID: " << m_previewInfo.id;
        return false;
    }

    m_previewInfo.isCreated = true;
    if (!g_mediaMgr->UpdateVideoModelLayout(m_previewInfo))
    {
        LOG(ERROR) << "[Preview::Create] UpdateVideoModelLayout failed, previewID: " << m_previewInfo.id;
        return false;
    }

    return true;
}

bool Preview::Destroy()
{
    if (!g_mediaMgr->DestroyPreview(m_previewInfo.id))
    {
        LOG(ERROR) << "[Preview::DestroyPreview] DestroyPreview failed, previewID: " << m_previewInfo.id;
        return false;
    }

    if (m_previewInfo.videoModel != UINT32_MAX)
    {
        if (!g_mediaMgr->DestroyVideoModel(m_previewInfo.videoModel))
        {
            LOG(ERROR) << "[Preview::DestroyPreview] DestroyVideoModel failed, videoModel: " << m_previewInfo.videoModel;
            return false;
        }
    }

    return true;
}

void Preview::SetPreviewInfo(const PREVIEW_INFO* previewInfo)
{
    m_previewInfo = *previewInfo;
}

void Preview::GetPreviewInfo(PREVIEW_INFO* previewInfo)
{
    *previewInfo = m_previewInfo;
}

void Preview::UpdatePreviewLayout(const PREVIEW_INFO& previewInfo)
{
    m_previewInfo = previewInfo;
    if (!g_mediaMgr->UpdateVideoModelLayout(m_previewInfo))
    {
        LOG(ERROR) << "[Preview::UpdatePreviewLayout] UpdateVideoModelLayout failed, previewID: " << m_previewInfo.id << ", videoModel: " << previewInfo.videoModel;
        return;
    }
}

bool Preview::BindCanvas(UINT64 canvasID)
{
    if (Canvas* pCanvas = ModeSceneMgr::GetInstance()->GetCanvasByID(canvasID))
    {
        pCanvas->SetPreviewID(m_previewInfo.id);

        CANVAS_INFO_EX canvasInfoEx;
        pCanvas->GetCanvasInfo(&canvasInfoEx);
        
        std::string canvas_id = "";
        Util::NumToString(canvasInfoEx.id, &canvas_id);

        std::string transition_id = "";
        if ((canvasInfoEx.filter.type == FILTER_CANVAS) && (FilterMgr::GetInstance()->GetFilterByID(canvasInfoEx.filter.id) != nullptr) && m_previewInfo.videoModel != UINT32_MAX)
            Util::NumToString(canvasInfoEx.filter.id, &transition_id);

        if (!m_previewInfo.isCreated)
        {
            if (!g_mediaMgr->UpdateVideoModelLayout(m_previewInfo))
            {
                LOG(ERROR) << "[Preview::BindCanvas] UpdateVideoModelLayout failed, previewID: " << m_previewInfo.id;
                return false;
            }

            m_previewInfo.isCreated = true;
        }

        if (std::find(m_previewInfo.canvasIDs.begin(), m_previewInfo.canvasIDs.end(), canvasID) == m_previewInfo.canvasIDs.end())
        {
            m_previewInfo.canvasIDs.push_back(canvasID);
        }
        m_previewInfo.curCanvasID = canvasID;        
        LOG(INFO) << "[Preview::BindCanvas] previewID: " << m_previewInfo.id << ", curCanvasID: " << m_previewInfo.curCanvasID << ", transitionID: " << transition_id;
        if (!g_sdkController->SetCanvasToPreview(m_previewInfo.id, canvas_id, transition_id))
        {
            LOG(ERROR) << "[Preview::BindCanvas] SetCanvasToPreview failed, previewID: " << m_previewInfo.id << ", canvasID: " << canvas_id << ", transitionID: " << transition_id;
            return false;
        }

        if (!g_mediaMgr->SetDisplay(m_previewInfo.id, true))
        {
            LOG(ERROR) << "[Preview::BindCanvas] SetDisplay failed, previewID: " << m_previewInfo.id;
            return false;
        }

        return true;
    }

    return false;
}

bool Preview::UnbindCanvas(UINT64 canvasID)
{
    LOG(INFO) << "[Preview::ApplyTransition] UnbindCanvas canvasID: " << canvasID << ", previewID: " << m_previewInfo.id;
    auto iter = std::find(m_previewInfo.canvasIDs.begin(), m_previewInfo.canvasIDs.end(), canvasID);
    if (iter != m_previewInfo.canvasIDs.end())
    {
        m_previewInfo.canvasIDs.erase(iter);
        return true;
    }
    return false;
}

void Preview::SetCurCanvasID(UINT64 canvasID)
{
    m_previewInfo.curCanvasID = canvasID;
}

void Preview::SetCanvasIDs(const std::vector<UINT64>& canvasIDs)
{
    m_previewInfo.canvasIDs = canvasIDs;
}
