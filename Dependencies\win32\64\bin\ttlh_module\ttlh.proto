// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";
package ttlh_proto;

message TTLHMessage {
  enum MessageType {
    kRequest = 0;
    kResponse = 1;
  }
  MessageType type = 1;
  optional string id = 2;
  oneof data {
    TTLHRequest req = 3;
    TTLHResponse rsp = 4;
  }
}

// 前端调用 ttlh api 或者 ttlh 内部发起的 event 均由 TTLHRequest 封装
message TTLHRequest {
  string name = 1;
  bytes data = 2;
}

// Request 的响应
message TTLHResponse {
  bool status = 1;
  string name = 2;
  bytes data = 3;
  sint32 error_code = 4;
}

message GearResolution {
  repeated sint32 wide_screen = 3;
  repeated sint32 standard_screen = 4;
}

message Gear {
  sint32 id = 1;
  string name = 2;
  GearResolution resolution = 3;
  sint32 fps = 4;
}

// 带宽范围-档位结构体
message BwGearIdEntry {
  sint64 min = 1;
  sint64 max = 2;
  sint32 gear_id = 3;
}

message VideoBitrate {
  sint32 min = 1;
  sint32 target = 2;
  sint32 max = 3;
}

message Bitrate {
  sint32 audio = 1;
  sint32 gear_id = 2;
  map<string, VideoBitrate> video_wide_screen = 3;
  map<string, VideoBitrate> video_standard_screen = 4;
}

message TopicConfig {
  repeated BwGearIdEntry bw_table = 1;
  map<string, sint32> device_level_table = 2;
  sint32 quality_perf_level_step = 3;
  bool enable_camera_enc_linkage = 4;
  repeated Bitrate bitrate_table = 5;
  repeated sint32 topic_id_list = 6;
  string topic_type = 7;
  repeated sint32 sub_topic_id_list = 8;
  string topic_category = 9;
}

// 策略配置
message StrategyConfig {
  // 策略优先级
  sint32 priority = 1;

  // 码表 index，表明使用哪张码表
  uint32 bitrate_table_index = 2;

  // 默认档位
  sint32 default_level = 3;

  // 升降级规则
  map<sint32, sint32> gear_shift_rule = 4;

  // 升降级类型
  // 1:down,2:up,3:down_and_up
  sint32 gear_shift_type = 5;

  // 策略名
  string name = 6;
}

// 预留系数
message BwReserveFactorEntry {
  // 最低带宽
  sint64 min = 1;
  // 最高带宽，不包括该值
  sint64 max = 2;
  float reservation = 3;
}

message DualCanvasConfig {
  repeated BwReserveFactorEntry bw_probe_reservation = 1;
  float bitrate_alloc_ratio = 2;
}

message Rect {
  sint32 width = 1;
  sint32 height = 2;
}


message CameraSuggestedConfig {
  repeated sint32 resolution = 1;
  repeated sint32 fps = 2;
  optional sint32 resolution_limit = 3;
}

message CameraRecommendConfig {
  // key: device level
  map<string, CameraSuggestedConfig> device_level_camera_recommend_config = 1;
  sint32 raise_threshold_for_fps = 2;
}

message RecommendStrategy {
  repeated TopicConfig topic_config = 1;
  repeated StrategyConfig gear_table_priority = 2;
  DualCanvasConfig dual_canvas_config = 3;
  CameraRecommendConfig camera_recommend_config = 4;
  string default_bitrate_table_topic = 5;
  // v1 参数
  optional CameraDefaultRecommend camera_default_recommend = 6;
}

message GoLiveParams {
  sint32 width = 1;
  sint32 height = 2;
  sint32 fps = 3;
}

/// =============== 返回值结构体 =======================
// 视频宽高参数
message VideoResolutionOut {
  float aspect_ratio = 1;
  sint32 width = 2;
  sint32 height = 3;
}

message GearOut {
  sint32 id = 1;
  string name = 2;
  GearResolution resolution = 3;
  sint32 fps = 4;
}

message BitrateOut {
  sint32 audio = 1;
  sint32 gear_id = 2;
  // key:codec
  map<string, VideoBitrate> video_wide_screen = 3;
  map<string, VideoBitrate> video_standard_screen = 4;
  float dual_canvas_video_bitrate_alloc_ratio = 5;
}

message GoLiveParamsOut {
  GearOut gear = 1;
  BitrateOut bitrate = 2;
  string decision_process_info = 3;
}

message TopicGoLiveParams {
  // 质量优先
  GoLiveParamsOut quality_priority = 1;
  // 性能优先
  GoLiveParamsOut performance_priority = 2;
}

message RecommendedGoLiveParams {
  string topic_type = 1;
  repeated sint32 topic_id_list = 2;
  // 每个话题的推荐结果
  TopicGoLiveParams topic_go_live_params = 3;
  repeated sint32 sub_topic_id_list = 4;
  string topic_category = 5;
}

message CameraDefaultRecommend
{
  Rect resolution = 1;
  sint32 fps = 2;
}


enum VIDEO_PIXEL_FORMAT
{
  PIXEL_FORMAT_UNKNOWN = 0;
  PIXEL_FORMAT_I420 = 1;
  PIXEL_FORMAT_YV12 = 2;
  PIXEL_FORMAT_NV12 = 3;
  PIXEL_FORMAT_NV21 = 4;
  PIXEL_FORMAT_UYVY = 5;
  PIXEL_FORMAT_YUY2 = 6;
  PIXEL_FORMAT_ARGB = 7;
  PIXEL_FORMAT_XRGB = 8;
  PIXEL_FORMAT_RGB24 = 9;
  PIXEL_FORMAT_RGBA = 10;
  PIXEL_FORMAT_BGR24 = 11;
  PIXEL_FORMAT_BGRA = 12;
  PIXEL_FORMAT_MJPEG = 13;
  PIXEL_FORMAT_I444 = 14;
  PIXEL_FORMAT_I444A = 15;
  PIXEL_FORMAT_I420A = 16;
  PIXEL_FORMAT_I422 = 17;
  PIXEL_FORMAT_I422A = 18;
  PIXEL_FORMAT_YVYU = 19;
  PIXEL_FORMAT_P010 = 20;
  PIXEL_FORMAT_P016 = 21;
  PIXEL_FORMAT_NV12_MS = 22;
  PIXEL_FORMAT_P010_MS = 23;
  PIXEL_FORMAT_P016_MS = 24;
  PIXEL_FORMAT_I010 = 25;
  PIXEL_FORMAT_V210 = 26;
  PIXEL_FORMAT_I210 = 27;
  PIXEL_FORMAT_HDYC = 28;
};

message CameraStrategy
{
  repeated VIDEO_PIXEL_FORMAT format_list = 1;
  sint32 width = 2;
  sint32 height = 3;
  sint32 fps = 4;
  sint32 min_fps = 5;
  sint32 max_fps = 6;
}

// 初始化接口
message VQSCInitialize {
  message Request {
    repeated Gear gear_table = 1;
    RecommendStrategy recommend_strategy = 2;
    map<string, string> current_topic_device_level = 3;
    bool use_old_recommend_way = 4;
    optional string extra_device_level = 5;
  }

  message Response {
    GoLiveParamsOut default_go_live_params = 1;
  }
}

// 手动选档查询码表的接口
message VQSCQueryGoLiveManuallySelectedParams {
  message Request {
    sint32 gear_id = 1;
    // 如果用户选择了话题，则传入话题 id
    optional sint32 topic_id = 2;
  }

  message Response {
    GoLiveParamsOut go_live_params = 1;
  }
}

// 相机推荐采集参数接口
message VQSCQueryCameraRecommendedParams {
  message Request {
    string camera_name = 1;
    optional string current_device_level = 2;
    repeated CameraParams capture_params_list = 3;
    optional GoLiveParams current_enc_params = 4;
    optional CameraParams current_camera_params = 5;
    // 如果用户选择了话题，则传入话题
    optional sint32 topicId = 6;
    optional CameraStrategy strategy = 7;
  }

  message Response {
    CameraParamsOut recommend_params = 1;
    string resolution_prompt = 2;
    string fps_prompt = 3;
    repeated CameraParamsOut optional_params = 4;
  }
}

// 开播参数推荐接口，在测速后使用
message VQSCQueryGoLiveRecommendedParams {
  message Request {
    optional sint64 bw_probe_bps = 1;
    map<string, string> current_topic_device_level = 2;
    optional CameraParams camera_max_params = 3;
    repeated sint32 topic_id_list = 4;
    repeated sint32 sub_topic_id_list = 5;
    optional string extra_device_level = 6;
  }

  message Response {
    repeated RecommendedGoLiveParams go_live_recommend_params = 1;
  }
}

message VQSCQueryCameraBestParamsForTarget {
  message Request {
    CameraStrategy camera_strategy = 1;
    repeated CameraParams capture_params_list = 2;
    string current_device_level = 3;
  }

  message Response {
    CameraParamsOut camera_best_params = 1;
  }
}

// 质量模式
enum QuailtyMode {
  kModeUnknown = 0;
  // 质量优先
  kQualityPriority = 1;
  // 性能优先
  kVQMPerfPriroity = 2;
};


// 相机采集参数
message CameraParams {
  int32 width = 1;
  int32 height = 2;
  int32 fps = 3;
  int32 max_fps = 4;
  int32 min_fps = 5;
  VIDEO_PIXEL_FORMAT format = 6;
}

message CameraParamsOut {
  int32 width = 1;
  int32 height = 2;
  int32 fps = 3;
  int32 max_fps = 4;
  int32 min_fps = 5;
  VIDEO_PIXEL_FORMAT format = 6;
  repeated sint32 optional_fps = 7;
}

// adaptive strategy api
message AdaptiveStrategyPerfInit {
  message Request {
    string data = 1;
  }
}

message AdaptiveStrategyPerfUnInit {
  message Request {
  }
}

message AdaptiveStrategyPerfSetParameters {
  message Request {
    string data = 1;
  }
}

message AdaptiveStrategyNetInit {
  message Request {
    string data = 1;
  }
}

message AdaptiveStrategyNetUnInit {
  message Request {
  }
}

message AdaptiveStrategyNetSetParameters {
  message Request {
    string data = 1;
  }
}

message StarshipGetParameters {
  message Request {
  }
  message Response {
    string data = 1;
  }
}

message StarshipSetParameters {
  message Request {
    string data = 1;
  }
}


message Callback {
    message AdaptiveStrategyResultCallback {
        string data = 1;
    }
    message AdaptiveStrategyErrorCallback {
        string data = 1;
    }
    message AdaptiveStrategyUnitQueueCallback {
        string data = 1;
    }
}



