﻿#include "MediaSDKCallBack.h"

#include "mediasdk_v2_header/mediasdk_defines_rtc.h"
#include "mediasdk_v2_header/mediasdk_callback_defines.h"
#include "mediasdk_v2_header/mediasdk_defines_stream_error_code.h"
#include "mediasdk_v2_header/base/base64.hpp"
#include "mediasdk_v2_header/plugins/browser_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/image_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/desktop_capture_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/fav_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/wasapi_audio_source_helper.h"
#include "mediasdk_v2_header/plugins/app_audio_capture_helper.h"
#include "mediasdk_v2_header/plugins/dshow_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/bytelink_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/graffiti_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/game_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/rtc_visual_source_helper.h"
#include "mediasdk_v2_header/plugins/effect_visual_filter_helper.h"

#include "NativeBridge/StreamSEIBuilder.h"
#include "librtc/rtc/bytertc_rts_defines.h"
#include "LSPublicHeader.h"
#include "EnumConverter.h"
#include "MediaMgr.h"
#include "AudioMgr.h"
#include "SourceMgr.h"
#include "ModeSceneMgr.h"
#include "stringutil.h"
#include "FilterMgr.h"
#include "MediaSDKControllerV2Impl.h"
#include "LogDumpMgr.h"

extern media_mgr::MediaMgr* g_mediaMgr;
extern CIEF::ICIEF* g_cief;

// Add missing closure definition
using closure = std::function<void()>;

class EventTask final : public CIEF::ITask
{
public:
    explicit EventTask(const closure& task)
    {
        m_task = task;
    }

    void Release() override
    {
        delete this;
    }

    void DoTask(CIEFGUID thread) override
    {
        m_task();
    }

	void QuitTask() override {}

protected:
    closure m_task;
};

namespace sdk_helper
{
	extern MediaSDKV2API g_sdk_api;
	extern std::map<std::string, STREAM_TYPE> g_startStreamType;
    extern std::mutex                         g_mutex_;
    extern std::set<std::string>              g_rtc_visual_ids;

	static std::mutex* g_visual_capture_type_changed_mutex = new std::mutex;
	static std::mutex* g_on_encode_event_mutex = new std::mutex;

static void NotifyStreamEncodeEvent(const std::string& stream_id, const std::string& json_info)
{
    StreamEncodeEvent event;
    event.streamID = stream_id;
    event.jsonInfo = json_info;

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

static std::vector<FORWARD_STREAM_STATE_INFO> MapRTCControllerForwardStreamStateInfo(mediasdk::MediaSDKString stream_state_infos)
{
	std::vector<FORWARD_STREAM_STATE_INFO> out;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(stream_state_infos.ToString());
			if (output_json.empty())
				break;

			if (!output_json.contains("forward_stream_state_infos"))
				break;

			nlohmann::json state_infos = output_json["forward_stream_state_infos"];
			for (auto info : state_infos)
			{
				FORWARD_STREAM_STATE_INFO res;
				res.roomID = info["room_id"];
				res.forwardStateMask = info["state"];
				res.forwardErrorMask = info["error"];
				out.push_back(res);
			}
		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse forward stream_state_infos error!";
	}
	return out;
}

static std::vector<NETWORK_STATES_INFO> MapRTCControllerNetWorkStats(mediasdk::MediaSDKString quality)
{
	std::vector<NETWORK_STATES_INFO> out;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(quality.ToString());
			if (output_json.empty())
				break;
			NETWORK_STATES_INFO stats;
			if (output_json.contains("local_quality"))
			{
				nlohmann::json            local_info = output_json["local_quality"];
				stats.uid = local_info["uid"];
				stats.fractionLost = local_info["fraction_lost"];
				stats.rtt = local_info["rtt"];
				stats.totalBandwidth = local_info["total_bandwidth"];
				stats.txQuality = local_info["tx_quality"];
				stats.rxQuality = local_info["rx_quality"];
				out.push_back(stats);
			}

			if (output_json.contains("remote_qualities"))
			{
				nlohmann::json remote_infos = output_json["remote_qualities"];
				for (auto info : remote_infos)
				{
					stats.uid = info["uid"];
					stats.fractionLost = info["fraction_lost"];
					stats.rtt = info["rtt"];
					stats.totalBandwidth = info["total_bandwidth"];
					stats.txQuality = info["tx_quality"];
					stats.rxQuality = info["rx_quality"];
					out.push_back(stats);
				}
			}


		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse NetWork stats error!";
	}
	return out;
}

static StreamStatsEvent MapLocalRTCControllerStreamStats(std::string uid, std::string stats)
{
	StreamStatsEvent out{};
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(stats);
			if (output_json.empty())
				break;
			out.uid = uid;

			nlohmann::json video_json = output_json["video_stats"];

			// TODO replace is_screen by stream_index
			// out.is_screen = video_json["is_screen"];

			out.videoSendTargetBitrate = 0;
			out.videoSendBitrate = video_json["sent_kbitrate"];                  //
			out.videoSentRate = static_cast<int>(video_json["sent_frame_rate"]); //
			out.videoLossRate = static_cast<int>(video_json["video_loss_rate"]);
			out.videoEncoderTargetRate = 0;
			out.videoEncoderRate = static_cast<int>(video_json["encoder_output_frame_rate"]); //
			out.localTxQuality = output_json["local_tx_quality"];                             //
			out.width = video_json["encoded_frame_width"];                                      //
			out.height = video_json["encoded_frame_height"];                                    //
			out.codecType = video_json["codec_type"];
		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "[MapLocalRTCControllerStreamStats] parse stream stats error! uid = " << uid;
	}
	return out;
}

static StreamStatsEvent MapRemoteRTCControllerStreamStats(std::string uid, std::string stats)
{
	StreamStatsEvent out{};
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(stats);
			if (output_json.empty())
				break;
			out.uid = uid;

			nlohmann::json video_json = output_json["video_stats"];
			// TODO replace is_screen with stream_index
			// out.is_screen = video_json["is_screen"];
			out.videoSendTargetBitrate = 0;
			out.videoSendBitrate = 0;
			out.videoSentRate = 0;
			out.videoLossRate = static_cast<int>(video_json["video_loss_rate"]);
			out.videoEncoderTargetRate = 0;
			out.videoEncoderRate = 0;
			out.localTxQuality = 0;
			out.width = video_json["width"];
			out.height = video_json["height"];
			out.codecType = video_json["codec_type"];
		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "[MapRemoteRTCControllerStreamStats] parse stream stats error! uid = " << uid;
	}
	return out;
}

static nlohmann::json MapLocalAudioProperties(mediasdk::MediaSDKString audio_properties_infos)
{
	nlohmann::json json_root;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(audio_properties_infos.ToString());
			if (output_json.empty())
				break;
			nlohmann::json infos = output_json["local_audio_properties_infos"];
			for (auto info : infos)
			{
				nlohmann::json array_item;
				array_item["uid"] = 0; // TODO: local is no 0, check this, interface changed
				array_item["volume"] = info["linear_volume"];
				array_item["nonlinear_volume"] = info["nonlinear_volume"];
				json_root.push_back(array_item);
			}
		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse local audio properties infos error!";
	}
	return json_root;
}

static nlohmann::json MapRemoteAudioProperties(mediasdk::MediaSDKString audio_properties_infos)
{
	nlohmann::json json_root = nlohmann::json::array();
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(audio_properties_infos.ToString());
			if (output_json.empty())
				break;
			nlohmann::json infos = output_json["remote_audio_properties_infos"];
			for (auto info : infos)
			{
				nlohmann::json array_item;
				array_item["uid"] = info["user_id"];
				array_item["volume"] = info["linear_volume"];
				array_item["nonlinear_volume"] = info["nonlinear_volume"];
				json_root.push_back(array_item);
			}
		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse remote audio properties infos error!";
	}
	return json_root;
}

SDKCallBackMgr::SDKCallBackMgr(sdk_helper::CallHelper& call_helper) : call_helper_(call_helper){}

SDKCallBackMgr::~SDKCallBackMgr(){}

void SDKCallBackMgr::SetThreadMonitorEventHandler(sdk_helper::ThreadMonitorEventHandler handler)
{
	std::unique_lock<std::mutex> lock(thread_monitor_handler_mutex_);
	thread_monitor_handler_ = handler;
}

void SDKCallBackMgr::AddMobileProjectorID(const std::string& id)
{
	mobile_projector_id_.insert(id);
}

void SDKCallBackMgr::EraseMobileProjectorID(const std::string& id)
{
	if (mobile_projector_id_.count(id) != 0)
	{
		mobile_projector_id_.erase(id);
	}
}

void SDKCallBackMgr::SetVirtualCameraID(std::string virtual_camera_id)
{
	m_virtual_camera_id = virtual_camera_id;
}

// MediaSDKGlobalEventObserver
void SDKCallBackMgr::OnPluginGlobalEvent(mediasdk::PluginInfo info, mediasdk::MediaSDKString event)
{
    nlohmann::json    output_json;
    bool              success = false;
    int               changeType = -1;
    std::string       eventName;
    std::string       wszid;
    DWORD             status;
    AUDIO_DATA_FLOW   data_flow;
    AUDIO_DEVICE_ROLE role;
    std::string       device_name;
	LOG(INFO) << " OnPluginGlobalEvent event =  " << event.ToString();
	try
	{
		output_json = nlohmann::json::parse(event.ToString());
		eventName = output_json.value("event_name", "");
		changeType = static_cast<int>(output_json.value("event_type", -1));
		wszid = output_json.value("device_id", "");
		status = output_json.value("new_state", 0x00000001);
		data_flow = MapAudioDataFlow2Native(EDataFlow(output_json.value("flow", 0)), NULL);
		role = MapAudioDeviceRole2Native(ERole(output_json.value("role", 0)), NULL);
		device_name = output_json.value("device_name", "");
		success = true;
	}
	catch (...)
	{
		LOG(ERROR) << "parse audio event error!";
	}
	if (!success)
	{
		LOG(ERROR) << "[OnPluginGlobalEvent] get param error, event = " << event.ToString();
		return;
	}

    if (eventName == "APP_AUDIO_CAPTURE_SESSION_LIST_CHANGED")
    {
		AudioCapableAppChangedEvent event{};
        g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
        return;
    }
	if (eventName != "AUDIO_DEVICE_STATE_CHANGED")
	{
		return;
	}

	if (changeType == 0)
	{
        AudioDeviceStateChangeEvent event{};
        event.deviceID = wszid;
        event.deviceState = MapAudioDeviceState2Native(status, NULL);
        g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
	}
	else if (changeType == 1)
	{
        AudioDeviceAddEvent event{};
        event.deviceID = wszid;
        g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
	}
	else if (changeType == 2)
	{
        AudioDeviceRemoveEvent event{};
        event.deviceID = wszid;
        g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
	}
	else if (changeType == 3)
	{
        AudioDefaultDeviceChangeEvent event{};
        event.deviceID = wszid;
        event.dataFlow = data_flow;
        event.deviceRole = role;
        event.deviceName = device_name;
        g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
	}
}

void SDKCallBackMgr::OnRenderThreadEvent(mediasdk::RenderThreadEvent event_type)
{
	std::unique_lock<std::mutex> lock(thread_monitor_handler_mutex_);
	if (!thread_monitor_handler_)
		return;

	if (!thread_tick_start_)
	{
		thread_tick_start_ = true;
		thread_monitor_handler_(MONITOR_THREAD_ID::MONITOR_THREAD_ID_RENDER_THREAD, MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_START);
	}
	thread_monitor_handler_(MONITOR_THREAD_ID::MONITOR_THREAD_ID_RENDER_THREAD, MONITOR_THREAD_EVENT_TYPE::MONITOR_THREAD_EVENT_TYPE_TICK);
}

void SDKCallBackMgr::OnDeviceLostEvent(mediasdk::MSDevLostEvent event_type)
{
	DevLostEvent event = {};
	event.str = "";
	event.error = event_type.error;
	event.removeReason = event_type.remove_reason;
	event.driverDate = event_type.driver_date.ToString();
	event.driverName = event_type.driver_name.ToString();
	event.driverVer = event_type.driver_ver.ToString();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnTeaEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString event)
{
	std::string stdStr = event.ToString();
	const std::map<std::string, std::string> mp = {
		{"ByteVC0VideoEncoderSource", "ByteVC0"},
		{"ByteVC1VideoEncoderSource", "ByteVC1"},
		{"QSVH264BufferVideoEncoderSource", "H264_QSV"},
		{"QSVHevcBufferVideoEncoderSource", "HEVC_QSV"},
		{"NVHevcVideoEncoderSource", "HEVC_NVENC_EX"},
		{"NVHevcBufferVideoEncoderSource", "HEVC_NVENC"},
		{"NVH264VideoEncoderSource", "H264_NVENC_EX"},
		{"NVH264BufferVideoEncoderSource", "H264_NVENC"},
		{"AMFHEVCBufferVideoEncoderSource", "HEVC_AMF"},
		{"AMFHEVCVideoEncoderSource", "HEVC_AMF_EX"},
		{"AMFH264BufferVideoEncoderSource", "H264_AMF"},
		{"AMFH264VideoEncoderSource", "H264_AMF_EX"} };
	for (auto it : mp)
	{
		size_t found = stdStr.find(it.first);
		if (found != string::npos)
		{
			stdStr.replace(found, it.first.size(), it.second);
			break;
		}
	}

	const std::vector<std::string> patterns = {"sender_", "encoder_", "Present_", "pixel_format_", "buffer_type_"};	
	for (size_t i = 0; i < stdStr.length(); i++) 
	{
		for (const auto& pattern : patterns) 
		{
			if (i + pattern.length() < stdStr.length() && stdStr.substr(i, pattern.length()) == pattern) 
			{
				size_t pos = i + pattern.length();
				if (pos < stdStr.length()) 
				{
					if (stdStr[pos] == '2') 
					{
						stdStr[pos] = '0';
					} 
					else if (stdStr[pos] == '3') 
					{
						stdStr[pos] = '1';
					}
				}
			}
		}
	}

	TeaEvent tea_event{};
	tea_event.name = id.ToString();
	tea_event.params = stdStr;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([tea_event]() {
        eventbus::EventBus::PostEvent(tea_event);
    }));
}

void SDKCallBackMgr::OnParfaitContextEvent(mediasdk::MediaSDKString key, mediasdk::MediaSDKString value)
{
    LogDumpMgr::GetInstance()->SetParfaitContextInfo(key.ToString(), value.ToString(), true);
}

void SDKCallBackMgr::OnVqosDataReport(mediasdk::MediaSDKString data)
{
    VqosDataReportEvent event{};
    event.data = data.ToString();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

// MediaSDKWindowEventObserver
void SDKCallBackMgr::OnRButtonUp(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        MouseClickEvent event{};
        event.visualID = mouse_click_canvas_item_id_[preview_id];
        event.type = MOUSE_CLICK_EVENT_TYPE::MOUSE_CLICK_EVENT_UP;
        eventbus::EventBus::PostEvent(event);
        LOG(INFO) << "[MouseClickEvent] previewID: " << preview_id << ", layerID : " << event.visualID << ", type: " << event.type << ", videoModel: " << preview_id;
    }));
}

void SDKCallBackMgr::OnLButtonDblClk(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        MouseDoubleClickEvent event{};
        event.layerID = mouse_click_canvas_item_id_[preview_id];
        event.videoModel = ModeSceneMgr::GetInstance()->GetOldCanvasIdxByPreviewID(preview_id);
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[MouseDoubleClickEvent] previewID: " << preview_id << ", oldCanvasIdx: " << event.videoModel << ", layerID: " << event.layerID;
    }));
}

void SDKCallBackMgr::OnRButtonDown(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        SelectVisualEvent event{};
		event.visualID = mouse_click_canvas_item_id_[preview_id];
		event.manual = true;
        event.videoModel = ModeSceneMgr::GetInstance()->GetOldCanvasIdxByPreviewID(preview_id);
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[SelectVisualEvent] previewID: " << preview_id << ", oldCanvasIdx: " << event.videoModel << ", visualID: " << event.visualID << ", manual: " << event.manual;
    }));

    
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        MouseClickEvent event{};
		event.visualID = mouse_click_canvas_item_id_[preview_id];
		event.type = MOUSE_CLICK_EVENT_TYPE::MOUSE_CLICK_EVENT_DOWN;
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[MouseClickEvent] previewID: " << preview_id << ", layerID: " << event.visualID << ", type: " << event.type;
    }));
}

void SDKCallBackMgr::OnLButtonDown(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        SelectVisualEvent event{};
		event.visualID = mouse_click_canvas_item_id_[preview_id];
		event.manual = true;
        event.videoModel = ModeSceneMgr::GetInstance()->GetOldCanvasIdxByPreviewID(preview_id);
		if (event.visualID.find("_clip_mask") != std::string::npos)
			return;
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[SelectVisualEvent] previewID: " << preview_id << ", oldCanvasIdx: " << event.videoModel << ", visualID: " << event.visualID << ", manual: " << event.manual;
    }));

    
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        MouseClickEvent event{};
		event.visualID = mouse_click_canvas_item_id_[preview_id];
		event.type = MOUSE_CLICK_EVENT_TYPE::MOUSE_CLICK_EVENT_DOWN;
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[MouseClickEvent] previewID: " << preview_id << ", layerID: " << event.visualID << ", type: " << event.type;
    }));
}

void SDKCallBackMgr::OnCanvasCurrentItemChanged(mediasdk::MediaSDKString canvas_id, bool manuel, mediasdk::MediaSDKString canvas_item_id)
{
    UINT64 canvasID = 0;
    Util::StringToNum(canvas_id.ToString(), &canvasID);
    UINT32 previewID = ModeSceneMgr::GetInstance()->GetPreviewIDByCanvasID(canvasID);
    mouse_click_canvas_item_id_[previewID] = canvas_item_id.ToString();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        SelectVisualEvent event{};
        event.visualID = canvas_item_id.ToString();
        event.manual = manuel;
        event.videoModel = ModeSceneMgr::GetInstance()->GetOldCanvasIdxByPreviewID(previewID);
        eventbus::EventBus::PostEvent(event);
        LOG(INFO) << "[SelectVisualEvent] previewID: " << previewID << ", layerID: " << event.visualID << ", manuel: " << manuel;
    }));
}

void SDKCallBackMgr::OnLButtonUp(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
        MouseClickEvent event{};
        event.visualID = mouse_click_canvas_item_id_[preview_id];
        event.type = MOUSE_CLICK_EVENT_TYPE::MOUSE_CLICK_EVENT_UP;
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[MouseClickEvent] previewID: " << preview_id << ", layerID: " << event.visualID << ", type: " << event.type;
    }));
}

void SDKCallBackMgr::OnMouseLeave(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([preview_id]() {
		MouseLeaveEvent event{};
        event.videoModel = ModeSceneMgr::GetInstance()->GetOldCanvasIdxByPreviewID(preview_id);
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnMouseHover(uint32_t preview_id)
{
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([preview_id]() {
		MouseEnterEvent event{};
        event.videoModel = ModeSceneMgr::GetInstance()->GetOldCanvasIdxByPreviewID(preview_id);
        eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[MouseEnterEvent] previewID: " << preview_id << ", oldCanvasIdx: " << event.videoModel;
    }));
}

void SDKCallBackMgr::OnLButtonDownIgnoreTrackState(uint32_t preview_id)
{
    LOG(INFO) << "[OnLButtonDownIgnoreTrackState] previewID: " << preview_id;
    OnLButtonDown(preview_id);
}

void SDKCallBackMgr::OnTransitionFinished(const std::string& transition_id)
{
	LOG(INFO) << "[OnTransitionFinished] transitionID: " << transition_id;
}

// MediaSDKCanvasEventObserver
void SDKCallBackMgr::OnCurrentCanvasChanged(uint32_t preview_id, mediasdk::MediaSDKString canvas_id)
{
	LOG(INFO) << "[OnCurrentCanvasChanged] previewID: " << preview_id << ", canvasID: " << canvas_id.ToString();
}

void SDKCallBackMgr::OnCanvasItemsDestroyedByDestroyCanvas(mediasdk::MediaSDKString destory_canvas_id, mediasdk::MediaSDKStringArray canvas_item_ids)
{
	LOG(INFO) << "[OnCanvasItemsDestroyedByDestroyCanvas] destoryedCanvasID: " << destory_canvas_id.ToString();
}

void SDKCallBackMgr::OnHoveredCanvasItemChanged(mediasdk::MediaSDKString canvas_id, mediasdk::MediaSDKString canvas_item_id)
{
    LOG(INFO) << "[OnHoveredCanvasItemChanged] canvasID: " << canvas_id.ToString() << ", layerID: " << canvas_item_id.ToString();
}

static void HandleCanvasItemTransformChanged(const std::string& canvas_item_id, const TRANSFORM&& transform)
{
	UINT64 id = 0;
	Util::StringToNum(canvas_item_id, &id);

	if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(id))
	{
		LAYER_INFO layerInfo{};
		pLayer->GetLayerInfo(&layerInfo);

		TRANSFORM trans = layerInfo.transform;
		trans.hFlip = transform.hFlip;
		trans.vFlip = transform.vFlip;
		{
			float layerDescAngle = 0;
			pLayer->GetLayerDescAngle(&layerDescAngle);
			trans.angle = fmod(transform.angle - layerDescAngle, 360);
			trans.angle = trans.angle < 0.0f ? trans.angle + 360.0f : trans.angle;
		}
		trans.scale.X = transform.scale.X;
		trans.scale.Y = transform.scale.Y;
		trans.translate.X = transform.translate.X;
		trans.translate.Y = transform.translate.Y;
		trans.clipRange.x = transform.clipRange.x;
		trans.clipRange.y = transform.clipRange.y;
		trans.clipRange.z = transform.clipRange.z;
		trans.clipRange.w = transform.clipRange.w;

		pLayer->UpdateLayerTransform(&trans);

		layerInfo = {};
		pLayer->GetLayerInfo(&layerInfo);

		TransformChangedEvent event{};
		std::string           visualID = "";
		Util::NumToString(layerInfo.id, &visualID);
		event.visualID = visualID;
		event.transform = trans;
		event.visualSize = { layerInfo.transform.size.Width, layerInfo.transform.size.Height };

		Gdiplus::RectF previewRect{};
		pLayer->GetPreviewLayoutRect(&previewRect);
		event.canvasSize = { previewRect.Width, previewRect.Height };
		event.layout = layerInfo.layout;
		eventbus::EventBus::PostEvent(event);
	}
}

void SDKCallBackMgr::OnCanvasItemTransformChanged(mediasdk::MediaSDKString canvas_item_id, const mediasdk::MSTransform& transform)
{
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
		HandleCanvasItemTransformChanged(canvas_item_id.ToString(), MapTransform2Native(transform));
	}));
}

void SDKCallBackMgr::OnBeginTrack(uint32_t preview_id, mediasdk::MediaSDKString canvas_item_id, mediasdk::HittestCursorPos pos)
{
	bool ok = false;
	CURSOR_HIT_POSITION hit_pos = MapCursorHitPosition2Native(pos, &ok);
	if (!ok)
	{
		LOG(ERROR) << "[SDKCallBackMgr::OnEndTrack] MapCursorHitPosition2Native failed";
		return;
	}

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
		BeginTrackEvent event{};
		event.hitPos = hit_pos;
		event.layerID = canvas_item_id.ToString();
		eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[BeginTrackEvent] layerID: " << event.layerID << ", hitPos: " << event.hitPos << ", previewID: " << preview_id;
	}));
}

void SDKCallBackMgr::OnEndTrack(uint32_t preview_id, mediasdk::MediaSDKString canvas_item_id, mediasdk::HittestCursorPos pos)
{
	bool ok = false;
	CURSOR_HIT_POSITION hit_pos = MapCursorHitPosition2Native(pos, &ok);
	if (!ok)
	{
		LOG(ERROR) << "[SDKCallBackMgr::OnEndTrack] MapCursorHitPosition2Native failed";
		return;
	}

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
		EndTrackEvent event{};
		event.hitPos = hit_pos;
		event.layerID = canvas_item_id.ToString();
		eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[EndTrackEvent] layerID: " << event.layerID << ", hitPos: " << event.hitPos << ", previewID: " << preview_id;
	}));
}

void SDKCallBackMgr::OnCanvasItemInvalidArea(uint32_t preview_id, mediasdk::MediaSDKString canvas_item_id)
{
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
		MouseClickEvent event{};
		event.visualID = canvas_item_id.ToString();
		event.type = MOUSE_CLICK_EVENT_TYPE::MOUSE_CLICK_EVENT_INVALID_AREA;
		eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[MouseClickEvent] previewID: " << preview_id << ", layerID: " << event.visualID << ", type: " << event.type;
	}));
}

void SDKCallBackMgr::OnCanvasItemValidArea(uint32_t preview_id, mediasdk::MediaSDKString canvas_item_id)
{
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
		MouseClickEvent event{};
		event.visualID = canvas_item_id.ToString();
		event.type = MOUSE_CLICK_EVENT_TYPE::MOUSE_CLICK_EVENT_VALID_AREA;
		eventbus::EventBus::PostEvent(event);
        LOG(INFO) << "[MouseClickEvent] previewID: " << preview_id << ", layerID : " << event.visualID << ", type : " << event.type;
	}));
}

void SDKCallBackMgr::OnCanvasItemFilterNotify(mediasdk::MediaSDKString canvas_item_id, mediasdk::MediaSDKString filter_id, mediasdk::MediaSDKString notify, mediasdk::MediaSDKString json_data)
{
	try
	{
		nlohmann::json output_json = nlohmann::json::parse(json_data.ToString());
		if (notify.ToString() == "effect_notify_resource_load")
		{
			EffectResourceLoadEvent event{};
			event.visualID = canvas_item_id.ToString();
			event.msgID = output_json.value("msgID", 0);
			event.arg1 = output_json.value("arg1", 0);
			event.arg2 = output_json.value("arg2", 0);
			event.arg3 = output_json.value("arg3", "");
			event.effectFirstFrameElapsedTime = output_json.value("effect_first_frame_elapsed_time", "");
            event.effectPostFirstFrameElapsedTime = output_json.value("effect_replay_first_frame_elapsed_time", "");
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
			}));
		}
		else if (notify.ToString() == "effect_notify_msg")
		{
			EffectMsgEvent event{};
			event.visualID = canvas_item_id.ToString();
			event.msgID = output_json.value("msgID", 0);
			event.arg1 = output_json.value("arg1", 0);
			event.arg2 = output_json.value("arg2", 0);
			event.arg3 = output_json.value("arg3", "");
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
			}));
		}
		else if (notify.ToString() == "effect_notify_algo_use")
		{
			bool us = output_json["use"];
			// EffectParaEvent todo
		}
		else if (notify.ToString() == "effect_notify_gles_version")
		{
			int64_t version = output_json["version"];
			EffectGLVersionEvent event{};
			event.version = version;
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
				LOG(INFO) << "[EffectGLVersionEvent] version: " << event.version;
			}));
		}
		else if (notify.ToString() == "effect_notify_status")
		{
			bool enable = output_json["enable"];
			EffectStateEvent event{};
			event.layerID = canvas_item_id.ToString();
			event.valid = enable;
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
				LOG(INFO) << "[EffectStateEvent] layerID: " << event.layerID << ", valid: " << event.valid;
			}));
		}
		else if (notify.ToString() == "effect_notify_lag")
		{
			EffectLagEvent event{};
			event.visual_id = canvas_item_id.ToString();
			event.effect_info = output_json["effect_info"];
			event.is_first_frame = output_json["is_first_frame"];
			event.time = output_json["ts"];
			event.cost = output_json["cost"];
			event.type = output_json["type"];
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
				LOG(INFO) << "[EffectLagEvent] layerID: " << event.visual_id << ", effectInfo: " << event.effect_info << ", isFirstFrame: " << event.is_first_frame << ", time: " << event.time << ", cost: " << event.cost << ", type: " << event.type;
			}));
		}
	}
	catch (...)
	{
		LOG(ERROR) << "parse VisualFilterNotify json_data error!";
	}
}

void SDKCallBackMgr::OnCanvasItemShortcutActionNotify(mediasdk::MediaSDKString canvas_item_id, mediasdk::ShortcutAction shortcut_action)
{
	ShortcutActionEvent event{};
	event.layerID = canvas_item_id.ToString();
	event.action = MapShortCutAction2Native(shortcut_action, NULL);
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
		eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[ShortcutActionEvent] layerID: " << event.layerID;
	}));
}

void SDKCallBackMgr::OnCanvasItemClipMaskEnd(mediasdk::MediaSDKString canvas_item_id)
{
	ClipMaskEndEvent event{};
	event.layerID = canvas_item_id.ToString();
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
		eventbus::EventBus::PostEvent(event);
		LOG(INFO) << "[ClipMaskEndEvent] layerID: " << event.layerID;
	}));
}

void SDKCallBackMgr::OnCanvasFilterNotify(mediasdk::MediaSDKString canvas_id, mediasdk::MediaSDKString filter_id, mediasdk::MediaSDKString notify, mediasdk::MediaSDKString json_data)
{
	LOG(INFO) << "[CanvasFilterNotify] canvasID: " << canvas_id.ToString() << ", filterID: " << filter_id.ToString() << ", notify: " << notify.ToString() << ", jsonData: " << json_data.ToString();
}

void SDKCallBackMgr::OnCanvasItemTransformAnimationFinished(const mediasdk::MediaSDKString& canvas_item_id, bool interrupted, const mediasdk::MSTransform& transform)
{
	VibeTransformAnimationFinishedEvent event{};
	event.visualID = canvas_item_id.ToString();
    event.interrupted = interrupted;
    event.transform = MapTransform2Native(transform);

	UINT64 layerID = 0;
    Util::StringToNum(event.visualID, &layerID);
    if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID))
    {
        LAYER_INFO layerInfo;
        pLayer->GetLayerInfo(&layerInfo);
        event.transform.size = layerInfo.transform.size;
        layerInfo.transform = event.transform;
        pLayer->SetLayerInfo(&layerInfo);
    }

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
		eventbus::EventBus::PostEvent(event);
        LOG(INFO) << "[VibeTransformAnimationFinishedEvent] layerID: " << event.visualID << ", interrupted: " << event.interrupted << ", transform: " << event.transform.toString();
	}));
}

void SDKCallBackMgr::OnCanvasItemVisibleAnimationFinished(const mediasdk::MediaSDKString& canvas_item_id, bool interrupted, bool visible)
{
	VibeVisibleAnimationFinishedEvent event{};
	event.visualID = canvas_item_id.ToString();
    event.interrupted = interrupted;
    event.visible = visible;

    UINT64     layerID = 0;
    Util::StringToNum(event.visualID, &layerID);
	if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID))
	{
        LAYER_INFO layerInfo;
        pLayer->GetLayerInfo(&layerInfo);
        layerInfo.show = visible;
        pLayer->SetLayerInfo(&layerInfo);
	}

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
		eventbus::EventBus::PostEvent(event);
        LOG(INFO) << "[VibeVisibleAnimationFinishedEvent] layerID: " << event.visualID << ", interrupted: " << event.interrupted << ", visible: " << event.visible;
	}));
}

static void HandleVisualSizeChanged(UINT64 layerID, UINT64 sourceID, const float width, const float height)
{
    SOURCE_INFO sourceInfo{};
    SourceMgr::GetInstance()->GetSourceInfoByID(sourceID, &sourceInfo);
    sourceInfo.size.Width = width;
    sourceInfo.size.Height = height;
    SourceMgr::GetInstance()->SetSourceInfoByID(sourceID, sourceInfo);
	ModeSceneMgr::GetInstance()->HandleLayerSizeChange(layerID, width, height);
}

// MediaSDKVisualEventObserver
void SDKCallBackMgr::OnVisualSizeChanged(mediasdk::MediaSDKString id, const mediasdk::MSSize& new_size)
{
	std::string str_id = id.ToString();
	if (mobile_projector_id_.count(str_id) != 0)
	{
		g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, str_id]() {
			UINT64 sourceID = 0;
			Util::StringToNum(str_id, &sourceID);
			auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
			for (const auto& layerID : layerIDs)
			{
				std::string layer_id = "";
				Util::NumToString(layerID, &layer_id);
				auto clip = mediasdk::MSClipF{ 0, 0, 0, 0 };
				call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemClip, layer_id.c_str(), clip);
				call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemFlipH, layer_id.c_str(), false);
				call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemFlipV, layer_id.c_str(), false);
				call_helper_.SyncCall<bool>(g_sdk_api.SetCanvasItemRotate, layer_id.c_str(), 0.0);
				HandleCanvasItemTransformChanged(layer_id, TRANSFORM{});
			}
		}));
	}

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, str_id, new_size]() {
		UINT64 sourceID = 0;
		Util::StringToNum(str_id, &sourceID);
		auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
		for (const auto& layerID : layerIDs)
		{
			LOG(INFO) << "[OnVisualSizeChanged] visualID: " << str_id << ", layerID: " << layerID << ", new_size: [" << new_size.cx << "," << new_size.cy << "]";
            HandleVisualSizeChanged(layerID, sourceID, new_size.cx, new_size.cy);
		}
	}));
}

static void HandleGameStateChanged(const std::string& source_id, GAME_CAPTURE_EVENT_TYPE evt_type, const uint64_t timestamp, const std::string& msg)
{
	UINT64 sourceID = 0;
	if (!Util::StringToNum<UINT64>(source_id, &sourceID))
	{
		LOG(ERROR) << "[HandleGameStateChanged] Invalid source ID: " << source_id;
		return;
	}

	auto& compositeMetaMap = SourceMgr::GetInstance()->GetCompositeMetas();
	auto iter = compositeMetaMap.find(sourceID);
	if (iter == compositeMetaMap.end() || iter->second.primaryType != VISUAL_GAME)
	{
		LOG(WARNING) << "[HandleGameStateChanged] Source ID " << sourceID << " not found or not a game source";
		return;
	}

	COMPOSITE_SOURCE_META& compositeMeta = iter->second;
	std::vector<UINT64> layerIDs;
	auto updateLayerBindings = [](const std::vector<UINT64>& layerIDs, UINT64 targetSourceID) {
		for (UINT64 layerID : layerIDs) 
		{
			SourceMgr::GetInstance()->UpdateLayerBinding(layerID, targetSourceID);
		}
	};

	if (compositeMeta.isFallback)
	{
		layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(compositeMeta.fallbackSourceID);
	}
	else
	{
		layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(compositeMeta.primarySourceID);
	}

	LOG(INFO) << "[HandleGameStateChanged] Processing event for sourceID: " << sourceID << ", event type: " << static_cast<int>(evt_type) << ", fallback: " << (compositeMeta.isFallback ? "true" : "false") << ", layerCount: " << layerIDs.size() << ", message: " << msg;
	if (evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_MEM || evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_TEXTURE)
	{
		// Game injection succeeded
		LOG(INFO) << "[HandleGameStateChanged] Game capture succeeded for sourceID: " << sourceID;
		
		if (!SourceMgr::GetInstance()->CheckSourceExist(compositeMeta.primarySourceID))
		{
			LOG(INFO) << "[HandleGameStateChanged] Primary source does not exist, creating new one for sourceID: " << sourceID;
			
			SOURCE_INFO gameInfo{};
			gameInfo.type = VISUAL_GAME;
			gameInfo.source = std::get<GAME_SOURCE>(compositeMeta.source);

			if (UINT64 newGameSourceID = SourceMgr::GetInstance()->CreateSource(&gameInfo, nullptr))
			{
				LOG(INFO) << "[HandleGameStateChanged] Created new game source: " << newGameSourceID << " for original sourceID: " << sourceID;
				
				compositeMeta.primarySourceID = newGameSourceID;
                compositeMeta.isFallback = false;
                compositeMetaMap[newGameSourceID] = compositeMeta;
				compositeMetaMap.erase(sourceID);
				updateLayerBindings(layerIDs, newGameSourceID);
				
				// Stop any retry in progress when capture is successful
				SourceMgr::GetInstance()->StopGameRetry(sourceID);
			}
			else
			{
				LOG(ERROR) << "[HandleGameStateChanged] Failed to create new game source for sourceID: " << sourceID;
			}
		}
		else
		{
			LOG(INFO) << "[HandleGameStateChanged] Primary source exists, updating layer bindings for sourceID: " << sourceID;
			updateLayerBindings(layerIDs, compositeMeta.primarySourceID);
            compositeMeta.isFallback = false;
            compositeMetaMap[sourceID] = compositeMeta;
			// Stop any retry in progress when capture is successful
			SourceMgr::GetInstance()->StopGameRetry(compositeMeta.primarySourceID);
		}

		if (SourceMgr::GetInstance()->CheckSourceExist(compositeMeta.fallbackSourceID))
		{
			// Game capture successfully, destroy window
			SourceMgr::GetInstance()->DestroySource(compositeMeta.fallbackSourceID);
		}

		SourceMgr::GetInstance()->SetCompositeMetas(compositeMetaMap);
	}
	else if (evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_TIME_OUT ||
	         evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_TEXTURE ||
	         evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_INTERNAL ||
		     evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_DEVICE ||
			 evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_HOOK ||
			 evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_INITIALIZE ||
			 evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_ERROR_MEM_CREATE_ERROR)
	{
		// Game injection failed
		LOG(INFO) << "[HandleGameStateChanged] Game capture failed for sourceID: " << sourceID 
			<< ", error type: " << static_cast<int>(evt_type) 
			<< ", message: " << msg;
		
		// Handle game capture failure by setting up retry mechanism
		UINT64 targetSourceID = compositeMeta.isFallback ? sourceID : compositeMeta.primarySourceID;
		
		// Create retry context
		SourceMgr::GameRetryContext ctx;
		ctx.attemptCount = 0;
		ctx.firstFailureTime = std::chrono::steady_clock::now();
		ctx.pendingLayers = layerIDs;
		
		// Start retry process with enhanced state machine
		SourceMgr::GetInstance()->StartGameRetry(targetSourceID, ctx);
		
		// Trigger state machine transition to begin retry
		SourceMgr::GetInstance()->TriggerGameRetry(targetSourceID);
		LOG(INFO) << "[HandleGameStateChanged] Starting game retry for sourceID: " << targetSourceID << ", event type: " << static_cast<int>(evt_type) << ", msg: " << msg;
	}

	// Publish game state events for statistics
	for (const auto& layerID : layerIDs)
	{
		GameStateEvent game_event{};
		std::string layer_id = "";
		Util::NumToString(layerID, &layer_id);
		game_event.layerID = layer_id;
		game_event.type = evt_type;
		game_event.timestamp = timestamp;
		game_event.info = msg;
		eventbus::EventBus::PostEvent(game_event);
		LOG(INFO) << "[HandleGameStateChanged] GameStateEvent layerID: " << layer_id << ", eventType: " << evt_type << ", timestamp: " << timestamp << ", msg: " << msg;
	}
}

void SDKCallBackMgr::OnVisualSourceEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString event)
{
	try
	{
		nlohmann::json output_json = nlohmann::json::parse(event.ToString());
		if (output_json.contains("state"))
		{
			bool state = output_json["state"];
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, state, id]() {
				UINT64 sourceID = 0;
				Util::StringToNum(id.ToString(), &sourceID);
				const std::vector<UINT64> layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
				for (const auto& layerID : layerIDs)
				{
					StateChangeEvent state_event{};
					std::string layer_id = "";
					Util::NumToString(layerID, &layer_id);
					state_event.layerID = layer_id;
					state_event.state = state;
					eventbus::EventBus::PostEvent(state_event);
					LOG(INFO) << "[StateChangeEvent] sourceID: " << id.ToString() << ", layerID: " << layerID << ", state: " << state;

					// For StudioMode
					{
                        SOURCE_INFO sourceInfo;
                        SourceMgr::GetInstance()->GetSourceInfoByID(sourceID, &sourceInfo);
						if (sourceInfo.type == VISUAL_BYTELINK && !state)
						{
							if (Layer* pLayer = ModeSceneMgr::GetInstance()->GetLayerByID(layerID))
							{
                                LAYER_INFO layerInfo;
                                pLayer->GetLayerInfo(&layerInfo);
                                layerInfo.isStudioMode = false;
                                pLayer->SetLayerInfo(&layerInfo);
							}
						}
					}
				}
				}));
		}
		else if (output_json.contains("game_event"))
		{
			auto                    val = output_json["game_event"];
			auto                    type = (mediasdk::GameCaptureEventType)val["event"];
			bool                    ok = false;
			GAME_CAPTURE_EVENT_TYPE evt_type = MapGameCaptureEventType2Native(type, &ok);
			if (!ok)
			{
				LOG(ERROR) << "[SDKCallBackMgr::OnVisualSourceEvent] MapGameCaptureEventType2Native failed";
				return;
			}

			uint64_t                ti = val["time"];
			std::string             info = val["info"];
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, evt_type, ti, info]() {
				HandleGameStateChanged(id.ToString(), evt_type, ti, info);
			}));

			VISUAL_CAPTURE_TYPE cap_type = CAPTURE_TYPE_NONE;
			if (evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_MEM)
			{
				cap_type = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_GAME_INJECT_MEMORY;
			}
			else if (evt_type == GAME_CAPTURE_EVENT_TYPE::CAPTURE_SUCCESS_TEXTURE)
			{
				cap_type = VISUAL_CAPTURE_TYPE::CAPTURE_TYPE_GAME_INJECT_TEXTURE;
			}

			uint32_t cap_pid = val["capture_id"];
			uint64_t win_id = val["window_id"];
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, cap_type, cap_pid, win_id]() {
				UINT64 sourceID = 0;
				Util::StringToNum(id.ToString(), &sourceID);
				auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
				for (const auto& layerID : layerIDs)
				{
					std::string layer_id = "";
					Util::NumToString(layerID, &layer_id);
					VisualCaptureTypeChangeEvent cap_event{};
					cap_event.layerID = layer_id;
					cap_event.type = cap_type;
					cap_event.capPID = cap_pid;
					cap_event.capHwnd = win_id;
					eventbus::EventBus::PostEvent(cap_event);
					LOG(INFO) << "[VisualCaptureTypeChangeEvent] visualID: " << id.ToString() << ", layerID: " << layerID << ", cap_type: " << cap_type << ", cap_pid: " << cap_pid << ", win_id: " << win_id;
				}
			}));
		}
		else if (output_json.contains("castmate_event"))
		{
			auto                json_castmate = output_json["castmate_event"];
			bool                success = false;
			CASTMATE_EVENT_TYPE ev_type = MapMobileProjectorEvent2Native(json_castmate.value("event", bytelink_visual_source::CastMateEvent::kCastMateEventClose), &success);
			if (!success)
			{
				LOG(ERROR) << "[OnVisualSourceEvent] MapMobileProjectorEvent error json_castmate[\"event\"] = " << static_cast<int>(json_castmate.value("event", bytelink_visual_source::CastMateEvent::kCastMateEventClose));
			}
			CASTMATE_PROTOCOL_TYPE protocol = MapMobileProjectorProtocol2Native(json_castmate.value("type", bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWiredAndroid), &success);
			if (!success)
			{
				LOG(ERROR) << "[OnVisualSourceEvent] MapMobileProjectorProtocol error json_castmate[\"type\"] = " << static_cast<int>(json_castmate.value("type", bytelink_visual_source::CastmateProtocolType::kCastmateProtocolTypeWiredAndroid));
			}
			uint32_t    event_code = json_castmate.value("code", 0);
			std::string message = json_castmate.value("msg", "");
			CreateCastMateEvent event{};
			event.eventType = ev_type;
			event.protocolType = protocol;
			event.msg = message;
			event.eventCode = event_code;
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
				LOG(INFO) << "[CreateCastMateEvent] ev_type: " << event.eventType << ", protocolType: " << event.protocolType << ", msg: " << event.msg << ", eventCode: " << event.eventCode;
			}));
		}
		else if (output_json.contains("fav_event"))
		{
			auto           evtType = (fav_visual_source::FAVEventType)output_json["fav_event"]["event"];
			bool           success = false;
			FAV_EVENT_TYPE evt = MapMediaSourceEvent2Native(evtType, &success);
			if (!success)
			{
				assert(false && "MapMediaSourceEvent2Native fail");
				return;
			}

			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, evt]() {
				LOG(INFO) << "[OnVisualSourceEvent] sourceID: " << id.ToString() << ", evt: " << evt;

				UINT64 sourceID = 0;
				Util::StringToNum(id.ToString(), &sourceID);
				auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
				for (const auto& layerID : layerIDs)
				{
					std::string layer_id = "";
					Util::NumToString(layerID, &layer_id);

					FavStateEvent fav_event{};
					fav_event.type = evt;
					fav_event.visualID = layer_id;
					eventbus::EventBus::PostEvent(fav_event);
				}
			}));
		}
		else if (output_json.contains("event"))
		{
			auto ev = output_json["event"];
			if (ev == "graffiti_tracks_changed")
			{
				uint64_t ti = output_json["time"];
				g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, ti]() {
					UINT64 sourceID = 0;
					Util::StringToNum(id.ToString(), &sourceID);
					auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
					for (const auto& layerID : layerIDs)
					{
						std::string layer_id = "";
						Util::NumToString(layerID, &layer_id);
						TracksInfoEvent event{};
						event.layerID = layer_id;
						event.tracksInfo = std::to_string(ti);
						eventbus::EventBus::PostEvent(event);
						LOG(INFO) << "[TracksInfoEvent] visualID: " << id.ToString() << " layerID: " << event.layerID << ", tracksInfo: " << event.tracksInfo;
					}
				}));
			}
		}
		else if (output_json.contains("desktop_event"))
		{
			auto     jsonz = output_json["desktop_event"];
			int32_t  capture = jsonz["capture"];
			uint32_t cap_pid = jsonz["capture_id"];

			bool ok = false;
			VISUAL_CAPTURE_TYPE cap_type = MapWindowCaptureMethod2Native((desktop_capture_visual_source::WindowCaptureMethod)capture, &ok);
			if (!ok)
			{
				LOG(ERROR) << "[SDKCallBackMgr::OnVisualSourceEvent] MapVisualCaptureType2Native failed";
				return;
			}

			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, cap_type, cap_pid]() {
				UINT64 sourceID = 0;
				Util::StringToNum(id.ToString(), &sourceID);
				auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
				for (const auto& layerID : layerIDs)
				{
					std::string layer_id = "";
					Util::NumToString(layerID, &layer_id);
					VisualCaptureTypeChangeEvent cap_event{};
					cap_event.layerID = layer_id;
					cap_event.type = cap_type;
					cap_event.capPID = cap_pid;
					eventbus::EventBus::PostEvent(cap_event);
					LOG(INFO) << "[VisualCaptureTypeChangeEvent] visualID: " << id.ToString() << " layerID: " << cap_event.layerID << ", cap_type: " << cap_type << ", cap_pid: " << cap_pid;
				}
			}));
		}
		else if (output_json.contains("format_info"))
		{
			std::string    json2 = output_json["format_info"];
			nlohmann::json output_json2 = nlohmann::json::parse(json2);
			int32_t        color_range = output_json2["video_range_detect"];
			COLOR_RANGE    color_range_native = MapColorRange2Native(mediasdk::VideoRange(color_range), NULL);
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, color_range_native]() {
				UINT64 sourceID = 0;
				Util::StringToNum(id.ToString(), &sourceID);
				auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
				for (const auto& layerID : layerIDs)
				{
					std::string layer_id = "";
					Util::NumToString(layerID, &layer_id);
					ColorRangeDetectEvent event{};
					event.layerID = layer_id;
					event.result = color_range_native;
					eventbus::EventBus::PostEvent(event);
				}
			}));
		}
		else if (output_json.contains("event_type"))
		{
            std::string event_type = output_json["event_type"];
            CAMERA_OPEN_ERROR_TYPE err_type = CAMERA_OPEN_NO_ERROR;
			if (event_type == "do_reopen_error")
			{
                err_type = CAMERA_REOPEN_ERROR;
			}
			else if (event_type == "device_occupied_error")
			{
                err_type = CAMERA_DEVICE_OCCUPIED_ERROR;
			}
			else if (event_type == "device_unknow_error")
			{
                err_type = CAMERA_DEVICE_UNKNOW_ERROR;
			}

			INT32 err_code = output_json["error_code"];
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, err_type, err_code]() {
				UINT64 sourceID = 0;
                Util::StringToNum(id.ToString(), &sourceID);

				if (err_type == CAMERA_REOPEN_ERROR)
				{
                    SOURCE_INFO sourceInfo{};
                    SourceMgr::GetInstance()->GetSourceInfoByID(sourceID, &sourceInfo);
                    if (!SourceMgr::GetInstance()->ReopenSource(sourceID, &sourceInfo))
                        LOG(ERROR) << "[CameraOpenErrorEvent] ReopenSource failed, sourceID: " << sourceID << ", sourceType: " << sourceInfo.type;
				}
                else if (err_type == CAMERA_DEVICE_OCCUPIED_ERROR || err_type == CAMERA_DEVICE_UNKNOW_ERROR)
                {
                    auto& compositeMetas = SourceMgr::GetInstance()->GetCompositeMetas();
                    auto& iter = compositeMetas.find(sourceID);
                    if (iter != compositeMetas.end() && (iter->second.primarySourceID == sourceID))
                    {
                        if (!SourceMgr::GetInstance()->ActivateFallback(iter->second, sourceID))
                            LOG(ERROR) << "[CameraOpenErrorEvent] ActivateFallback failed, sourceID: " << sourceID << ", err_type: " << err_type;
                    }
                }

                auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
				for (const auto& layerID : layerIDs)
				{
                    std::string layer_id = "";
                    Util::NumToString(layerID, &layer_id);
                    CameraOpenErrorEvent event{};
                    event.layerID = layer_id;
                    event.errorType = err_type;
                    event.errorCode = err_code;
                    eventbus::EventBus::PostEvent(event);
                    LOG(INFO) << "[CameraOpenErrorEvent] layerID: " << layerID << ", sourceID: " << sourceID << ", errType: " << err_type << ", errCode: " << err_code;
				}
            }));
		}
		else if (output_json.contains("dshow_event"))
		{
            std::string    json2 = output_json["dshow_event"];
            nlohmann::json output_json2 = nlohmann::json::parse(json2);
            int32_t        pro = output_json2["property"];
            bool           state = output_json2["state"];
            CAMERA_CONTROL_TYPE type = MapCameraControlType2Native(pro, NULL);
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, id, type, state]() {
                UINT64 sourceID = 0;
                Util::StringToNum(id.ToString(), &sourceID);
                auto& layerIDs = SourceMgr::GetInstance()->GetLayersBySourceID(sourceID);
                for (const auto& layerID : layerIDs)
                {
                    std::string layer_id = "";
                    Util::NumToString(layerID, &layer_id);
                    CameraControlEvent event{};
                    event.layerID = layer_id;
                    event.type = type;
                    event.state = state;
                    eventbus::EventBus::PostEvent(event);
                }
			}));
		}
	}
	catch (...)
	{
		LOG(ERROR) << "parse VisualSourceEvent error!";
	}
}

void SDKCallBackMgr::OnVisualReopenResult(bool reopen_result, mediasdk::MediaSDKString id)
{
}

void SDKCallBackMgr::OnVisualDestroyedWhenAllRefsRemoved(mediasdk::MediaSDKString id)
{
	LOG(INFO) << "[OnVisualDestroyedWhenAllRefsRemoved] sourceID: " << id.ToString() << " destroyed when all refs removed";

	UINT64 sourceID = 0;
    Util::StringToNum(id.ToString(), &sourceID);
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([sourceID]() {
        SOURCE_INFO sourceInfo{};
        SourceMgr::GetInstance()->GetSourceInfoByID(sourceID, &sourceInfo);
        if (sourceInfo.type == VISUAL_GAME)
        {
            SourceMgr::GetInstance()->StopGameRetry(sourceID);
        }

		/*
		if (SourceMgr::GetInstance()->CheckSourceExist(sourceID))
		{
			LOG(INFO) << "[OnVisualDestroyedWhenAllRefsRemoved] remove logic source, sourceID: " << sourceID;
			SourceMgr::GetInstance()->SetSourceNotRealCreated(sourceID);
		}
		*/
	}));
}

// MediaSDKVirtualCameraEventObserver
void SDKCallBackMgr::OnVirtualCameraFirstFrameWrite(const mediasdk::MSSize& new_size)
{
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([this, new_size]() {
		LOG(INFO) << "[OnVirtualCameraFirstFrameWrite] new_size: [" << new_size.cx << "," << new_size.cy << "]";
        UINT64 virtualCameraLayerID = 0;
        Util::StringToNum(m_virtual_camera_id, &virtualCameraLayerID);
        HandleVisualSizeChanged(virtualCameraLayerID, 0, new_size.cx, new_size.cy);
	}));
}

// MediaSDKAudioStatusObserver
void SDKCallBackMgr::OnAudioPeak(mediasdk::MediaSDKString id, const float* peak_array, uint32_t peak_array_size)
{
}

void SDKCallBackMgr::OnAudioPeakLR(mediasdk::MediaSDKString id, const float left_dev, const float right_dev, const float left, const float right)
{
    {
        std::unique_lock<std::mutex> lock(g_mutex_);
        if (g_rtc_visual_ids.find(id.ToString()) != g_rtc_visual_ids.end())
        {
            return;
        }
    }

    std::string stdId = id.ToString();
    UINT64      now = TinyTime::Now();
    auto&       state = audio_level_states_[stdId];
    state.lastDataTime = now;

	std::vector<float>& info = audio_info_list_[stdId];
	info.clear();
	
	if (state.currentLevels.empty())
	{
	    state.currentLevels = {left, right, left_dev, right_dev};
	}
	else
	{
	    const float smoothFactor = 0.3f;
	    state.currentLevels[0] = state.currentLevels[0] * (1.0f - smoothFactor) + left * smoothFactor;
	    state.currentLevels[1] = state.currentLevels[1] * (1.0f - smoothFactor) + right * smoothFactor;

	    state.currentLevels[2] = left_dev;
	    state.currentLevels[3] = right_dev;
	}

	state.lastUpdateTime = now;
	info.push_back(state.currentLevels[0]);
	info.push_back(state.currentLevels[1]);
	info.push_back(state.currentLevels[2]);
	info.push_back(state.currentLevels[3]);

    PostAudioPeak();
}

void SDKCallBackMgr::OnAudioTrackPeak(uint32_t track_id, const float left, const float right)
{
    std::string         stdId = "trackbit_" + std::to_string(1 << (track_id - 1));
    std::vector<float>& info = audio_info_list_[stdId];
	info.clear();
    info.push_back(left);
    info.push_back(right);
    PostAudioPeak();
}

void SDKCallBackMgr::OnEchoDetectionResult(const float probability)
{
    EchoDetectionResultEvent event{};
    event.probability = probability;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

static void HandleAudioFailedStatus(const std::string& audio_id, uint32_t error_code, const std::string& msg, int32_t hresult)
{
	LOG(INFO) << "[HandleAudioFailedStatus] audioID: " << audio_id << ", errorCode: " << error_code << ", msg: " << msg; 
    UINT64 audioID = 0;
	Util::StringToNum(audio_id, &audioID);

    if (Audio* pAudio = AudioMgr::GetInstance()->GetAudioByID(audioID))
    {
        AUDIO_INFO audioInfo{};
        pAudio->GetAudioInfo(&audioInfo);
        if (audioInfo.retryCount <= 2 && error_code > 0 &&
            ((audioInfo.type == AUDIO_WAS && error_code != AUDIO_CLIENT_INIT_FIALED) ||
             (audioInfo.type == AUDIO_APP && error_code != FIRST_FRAME_CALLBACK) ||
             (audioInfo.type == AUDIO_PCM && error_code == CREATE_INVALID_LEFT_RIGHT_DATAS)))
        {
            LOG(INFO) << "[HandleAudioFailedStatus] retry for " << audioInfo.retryCount << "th time";
            for (auto& filter : audioInfo.filters)
                pAudio->RemoveFilter(&filter);
            AudioMgr::GetInstance()->DestoryAudio(pAudio);
            ::Sleep(300 * audioInfo.retryCount);
            AudioMgr::GetInstance()->CreateAudio(&audioInfo, &audioID);

            if (Audio* pNewAudio = AudioMgr::GetInstance()->GetAudioByID(audioID))
            {
                ++audioInfo.retryCount;
                AUDIO_CONTROL_INFO controlInfo{};
                controlInfo.audioInfo = audioInfo;
                UINT64 cmd = AUDIO_CONTROL_SET_ALL_SETTING | AUDIO_CONTROL_SET_AUDIO_TRACK;
                if (audioInfo.type == AUDIO_PCM)
                {
                    cmd |= AUDIO_CONTROL_UPDATE_PCM;
                }
                else if (audioInfo.type == AUDIO_WAS)
                {
                    auto wasAudio = std::get<WAS_AUDIO>(audioInfo.audio);
                    if (wasAudio.type == AUDIO_INPUT_MICROPHONE && wasAudio.isLyrax)
                    {
                        cmd |= (AUDIO_CONTROL_ENABLE_AEC | AUDIO_CONTROL_SET_AEC_REF_ID | AUDIO_CONTROL_SET_ANS_OPTION | AUDIO_CONTROL_SET_AGC_OPTION);
                    }
                }
                controlInfo.cmd = static_cast<AUDIO_CONTROL_CMD>(cmd);
                pNewAudio->ControlAudio(&controlInfo);
                for (auto& filter : audioInfo.filters)
                    pNewAudio->BindFilter(&filter);
            }
        }
        else
        {
            if (audioInfo.type == AUDIO_APP && error_code == FIRST_FRAME_CALLBACK)
            {
                audioInfo.retryCount = 1;
                if (Audio* pAppAudio = AudioMgr::GetInstance()->GetAudioByID(audioID))
                {
                    pAppAudio->SetAudioInfo(&audioInfo);
                }
                return;
            }

            AudioFailedStatusEvent event{};
            event.audioID = audio_id;
            event.errCode = error_code;
            event.errMsg = msg;
            event.audioType = audioInfo.type;
			event.hresult = hresult;
            eventbus::EventBus::PostEvent(event);
            LOG(INFO) << "[HandleAudioFailedStatus] audio failed and will not handle, audioID: " << event.audioID << ", audioType: " << event.audioType << ", errorCode: " << event.errCode << ", errorMsg: " << event.errMsg << ", hresult: " << hresult;
        }
    }
}

void SDKCallBackMgr::OnAudioEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString event)
{
	try
	{
        nlohmann::json output_json = nlohmann::json::parse(event.ToString());
        std::string    eventName = output_json.value("event_name", "");
		if (eventName == "OnPCMAudioEOF")
		{
            PCMAudioEOFEvent event{};
            event.audioID = id.ToString();
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
				LOG(INFO) << "[PCMAudioEOFEvent] audioID: " << event.audioID;
            }));
		}
		else if (eventName == "OnPCMAudioBreak")
		{
			uint32_t remind = output_json.value("Remaining", 0);
            PCMAudioBreakEvent event{};
            event.audioID = id.ToString();
            event.leftSampleCnt = remind;
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
				LOG(INFO) << "[PCMAudioEOFEvent] audioID: " << event.audioID << ", leftSampleCnt: " << event.leftSampleCnt;
            }));
		}
		else if (eventName == "CommonMetricsCallback")
		{
			std::string common_metrics = output_json.value("common_metrics_result", "");
			std::string filter_id = output_json.value("filter_id", "");

            CommonMetricsEvent event{};
            event.filterID = filter_id;
            event.mediaIDs.push_back(id.ToString());
            event.commonMetrics = common_metrics;
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
		}
		else if (eventName == "OnDeviceTransportType")
		{
			int         type = output_json["type"];
			bool        is_mic = output_json["mic"];
			bool        speaker = output_json["speaker"];
			std::string device_id = output_json["device_id"];
            float       systemCaptureVolume = output_json["volume"];
            bool        systemMute = output_json["mute"];
			bool        map_ok = false;
			auto        typez = MapDeviceTransportType2Native(type, &map_ok);
			if (!map_ok)
			{
				LOG(ERROR) << "[MediaSDKAudioStatusObserver::OnAudioEvent] MapDeviceTransportType error! type = " << type;
			}

            WASAPIDeviceInfoEvent event{};
            event.audioID = id.ToString();
            event.deviceID = device_id;
            event.mic = is_mic;
            event.speaker = speaker;
            event.type = typez;
			event.systemCaptureVolume = systemCaptureVolume;
			event.systemMute = systemMute;
            LOG(INFO) << "[MediaSDKAudioStatusObserver::OnAudioEvent] device_id = " << device_id << " type = " << type << " is_mic = " << is_mic << " speaker = " << speaker << " systemCaptureVolume = " << systemCaptureVolume << " systemMute = " << systemMute;
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
		}
        else if (eventName == "OnAudioInputMixerError")
        {
            AudioBufferMisalignmentEvent event{};
            event.audioID = id.ToString();
            event.tooEarly = output_json["too_early"];
            event.tooLate = output_json["too_late"];
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
        }
		else if (eventName == "OnAudioInputSourceFailed")
		{
			uint32_t    error_code = output_json["error_code"];
			int32_t     hresult = output_json["hresult"];
			std::string msg = output_json["error_msg"];

            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([=]() {
                HandleAudioFailedStatus(id.ToString(), error_code, msg, hresult);
            }));
		}
		else if (eventName == "AudioSourceFormat")
		{
			AudioSourceFormatEvent event{};
			event.audioID = id.ToString();
			event.format = output_json["format"];
			event.channel = output_json["channel"];
			event.sampleRate = output_json["sample_rate"];
			event.layout = output_json["layout"];
			event.blockSize = output_json["block_size"];
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
		}
		else if (eventName == "OnMicRawErrorCaptureFormat") 
		{
			LOG(INFO) << "OnMicRawErrorCaptureFormat";
			AudioSourceErrorFormatEvent event{};
			event.audioID = id.ToString();
			event.channel = output_json["channel"];
			event.bitsPerSample = output_json["bitspersample"];
			event.sampleRate = output_json["samplerate"];
			event.channelMask = output_json["channelmask"];
			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
				eventbus::EventBus::PostEvent(event);
			}));
		}
        else if (eventName == "OnDeviceVolume")
        {
            AudioSystemDeviceVolumeEvent event{};
            event.systemCaptureVolume = output_json["volume"];
            event.systemMute = output_json["mute"];
			event.audioID = id.ToString();
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
        }
        else if (eventName == "OnAudioInputSourceWarning")
        {
			AudioSourceWarningEvent event{};
			event.audioID = id.ToString();
			event.warningCode = output_json["warning_code"];
            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
        }
				else if (eventName == "LyraxAudioRawDataModeUpdated")
        {
						LOG(INFO) << "LyraxAudioRawDataModeUpdated";
						AudioLyraxRawDataResultEvent event{};
						event.audioID = id.ToString();
						event.rawDataApiOption = output_json["raw_data_api_option"];
						event.rawDataApiDecisionOption = output_json["raw_data_api_decision_option"];
						event.applyRawDataOption = output_json["apply_raw_data_option"];
						event.systemState = output_json["system_state"];
						event.applyResult = output_json["apply_result"];
						g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
								eventbus::EventBus::PostEvent(event);
						}));
        }
				else if (eventName == "OnDeviceCaptureRateCheckResult")
				{
					LOG(INFO) << "OnDeviceCaptureRateCheckResult";
					AudioDeviceCaptureRateCheckResultEvent event{};
					event.audioID = id.ToString();
					event.rawRate = output_json["raw_rate"];
					event.diffRate = output_json["diff_rate"];
					g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
						eventbus::EventBus::PostEvent(event);
					}));
				}
				else if (eventName == "AudioInputNoiseDetection")
				{
					LOG(INFO) << "AudioInputNoiseDetection";
					AudioInputNoiseDetectionResultEvent event{};
					event.noiseLevel = output_json["noise_level"];
					event.noiseRms = output_json["noise_rms"];
					g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
						eventbus::EventBus::PostEvent(event);
					}));
				}
	}
	catch (...)
	{
		LOG(ERROR) << "parse audio event error!";
	}
}

// MediaSDKStreamStatusObserver
void SDKCallBackMgr::OnStartStreamResult(mediasdk::MediaSDKString  id, mediasdk::StreamErrorCode error_code)
{
	// 启动推流过程中，成功或发生了错误，回复OnStartStreamResul
	if (error_code == mediasdk::StreamErrorCode::kStreamSuccess)
		return;

	std::unique_lock<std::mutex> lock(event_handler_mutex_);
	using namespace std::chrono;
	auto            time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event{};
	event.streamID = id.ToString();
	if (error_code == mediasdk::StreamErrorCode::kStreamCreateVideoEncoderFailed)
	{
		event.extra = "SM_OUTPUT_INVALID_VIDEO_CODEC_SM";
		event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_DISCONNECTED;
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_VIDEO_CODEC;
	}
    else if (error_code == mediasdk::StreamErrorCode::kStreamCreateAudioEncoderFailed)
    {
		event.extra = "SM_OUTPUT_INVALID_AUDIO_CODEC_SM";
		event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_DISCONNECTED;
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_AUDIO_CODEC;
    }
	else if (error_code == mediasdk::StreamErrorCode::kStreamFailed)
	{
		LOG(ERROR) << " OnStartStreamResult error !, error_code = mediasdk::StreamErrorCode::kStreamFailed id = " << id.ToString();
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamInvalidParams)
	{
		event.extra = "";
		event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_DISCONNECTED;
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_PARAMETER;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamCreateServiceFailed)
	{
		event.extra = "";
		event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_DISCONNECTED;
		event.code = STREAM_OUTPUT_CODE::OUTPUT_CONNECT_RTMPS_FAILED_CERT_VERIFY;
	}
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnStartStreamResult] get stream type error! info.stream_id = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
	}
	event.fallbackType = STREAM_TYPE::STREAM_NONE;
	
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnReconnecting(mediasdk::MediaSDKString id)
{
	//   - 启动重连回复OnReconnecting
	using namespace std::chrono;
	auto time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event{};
	event.streamID = id.ToString();
	event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_RECONNECTING;
	event.code = STREAM_OUTPUT_CODE::OUTPUT_DISCONNECTED; // OUTPUT_DISCONNECTED或OUTPUT_CONNECT_FAIL
	event.extra = "";
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnReconnecting] get stream type error! event.streamID = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
	}
	event.fallbackType = STREAM_TYPE::STREAM_NONE;

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnConnected(mediasdk::MediaSDKString id)
{
	//   - 连接成功回复(包括任何情况下的的连接成功) OnConnected
	using namespace std::chrono;
	auto time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event{};
	event.streamID = id.ToString();
	event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_CONNECTED;
	event.code = STREAM_OUTPUT_CODE::OUTPUT_SUCCESS;
	event.extra = "";
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnConnected] get stream type error! event.streamID = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
	}
	event.fallbackType = STREAM_TYPE::STREAM_NONE;

    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnFirstFrame(mediasdk::MediaSDKString id)
{
	using namespace std::chrono;
	auto time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event;
	event.streamID = id.ToString();
	event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_FIRST_FRAME;
	event.code = STREAM_OUTPUT_CODE::OUTPUT_SUCCESS;
	event.extra = "";
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnReconnecting] get stream type error! event.streamID = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
	}
	event.fallbackType = STREAM_TYPE::STREAM_NONE;
	
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnStartFallback(mediasdk::MediaSDKString id)
{
	//   - 启动Fallback回复OnStartFallback
	using namespace std::chrono;
	auto time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event;
	event.streamID = id.ToString();
	event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_FALLBACK;
	event.code = STREAM_OUTPUT_CODE::OUTPUT_SUCCESS; // todo OUTPUT_FALLBACK 上层没有对应的pb
	event.extra = "SM_OUTPUT_FALLBACK_SM";
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnStartFallback] get stream type error! event.streamID = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
	}
	event.fallbackType = STREAM_TYPE::STREAM_LIVE_BYTE_RTMP;
	
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnFallbackResult(mediasdk::MediaSDKString id, bool success)
{
	//   - Fallback结束回复OnFallbackResult
	using namespace std::chrono;
	auto time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event;
	event.streamID = id.ToString();
	if (success)
	{
		event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_FALLBACK_SUCCESS;
		event.code = STREAM_OUTPUT_CODE::OUTPUT_SUCCESS;
		event.extra = "";
		event.timeEpoch = time_epoch;
		{
			std::unique_lock<std::mutex> lock(g_mutex_);
			if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
			{
				LOG(ERROR) << "[OnFallbackResult] get stream type error! event.streamID = " << event.streamID;
				return;
			}
			g_startStreamType[event.streamID] = STREAM_TYPE::STREAM_LIVE_BYTE_RTMP;
			event.curType = g_startStreamType[event.streamID];
		}
		event.fallbackType = STREAM_TYPE::STREAM_NONE;
		
		g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
	}
	else
	{
		LOG(ERROR) << "[OnFallbackResult] error ! id = " << id.ToString();
	}
}

void SDKCallBackMgr::OnStreamStopped(mediasdk::MediaSDKString  id, mediasdk::StreamErrorCode error_code)
{
	// - 推流进行中，发生了错误，StreamService内部产生OnInprogressFatalError，处理后给业务回复OnStreamStopped，error_code为错误码非kStreamSuccess值
	// 录制发生错误，磁盘空间不足等
	using namespace std::chrono;
	auto time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event;
	event.streamID = id.ToString();
	event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_DISCONNECTED;
	event.extra = "";
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnStreamStopped] get stream type error! event.streamID = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
		g_startStreamType.erase(event.streamID);
	}
	event.fallbackType = STREAM_TYPE::STREAM_NONE;

	event.code = STREAM_OUTPUT_CODE::OUTPUT_SUCCESS;
	if (error_code == mediasdk::StreamErrorCode::kStreamSuccess)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_SUCCESS;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamCreateVideoEncoderFailed ||
		error_code == mediasdk::StreamErrorCode::kStreamCreateAudioEncoderFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_VIDEO_CODEC;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamCreateServiceFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_CONNECT_RTMPS_FAILED_CERT_VERIFY;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamConnectFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_CONNECT_FAILED;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamServiceStartFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_CONNECT_RTMPS_FAILED;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamInvalidParams)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_PARAMETER;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamShakeHandFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_STREAM;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamSourceInitializeFailed ||
		error_code == mediasdk::StreamErrorCode::kStreamPreprocessingAfterConnectedFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_DISCONNECTED;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamInvalidUrl ||
		error_code == mediasdk::StreamErrorCode::kStreamInvalidFilePath ||
		error_code == mediasdk::StreamErrorCode::kStreamCreateFileFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_BAD_PATH;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamWriteAudioFailed ||
		error_code == mediasdk::StreamErrorCode::kStreamWriteVideoFailed)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_INVALID_CALL;
	}
	else if (error_code == mediasdk::StreamErrorCode::kStreamFailed ||
		error_code == mediasdk::StreamErrorCode::kStreamServiceWriteError ||
		error_code == mediasdk::StreamErrorCode::kStreamAlreadyConnect ||
		error_code == mediasdk::StreamErrorCode::kStreamSocketConnectFailed ||
		error_code == mediasdk::StreamErrorCode::kStreamAlreadyStarted)
	{
		event.code = STREAM_OUTPUT_CODE::OUTPUT_FAILED;
	}

	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnEncodeError(mediasdk::MediaSDKString id)
{
	using namespace std::chrono;
	auto                         time_epoch = duration_cast<milliseconds>(system_clock::now().time_since_epoch()).count();

	StreamEvent event;
	event.streamID = id.ToString();
	event.action = STREAM_OUTPUT_ACTION::OUTPUT_ACTION_ENCODE_FAILED;
	event.code = STREAM_OUTPUT_CODE::OUTPUT_VIDEO_ENCODE_ERROR;
	event.extra = "";
	event.timeEpoch = time_epoch;
	{
		std::unique_lock<std::mutex> lock(g_mutex_);
		if (g_startStreamType.find(event.streamID) == g_startStreamType.end())
		{
			LOG(ERROR) << "[OnConnected] get stream type error! event.stream_id = " << event.streamID;
			return;
		}
		event.curType = g_startStreamType[event.streamID];
	}
	event.fallbackType = STREAM_TYPE::STREAM_NONE;
	
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnEncodeEvent(mediasdk::MediaSDKString id, mediasdk::MediaSDKString json_param)
{
	NotifyStreamEncodeEvent(id.ToString(), json_param.ToString());
}

void SDKCallBackMgr::OnBitrateChange(mediasdk::MediaSDKString id, uint32_t pre_bitrate_kbps, uint32_t bitrate_kbps)
{
	ABRBitrateChangeEvent event{};
    event.streamID = id.ToString();
    event.curBitrate = bitrate_kbps;
    event.prevBitrate = pre_bitrate_kbps;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnSpeedTestResult(mediasdk::MediaSDKString id, mediasdk::MediaSDKString speed_test_result)
{
	// 开播测速
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(speed_test_result.ToString());
			if (output_json.empty())
				break;

			BandwidthEvent event{};
			event.streamID = id.ToString();
			bool success = false;
			event.streamType = MapStreamType2Native(output_json["stream_type"], &success);
			if (!success)
			{
				LOG(ERROR) << "[OnSpeedTestResult] MapStreamTypeFromString error output_json[\"stream_type\"] = " << output_json["stream_type"];
			}
			event.averageTransportBitrate = output_json["average_transport_bitrate"];
			event.probRtt = output_json["prob_rtt"];
			event.probBandwidth = output_json["prob_bandwidth"];
			event.totalSends = static_cast<int32_t>(output_json["total_sends"]);
			event.totalDrops = output_json["total_drops"];
			event.totalDuration = output_json["total_duration"];
			event.totalSendDuration = output_json["total_send_duration"];
			event.result = static_cast<RTMP_UPLOAD_SPEED_TEST_RESULT>(output_json["result"]);
			event.failedReason = static_cast<RTMP_UPLOAD_SPEED_TEST_FAIL_REASON>(output_json["failed_reason"]);

			g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));

		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse speed_test_result error!";
	}
}

// MediaSDKRTCEventObserver
void SDKCallBackMgr::OnEngineStart(int error_code)
{
	EngineStartEvent event{};
    event.ret = 0;
    event.errorCode = error_code;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnEngineStop()
{
	EngineStopEvent event{};
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnJoinChannel(mediasdk::MediaSDKString room_id, mediasdk::MediaSDKString user_id, int state, mediasdk::MediaSDKString extra_info)
{
	bool is_first_time = false;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(extra_info.ToString());
			if (output_json.empty())
				break;

			int join_type = output_json["join_type"];
			is_first_time = (join_type == mediasdk::kJoinRoomTypeFirst);
            JoinChannelEvent event{};
            event.channel = room_id.ToString();
            event.uid = user_id.ToString();
            event.errorCode = static_cast<INT32>(state);
            event.firstTime = is_first_time;

            g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
                eventbus::EventBus::PostEvent(event);
            }));
		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse frame_info error!";
	}
}

void SDKCallBackMgr::OnLeaveChannel()
{
	LeaveChannelEvent event{};
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnUserJoined(mediasdk::MediaSDKString user_id, int elapsed)
{
	RemoteEnterEvent event{};
    event.uid = user_id.ToString();
    event.elapsed = elapsed;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnUserLeave(mediasdk::MediaSDKString user_id, int reason)
{
	auto elapsed = MapRemoteLeaveReason2Native(static_cast<bytertc::UserOfflineReason>(reason), nullptr);
	RemoteLeaveEvent event{};
    event.uid = user_id.ToString();
    event.elapsed = elapsed;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnLocalStreamStats(mediasdk::MediaSDKString stats)
{
	StreamStatsEvent event = MapLocalRTCControllerStreamStats("", stats.ToString());
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnRemoteStreamStats(mediasdk::MediaSDKString user_id, mediasdk::MediaSDKString stats)
{
	StreamStatsEvent event = MapRemoteRTCControllerStreamStats(user_id.ToString(), stats.ToString());
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnUserPublishStream(mediasdk::MediaSDKString user_id, int stream_index, bool is_screen, int media_stream_type) // todo rtc
{
}

void SDKCallBackMgr::OnUserUnpublishStream(mediasdk::MediaSDKString user_id, int stream_index, int media_stream_type, int reason) // todo rtc
{
}

void SDKCallBackMgr::OnNetworkQuality(mediasdk::MediaSDKString quality)
{
	auto stats_list = MapRTCControllerNetWorkStats(quality);
    NetworkStatsEvent event{};
	event.infos = stats_list;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnConnectionStateChanged(int state)
{
    ConnectionStateEvent event{};
    event.state = state;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));

	if (state == mediasdk::kConnectionStateLost)
	{
        ConnectionLostEvent event{};
        g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
            eventbus::EventBus::PostEvent(event);
        }));
	}
}

void SDKCallBackMgr::OnWarning(int warn)
{
    WarningEvent event{};
    event.warning = warn;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnError(int error)
{
    ErrorEvent event{};
    event.error = error;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnForwardStreamStateChanged(mediasdk::MediaSDKString stream_state_infos)
{
    ForwardStreamStateInfoEvent event{};
	event.infos = MapRTCControllerForwardStreamStateInfo(stream_state_infos);
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnLocalAudioPropertiesReport(mediasdk::MediaSDKString audio_properties_infos)
{
	nlohmann::json output_json = MapLocalAudioProperties(audio_properties_infos);
    LocalAudioVolumeIndicationEvent event{};
    event.jsonContent = output_json.dump();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnRemoteAudioPropertiesReport(mediasdk::MediaSDKString audio_properties_infos)
{
	nlohmann::json output_json = MapRemoteAudioProperties(audio_properties_infos);
    RemoteAudioVolumeIndicationEvent event{};
    event.jsonContent = output_json.dump();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnActiveSpeaker(mediasdk::MediaSDKString room_id, mediasdk::MediaSDKString uid)
{
    ActiveSpeakerEvent event{};
    event.uid = uid.ToString();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnAudioDeviceStateChanged(mediasdk::MediaSDKString state_info)
{
	// sdkv1没用
}

void SDKCallBackMgr::OnFirstRemoteAudioFrame(mediasdk::MediaSDKString user_id, int stream_index)
{
	RemoteFirstAudioFrameEvent event{};
    event.uid = user_id.ToString();
    event.streamIdx = stream_index;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnFirstRemoteVideoFrameDecoded(mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString frame_info)
{
	uint32_t width = 0, height = 0, rotation = 0;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(frame_info.ToString());
			if (output_json.empty())
				break;
			width = output_json["width"];
			height = output_json["height"];
			rotation = output_json["rotation"];

		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse frame_info error!";
	}

	RemoteFirstVideoFrameDecodedEvent event{};
    event.uid = user_id.ToString();
    event.streamIdx = stream_index;
    event.width = width;
    event.height = height;
    event.rotation = rotation;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnFirstRemoteVideoFrameRendered(mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString frame_info)
{
	uint32_t width = 0, height = 0, rotation = 0;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(frame_info.ToString());
			if (output_json.empty())
				break;
			width = output_json["width"];
			height = output_json["height"];
			rotation = output_json["rotation"];

		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse frame_info error!";
	}

	RemoteFirstVideoFrameRenderEvent event{};
    event.uid = user_id.ToString();
    event.streamIdx = stream_index;
    event.width = width;
    event.height = height;
    event.rotation = rotation;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnRemoteVideoSizeChanged(mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString frame_info)
{
	uint32_t width = 0, height = 0, rotation = 0;
	try
	{
		do
		{
			nlohmann::json output_json = nlohmann::json::parse(frame_info.ToString());
			if (output_json.empty())
				break;
			width = output_json["width"];
			height = output_json["height"];
			rotation = output_json["rotation"];

		} while (0);
	}
	catch (...)
	{
		LOG(ERROR) << "parse frame_info error!";
	}

	RemoteVideoSizeChangeEvent event{};
    event.uid = user_id.ToString();
    event.streamIdx = stream_index;
    event.width = width;
    event.height = height;
    event.rotation = rotation;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnSEIMessageReceived(mediasdk::MediaSDKString room_id, mediasdk::MediaSDKString user_id, int stream_index, mediasdk::MediaSDKString message)
{ // 上层没用
}

void SDKCallBackMgr::OnRoomMessageReceived(mediasdk::MediaSDKString user_id, mediasdk::MediaSDKString message)
{
	RoomMessageReceivedEvent event{};
    event.uid = user_id.ToString();
    event.msg = message.ToString();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnUserMessageReceived(mediasdk::MediaSDKString user_id, mediasdk::MediaSDKString message)
{
	UserMessageReceivedEvent event{};
    event.uid = user_id.ToString();
    event.msg = message.ToString();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnStreamMixingEvent(mediasdk::MediaSDKString task_id, int event_type, int error, int mixed_stream_type)
{
	bool                success = false;
	STREAM_MIXING_EVENT eventType = MapStreamMixingEvent2Native(event_type, &success);
    if (!success)
    {
        LOG(ERROR) << "map StreamMixingEvent error!";
        return;
    }
	StreamMixingEvent event{};
	event.taskID = task_id.ToString();
    event.eventType = eventType;
    event.error = error;
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

// MediaSDKEPEventObserver
void SDKCallBackMgr::OnDownloadModelSuccess(mediasdk::MediaSDKString request_id)
{
	json11::Json::object value;
	value["event"] = "download_model";
	value["success"] = true;
	value["request_id"] = request_id.ToString();

	EffectPlatformEvent event{};
    event.serializedJson = json11::Json(value).dump();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnDownloadModelError(mediasdk::MediaSDKString request_id, mediasdk::MediaSDKString error)
{
	json11::Json::object value;
	value["event"] = "download_model";
	value["success"] = false;
	value["error"] = error.ToString();
	value["request_id"] = request_id.ToString();

	EffectPlatformEvent event{};
    event.serializedJson = json11::Json(value).dump();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::OnDownloadModelProgress(mediasdk::MediaSDKString request_id, int progress)
{
	json11::Json::object value;
	value["event"] = "download_model_progress";
	value["success"] = true;
	value["progress"] = progress;
	value["request_id"] = request_id.ToString();

	EffectPlatformEvent event{};
    event.serializedJson = json11::Json(value).dump();
    g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}

void SDKCallBackMgr::PostAudioPeak()
{
    UINT64 now = TinyTime::Now();
    if (timestamp_ <= 0)
        timestamp_ = now;
    if (now - timestamp_ >= interval_ * 1000000L)
    {
        timestamp_ = now;
        OnAudioPeakTimeout();
    }
}

void SDKCallBackMgr::OnAudioPeakTimeout()
{
    for (auto& [audioId, state] : audio_level_states_)
    {
        if (audio_info_list_.find(audioId) == audio_info_list_.end())
        {
            UINT64 now = TinyTime::Now();
            if ((now - state.lastDataTime) > 500000000)
            {
                std::vector<float> zeros = {0.0f, 0.0f, 0.0f, 0.0f};
                audio_info_list_[audioId] = zeros;
            }
        }
    }

    AudioPeakEvent event{};
    for (const auto& [audioID, peakInfo] : audio_info_list_)
    {
		if (peakInfo.size() < 2) continue;
		
        AUDIO_PEAK_INFO info{};
        info.audioID = audioID;
        info.isTrack = (audioID.find("trackbit_") == 0);
        info.peak = {peakInfo[0], peakInfo[1]};
        if (!info.isTrack && peakInfo.size() >= 4)
            info.devicePeak = {peakInfo[2], peakInfo[3]};
        event.infos.push_back(info);
    }

	audio_info_list_.clear();
	g_cief->GetThreadMgr()->AddTaskToBackThread(new EventTask([event]() {
        eventbus::EventBus::PostEvent(event);
    }));
}
}