#pragma once

#include "LSPublicHeader.h"

class Audio
{
public:
	Audio();
	Audio(AUDIO_INFO audioInfo);
	~Audio();

	bool SyncToMedia(bool add);
	bool ControlAudio(AUDIO_CONTROL_INFO* info);
	void SetAudioInfo(const AUDIO_INFO* info);
	void GetAudioInfo(AUDIO_INFO* info, AUDIO_INFO_CMD cmd = AUDIO_INFO_NONE);

	void BindFilter(FILTER* info);
    void UnBindFilter(FILTER* info);
    void AddFilter(FILTER* info);
	void RemoveFilter(FILTER* info);

	void UpdateVISAudio(VISUAL_TYPE sourceType);

protected:
	AUDIO_INFO m_audioInfo{};
};

class CaptureAudio
{
public:
	CaptureAudio();
    CaptureAudio(CAPTURE_AUDIO_INFO captureAudioInfo);
	~CaptureAudio();

	bool SyncToAudioFrameProcessor(bool add);
	bool ControlCaptureAudio(AUDIO_CONTROL_INFO* info);
	void SetCaptureAudioInfo(const CAPTURE_AUDIO_INFO* info);
	void GetCaptureAudioInfo(CAPTURE_AUDIO_INFO* info);
	bool SetOriginAudio(UINT64 originAudioID);
	bool SetReplaceRange(UINT64 captureAudioID, INT64 beginPTSMs, INT64 endPTSMs, bool* hit, INT64* bufferBeginPTSMs, INT64* bufferEndPTSMs,
                       bool enableDump, const std::string& dumpTag, const std::string& dumpPath);
    bool EnableAutoMute(UINT64 captureAudioID, bool enable);

protected:
	CAPTURE_AUDIO_INFO m_captureAudioInfo{};
};